name: Build MacOS

on:
  push:
    branches:
      - build-macos
    paths:
      - '.github/workflows/build-macos.yml'
      - 'packages/**'
      - 'macos/**'
      - 'lib/**'
      - 'assets/**'
      - 'pubspec.yaml'

jobs:
  build:
    runs-on: macos-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Increment Build Number
        run: |
            # Determine the OS
            if [[ "$RUNNER_OS" == "Windows" ]]; then
                # Windows commands
                SET "ENV_FILE=.env.production"
                SET "BUILD_NUMBER="
                FOR /F "delims=" %%A IN (build_number.txt) DO SET "BUILD_NUMBER=%%A"
                SET /A NEW_BUILD_NUMBER=BUILD_NUMBER + 1
      
                echo %NEW_BUILD_NUMBER% > build_number.txt
                powershell -Command "(Get-Content pubspec.yaml) -replace 'version: .+', 'version: 1.0.%NEW_BUILD_NUMBER%+%NEW_BUILD_NUMBER%' | Set-Content pubspec.yaml"
      
                git config --global user.email "<EMAIL>"
                git config --global user.name "Simon Dai"
                git add build_number.txt pubspec.yaml
                git commit -m "Increment build number to %NEW_BUILD_NUMBER%"
            else
                # Linux/macOS commands
                ENV_FILE=".env.production"
                echo "Creating/updating $ENV_FILE..."
                BUILD_NUMBER=$(cat build_number.txt)
                NEW_BUILD_NUMBER=$((BUILD_NUMBER + 1))
      
                echo $NEW_BUILD_NUMBER > build_number.txt
                sed -i '' "s/^version: .*/version: 1.0.$NEW_BUILD_NUMBER+${NEW_BUILD_NUMBER}/" pubspec.yaml
      
                git config --global user.email "<EMAIL>"
                git config --global user.name "Simon Dai"
                git add build_number.txt pubspec.yaml
                git commit -m "Increment build number to $NEW_BUILD_NUMBER"
            fi
      
            # Push changes back to the repository
            git push origin HEAD:main  # Adjust the branch name as necessary
        env:
            GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  # For GitHub Actions
        

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.4' # Change to your Flutter version

      - name: Install Apple Certificate
        uses: apple-actions/import-codesign-certs@v1
        with:
          p12-file-base64: ${{ secrets.APPLE_CERTIFICATE_P12 }}
          p12-password: ${{ secrets.APPLE_PASSWORD }}

      - name: Install the provisioning profile
        run: |
          mkdir -p ~/Library/Developer/Xcode/Provisioning\ Profiles
          echo "${{ secrets.APPLE_PROVISION_PROFILE }}" | base64 --decode > ~/Library/Developer/Xcode/Provisioning\ Profiles/Github_Actions.provisionprofile

      - name: Build macOS App
        run: |
          flutter pub get
          flutter build macos -v

      - name: Sign macOS App
        run: |
          codesign --deep --force --verbose --options runtime --timestamp --sign "${{ secrets.APPLE_DEVELOPER_ID_APPLICATION }}" build/macos/Build/Products/Release/YourApp.app

          find build/macos/Build/Products/Release/YourApp.app -type d -name "*.framework" -exec codesign --force --verbose --options runtime --timestamp --sign "${{ secrets.APPLE_DEVELOPER_ID_APPLICATION }}" {} \;
          find build/macos/Build/Products/Release/YourApp.app -type f -exec codesign --force --verbose --options runtime --timestamp --sign "${{ secrets.APPLE_DEVELOPER_ID_APPLICATION }}" {} \;

          codesign --verify --deep --strict --verbose build/macos/Build/Products/Release/YourApp.app

      - name: Compress macOS App
        run: |
          ditto -c -k --sequesterRsrc --keepParent "build/macos/Build/Products/Release/YourApp.app" "YourApp.zip"

      - name: Notarize macOS App
        run: |
          xcrun notarytool store-credentials --apple-id ${{ secrets.APPLE_ID }} --password ${{ secrets.APPLE_APPLICATION_SPECIFIC_PASSWORD }} --team-id ${{ secrets.APPLE_TEAM_ID }} --validate notorization_profile
          xcrun notarytool submit --keychain-profile "notorization_profile" --progress --wait YourApp.zip

      - name: Staple Notarization Ticket
        run: |
          xcrun stapler staple "build/macos/Build/Products/Release/YourApp.app"

      - name: Install Fastlane
        run: |
          gem install fastlane

      - name: Upload to App Store Connect
        env:
          APP_STORE_CONNECT_API_KEY: ${{ secrets.APP_STORE_CONNECT_API_KEY }}  # Your API Key secret
          APP_STORE_CONNECT_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }}  # Your Issuer ID secret
        run: |
          fastlane deliver --ipa "build/macos/Build/Products/Release/YourApp.app" --skip_screenshots --skip_metadata --api_key $APP_STORE_CONNECT_API_KEY --issuer_id $APP_STORE_CONNECT_ISSUER_ID
