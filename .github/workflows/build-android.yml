name: Build Android

permissions:
    contents: write # Allow write access to repository contents

on:
    push:
        branches:
            - build-android
        paths:
            - ".github/workflows/build-android.yml"
            - "packages/**"
            - "android/**"
            - "lib/**"
            - "assets/**"
            - "pubspec.yaml"

jobs:
    build:
        runs-on: self-hosted
        env:
            AWS_API_GATEWAY_API_KEY: ${{ secrets.AWS_API_GATEWAY_API_KEY }}
            API_URL_PROD: ${{ secrets.API_URL_PROD }}
            OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
            GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
            EMBEDDING_SERVER_HOST: ${{ secrets.EMBEDDING_SERVER_HOST }}
            WEBSOCKET_HOST: ${{ secrets.WEBSOCKET_HOST }}
            DIOGENES_WEB_URL: ${{ secrets.DIOGENES_WEB_URL }}
            REPLICATE_API_TOKEN: ${{ secrets.REPLICATE_API_TOKEN }}
            GOOGLE_AI_API_KEY: ${{ secrets.GOOGLE_AI_API_KEY }}
            DEEPGRAM_API_KEY: ${{ secrets.DEEPGRAM_API_KEY }}
            LIVEKIT_SERVER_URL: ${{ secrets.LIVEKIT_SERVER_URL }}
            GOOGLE_CLOUD_IOS_API_KEY: ${{ secrets.GOOGLE_CLOUD_IOS_API_KEY }}
            STRIPE_PUBLISHABLE: ${{ secrets.STRIPE_PUBLISHABLE }}
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
              with:
                  fetch-depth: 1
                  submodules: recursive

            - name: Create packages folder (Linux/macOS)
              if: runner.os != 'Windows'
              run: |
                  if [ ! -d "packages" ]; then
                    mkdir packages
                  fi
              shell: bash

            - name: Create packages folder (Windows)
              if: runner.os == 'Windows'
              run: |
                  if (!(Test-Path -Path "packages")) {
                    New-Item -ItemType Directory -Path "packages"
                  } else {
                    Write-Output "Directory packages already exists"
                  }
              shell: powershell

            - name: Initialize and update submodules (Linux/macOS)
              if: runner.os != 'Windows'
              run: |
                  if [ ! -d "packages/maid_llm" ]; then
                    git clone --recursive https://github.com/Mobile-Artificial-Intelligence/maid_llm.git packages/maid_llm
                  else
                    echo "Directory packages/maid_llm already exists, pulling latest changes..."
                    cd packages/maid_llm
                    git pull origin main  # Replace 'main' with the correct branch if needed
                    git submodule update --init --recursive
                  fi
              shell: bash

            - name: Initialize and update submodules (Windows)
              if: runner.os == 'Windows'
              run: |
                  if (!(Test-Path -Path "packages/maid_llm")) {
                    git clone --recursive https://github.com/Mobile-Artificial-Intelligence/maid_llm.git packages/maid_llm
                  } else {
                    Write-Output "Directory packages/maid_llm already exists, pulling latest changes..."
                    Set-Location -Path packages/maid_llm
                    git pull origin main  # Replace 'main' with the correct branch if needed
                    git submodule update --init --recursive
                  }
              shell: powershell

            - name: Increment Build Number (Windows)
              if: runner.os == 'Windows'
              run: |
                  echo "Incrementing build number on Windows..."
                  $ENV_FILE = ".env.production"
                  $BUILD_NUMBER = Get-Content build_number.txt -Raw
                  $NEW_BUILD_NUMBER = [int]$BUILD_NUMBER + 1
            
                  # Save the new build number back to the file
                  $NEW_BUILD_NUMBER | Set-Content build_number.txt
            
                  # Update pubspec.yaml with the new build number
                  (Get-Content pubspec.yaml) -replace 'version: .+', "version: 1.0.$NEW_BUILD_NUMBER+$NEW_BUILD_NUMBER" | Set-Content pubspec.yaml
            
                  git config --global user.email "<EMAIL>"
                  git config --global user.name "Simon Dai"
                  git add build_number.txt pubspec.yaml
                  git commit -m "Increment build number to $NEW_BUILD_NUMBER"
            

            - name: Increment Build Number (Linux/macOS)
              if: runner.os != 'Windows'
              run: |
                  echo "Incrementing build number on Linux/macOS..."

                  # Check if pubspec.yaml exists
                  if [ ! -f "pubspec.yaml" ]; then
                      echo "pubspec.yaml not found!"
                      exit 1
                  fi

                  # Read the current build number
                  BUILD_NUMBER=$(cat build_number.txt)
                  NEW_BUILD_NUMBER=$((BUILD_NUMBER + 1))

                  # Update the build number in build_number.txt
                  echo $NEW_BUILD_NUMBER > build_number.txt

                  # Update the version in pubspec.yaml based on OS
                  if [[ "$RUNNER_OS" == "macOS" ]]; then
                      sed -i '' "s/^version: .*/version: 1.0.$NEW_BUILD_NUMBER+${NEW_BUILD_NUMBER}/" pubspec.yaml
                  else
                      sed -i "s/^version: .*/version: 1.0.$NEW_BUILD_NUMBER+${NEW_BUILD_NUMBER}/" pubspec.yaml
                  fi

                  # Configure Git and commit the changes
                  git config --global user.email "<EMAIL>"
                  git config --global user.name "Simon Dai"
                  git add build_number.txt pubspec.yaml
                  git commit -m "Increment build number to $NEW_BUILD_NUMBER"
    

            # Push changes back to the repository
            - name: Push Changes
              run: |
                  git push origin HEAD:build-android  # Adjust the branch name as necessary
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

            - name: Set up JDK 17
              uses: actions/setup-java@v2
              with:
                  java-version: "17"
                  distribution: "adopt"

            #            - name: Install Dependencies (Linux)
            #              if: runner.os == 'Linux'
            #              run: |
            #                sudo apt-get update
            #                sudo apt-get install -y cmake libvulkan-dev

            - name: Install Chocolatey (Windows)
              if: runner.os == 'Windows'
              run: |
                  Set-ExecutionPolicy Bypass -Scope Process -Force; 
                  [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; 
                  iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
              shell: powershell

            - name: Add Chocolatey to PATH (Windows)
              if: runner.os == 'Windows'
              run: |
                  $env:Path += ";C:\ProgramData\chocolatey\bin"
              shell: powershell

            # - name: Install Dependencies (Windows)
            #   if: runner.os == 'Windows'
            #   run: |
            #       choco install -y cmake
            #   shell: powershell

            - name: Install Dependencies (macOS)
              if: runner.os == 'macOS'
              run: |
                  brew install cmake

            - name: Setup Flutter
              uses: subosito/flutter-action@v2
              with:
                  flutter-version: "3.24.4"
                  channel: stable
                  cache: false
                  pub-cache-path: default
                  dry-run: false

            - name: Decode Keystore File (Linux/macOS)
              if: runner.os != 'Windows'
              env:
                  KEYSTORE: ${{ secrets.ANDROID_KEYSTORE }}
              run: |
                  echo "$KEYSTORE" | base64 --decode > android/app/key.jks

            - name: Decode Keystore File (Windows)
              if: runner.os == 'Windows'
              env:
                  KEYSTORE: ${{ secrets.ANDROID_KEYSTORE }}
              run: |
                  [System.IO.File]::WriteAllBytes("android/app/key.jks", [System.Convert]::FromBase64String("$KEYSTORE"))

            - name: Create key.properties (Linux/macOS)
              if: runner.os != 'Windows'
              run: |
                  echo "storeFile=key.jks" > android/key.properties
                  echo "storePassword=${{ secrets.ANDROID_STORE_PASSWORD }}" >> android/key.properties
                  echo "keyPassword=${{ secrets.ANDROID_KEY_PASSWORD }}" >> android/key.properties
                  echo "releasePassword=${{ secrets.ANDROID_KEY_PASSWORD }}" >> android/key.properties
                  echo "keyAlias=${{ secrets.ANDROID_KEY_ALIAS }}" >> android/key.properties
                  echo "releaseAlias=${{ secrets.ANDROID_KEY_ALIAS }}" >> android/key.properties

            - name: Create key.properties (Windows)
              if: runner.os == 'Windows'
              run: |
                  $keyPropertiesContent = @"
                  storeFile=key.jks
                  storePassword=${{ secrets.ANDROID_STORE_PASSWORD }}
                  keyPassword=${{ secrets.ANDROID_KEY_PASSWORD }}
                  releasePassword=${{ secrets.ANDROID_KEY_PASSWORD }}
                  keyAlias=${{ secrets.ANDROID_KEY_ALIAS }}
                  releaseAlias=${{ secrets.ANDROID_KEY_ALIAS }}
                  "@
                  Set-Content -Path android\key.properties -Value $keyPropertiesContent

            - name: Create/Update .env.production file (Linux/macOS)
              if: runner.os != 'Windows'
              run: |
                  ENV_FILE=".env.production"
                  echo "Creating/updating $ENV_FILE..."

                  # Check if the file exists, create it if it does not
                  if [ ! -f "$ENV_FILE" ]; then
                      touch "$ENV_FILE"
                  fi

                  declare -a ENV_VARS=(
                      "AWS_API_GATEWAY_API_KEY"
                      "API_URL_PROD"
                      "OPENAI_API_KEY"
                      "GOOGLE_MAPS_API_KEY"
                      "EMBEDDING_SERVER_HOST"
                      "WEBSOCKET_HOST"
                      "DIOGENES_WEB_URL"
                      "REPLICATE_API_TOKEN"
                      "GOOGLE_AI_API_KEY"
                      "DEEPGRAM_API_KEY"
                      "LIVEKIT_SERVER_URL"
                      "GOOGLE_CLOUD_IOS_API_KEY"
                      "STRIPE_PUBLISHABLE"
                  )

                  # Loop through the environment variables and update or append to the .env.production file
                  for VAR in "${ENV_VARS[@]}"; do
                      # Remove existing variable if it exists and then append the new variable
                      if [[ "$RUNNER_OS" == "macOS" ]]; then
                      sed -i '' "/^$VAR=/d" "$ENV_FILE"  # Remove existing variable (macOS)
                      else
                      sed -i "/^$VAR=/d" "$ENV_FILE"  # Remove existing variable (Linux)
                      fi
                      echo "$VAR=\${$VAR}" >> "$ENV_FILE"  # Append the new variable
                  done

            - name: Create/Update .env.production file (Windows)
              if: runner.os == 'Windows'
              run: |
                  $ENV_FILE=".env.production"
                  Write-Output "Creating/updating $ENV_FILE..."
                  $ENV_VARS = @(
                      "AWS_API_GATEWAY_API_KEY"
                      "API_URL_PROD"
                      "OPENAI_API_KEY"
                      "GOOGLE_MAPS_API_KEY"
                      "EMBEDDING_SERVER_HOST"
                      "WEBSOCKET_HOST"
                      "DIOGENES_WEB_URL"
                      "REPLICATE_API_TOKEN"
                      "GOOGLE_AI_API_KEY"
                      "DEEPGRAM_API_KEY"
                      "LIVEKIT_SERVER_URL"
                      "GOOGLE_CLOUD_IOS_API_KEY"
                      "STRIPE_PUBLISHABLE"
                  )

                  # Remove existing variables and add new ones
                  foreach ($VAR in $ENV_VARS) {
                      (Get-Content $ENV_FILE) | Where-Object { $_ -notmatch "^$VAR=" } | Set-Content $ENV_FILE
                      Add-Content $ENV_FILE "$VAR=`${$VAR}"
                  }

            - name: Configure Flutter
              run: |
                  flutter config --no-analytics
                  flutter pub get

            - name: Build APK
              run: |
                  flutter build apk -v --split-per-abi

            - name: Upload APK
              uses: actions/upload-artifact@v4
              with:
                  name: diogenesaichatbot-android-apk
                  path: build/app/outputs/apk/release

            - name: Build appbundle
              run: |
                  flutter build appbundle

            - name: Rename AAB
              run: mv build/app/outputs/bundle/release/app-release.aab build/app/outputs/bundle/release/diogenesaichatbot-android-bundle.aab

            - name: Upload AAB
              uses: actions/upload-artifact@v4
              with:
                  name: diogenesaichatbot-android-aab
                  path: build/app/outputs/bundle/release/diogenesaichatbot-android-bundle.aab

            # - name: Decode Google Cloud Service Account Credentials
            #   run: |
            #       echo "$GCLOUD_SERVICE_ACCOUNT_CREDENTIALS" | base64 --decode > "$CM_BUILD_DIR/gcloud_service_account.json"

            # - name: Upload AAB to Google Play
            #   uses: r0adkll/upload-google-play@v1.1.3
            #   with:
            #     serviceAccountJsonPlainText: ${{ secrets.SERVICE_ACCOUNT_JSON }}  # Replace with your GitHub secret
            #     packageName: com.diogenes.ai.chatbot  # Your app's package name
            #     releaseFiles: ./build/app/outputs/bundle/release/diogenesaichatbot-android-bundle.aab  # Path to your AAB
            #     track: production  # Specify the track (production, beta, etc.)
            #     status: completed  # You can also set this to draft or inProgress as needed
            #     releaseName: "Version 1.0.0"  # Optional: Friendly release name
            #     inAppUpdatePriority: 2  # Optional: Priority for in-app updates (0-5)
            #     userFraction: 0.33  # Optional: Percentage of users who should get the staged version
            #     # whatsNewDirectory: ./whatsnew/  # Optional: Directory for what's new files

