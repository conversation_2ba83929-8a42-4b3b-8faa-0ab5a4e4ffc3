# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on merge
"on":
  push:
    branches:
      - build_web
jobs:
  build_and_deploy:
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          channel: "stable"
          API_URL_PROD: "${{secrets.API_URL_PROD}}"
          AWS_API_GATEWAY_API_KEY: "${{secrets.AWS_API_GATEWAY_API_KEY}}"
          OPENAI_API_KEY: "${{secrets.OPENAI_API_KEY}}"
          EMBEDDING_SERVER_HOST: "${{secrets.EMBEDDING_SERVER_HOST}}"
          WEBSOCKET_HOST: "${{secrets.WEBSOCKET_HOST}}"
          GOOGLE_MAPS_API_KEY: "${{secrets.GOOGLE_MAPS_API_KEY}}"
          DIOGENES_WEB_URL: "${{secrets.DIOGENES_WEB_URL}}"
          REPLICATE_API_TOKEN: "${{secrets.REPLICATE_API_TOKEN}}"
          GOOGLE_AI_API_KEY: "${{secrets.GOOGLE_AI_API_KEY}}"
          DEEPGRAM_API_KEY: "${{secrets.DEEPGRAM_API_KEY}}"
          LIVEKIT_SERVER_URL: "${{secrets.LIVEKIT_SERVER_URL}}"
          GOOGLE_CLOUD_IOS_API_KEY: "${{secrets.GOOGLE_CLOUD_IOS_API_KEY}}"
          STRIPE_PUBLISHABLE: "${{secrets.STRIPE_PUBLISHABLE}}"
      - run: flutter pub get
      - run: flutter test
      - run: flutter build web   --dart-define=AWS_API_GATEWAY_API_KEY=$AWS_API_GATEWAY_API_KEY --dart-define=API_URL_PROD=$API_URL_PROD --dart-define=OPENAI_API_KEY=$OPENAI_API_KEY  --dart-define=EMBEDDING_SERVER_HOST=$EMBEDDING_SERVER_HOST             --dart-define=GOOGLE_MAPS_API_KEY=$GOOGLE_MAPS_API_KEY             --dart-define=WEBSOCKET_HOST=$WEBSOCKET_HOST             --dart-define=DIOGENES_WEB_URL=$DIOGENES_WEB_URL             --dart-define=REPLICATE_API_TOKEN=$REPLICATE_API_TOKEN             --dart-define=GOOGLE_AI_API_KEY=$GOOGLE_AI_API_KEY               --dart-define=DEEPGRAM_API_KEY=$DEEPGRAM_API_KEY                 --dart-define=LIVEKIT_SERVER_URL=$LIVEKIT_SERVER_URL                       --dart-define=GOOGLE_CLOUD_IOS_API_KEY=$GOOGLE_CLOUD_IOS_API_KEY                       --dart-define=STRIPE_PUBLISHABLE=$STRIPE_PUBLISHABLE                       --web-renderer auto
      - uses: FirebaseExtended/action-hosting-deploy@v0.9.0
        with:
          repoToken: "${{ secrets.GITHUB_TOKEN }}"
          firebaseServiceAccount: "${{ secrets.FIREBASE_SERVICE_ACCOUNT_DIOGENESAICHATBOT }}"
          channelId: live
          projectId: diogenesaichatbot
