# Copilot General Instructions

These instructions are intended to guide contributors and Copilot in maintaining high code quality, usability, and maintainability throughout the project.

## General Guidelines

1. **Add TODO Items When Necessary**

    - Always add TODO comments for:
        - Usage of mock data.
        - Links or buttons that do not point to meaningful content.
        - Temporary workarounds.
        - Any items that need to be done or revised later.
    - Use a consistent TODO format, e.g., `// TODO: <description>`.
    - Regularly review and address TODOs to prevent technical debt.

2. **UI/UX Hints**

    - Provide clear UI hints and guidance for users to understand how to use features.
    - Use tooltips, placeholder text, and inline help where appropriate.
    - Ensure error and success messages are user-friendly and actionable.
    - Strive for accessibility: use semantic HTML, ARIA labels, and keyboard navigation where possible.

3. **Data List/Table Display**

    - When displaying lists or tables of data, always consider user-friendliness and performance:
        - Implement features like filtering, searching, sorting, and pagination as needed.
        - Avoid loading large datasets all at once; use lazy loading or virtual scrolling if necessary.
        - Make sure the UI remains responsive and accessible.
        - Provide loading indicators and empty state messages for better user experience.

4. **Code Quality**

    - Write clean, readable, and reusable code.
    - Follow established coding standards and best practices.
    - Refactor duplicated logic into reusable functions or components.
    - **Avoid large files:**
        - Break up long files into smaller, focused modules, widgets, or classes.
        - Each file should have a clear, single responsibility.
        - Extract reusable code into shared modules/components.
        - If a file grows too large, refactor for maintainability and clarity.
    - Add comments and documentation where necessary to explain complex logic.
    - Write unit and integration tests for critical logic and components.
    - Use meaningful variable and function names.
    - Keep functions and components small and focused on a single responsibility.

5. **Design Documentation**
    - Always review relevant design documents under the `docs/` folder before starting significant new features or making major changes.
    - Keep design documents up to date:
        - Update documentation when there are critical system changes, architectural decisions, or major progress updates.
        - Add new documents or sections as needed to reflect new features or workflows.
    - Ensure documentation is clear, concise, and accessible to all contributors.

---

Adhering to these guidelines will help ensure a high-quality, maintainable, and user-friendly project.
