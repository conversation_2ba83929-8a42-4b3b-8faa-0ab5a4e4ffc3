# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Diogenes AI Chatbot is a comprehensive Flutter social platform where users can connect, chat with friends, and interact with AI. The app integrates multiple AI models, real-time communication, and various content creation tools.

## Technology Stack

- **Flutter 3.24.4**: Cross-platform mobile, web, and desktop application
- **Dart SDK 3.8.1**: Programming language
- **Firebase**: Backend services (Firestore, Auth, Storage, Analytics, Messaging, Remote Config)
- **OpenAI/Google AI**: Primary AI model integrations
- **Melos**: Monorepo management
- **Provider/Riverpod/BLoC**: State management (multiple patterns used)
- **Native AI**: Local LLM integration via maid_llm package

## Essential Development Commands

### Setup and Dependencies
```bash
# Get Flutter dependencies
flutter pub get

# Clean build artifacts
flutter clean && flutter pub get

# Run code generation
flutter packages pub run build_runner build

# Using Melos for monorepo management
melos bootstrap
melos run build_runner
```

### Development and Testing
```bash
# Run on development
flutter run --flavor development --dart-define-from-file=.env.development

# Run on production
flutter run --flavor production --dart-define-from-file=.env.production

# Analyze code
flutter analyze

# Run tests
flutter test

# Run integration tests
flutter test integration_test/
```

### Build Commands
```bash
# Build APK for Android
flutter build apk --split-per-abi

# Build App Bundle for Android
flutter build appbundle

# Build for iOS
flutter build ios

# Build for Web
flutter build web

# Build for macOS
flutter build macos

# Build for Windows
flutter build windows
```

## Architecture Overview

### Core Architecture Pattern
The project follows a feature-based modular architecture with multiple state management approaches:

- **Features**: Organized by domain (ai_tutor, offline_llm, image_generation, etc.)
- **Clean Architecture**: Each feature contains data/domain/presentation layers
- **Dependency Injection**: GetIt service locator pattern
- **Multi-platform Support**: iOS, Android, Web, macOS, Windows

### Key Directory Structure
```
lib/
├── features/           # Feature modules
│   ├── ai_tutor/      # AI tutoring functionality
│   ├── offline_llm/   # Local LLM integration
│   ├── image_generation/ # AI image generation
│   ├── billing/       # Payment and subscription
│   └── [other features]
├── services/          # Global services
├── models/            # Data models
├── providers/         # State management
├── pages/             # Legacy page structure
├── widgets/           # Reusable UI components
└── api_client/        # Generated API clients
```

### State Management Patterns
- **Provider**: Primary state management for app-wide state
- **BLoC**: Used for complex business logic (posts, comments, etc.)
- **Riverpod**: Used in newer features
- **GetX**: Used for navigation and some reactive features

### Service Layer Architecture
Key services in `lib/services/`:
- `AuthService`: Firebase authentication
- `FirebaseService`: Core Firebase initialization
- `MessagingService`: Push notifications and messaging
- `RemoteConfigService`: Feature flags and configuration
- `BillingService`: Stripe payment integration
- `TTSService`: Text-to-speech functionality

### API Integration
- **Generated Clients**: Uses Swagger/OpenAPI code generation
- **Multiple AI Providers**: OpenAI, Google AI, Anthropic, local LLMs
- **Real-time Communication**: WebSocket and Firebase Realtime Database

## Environment Configuration

The app uses environment-specific configuration:
- `.env.development`: Development environment variables
- `.env.production`: Production environment variables

Key environment variables include:
- `GOOGLE_AI_API_KEY`: Google AI integration
- `OPENAI_API_KEY`: OpenAI API access
- `STRIPE_PUBLISHABLE`: Payment processing
- `FIREBASE_*`: Firebase configuration

## Testing Strategy

### Test Structure
- Unit tests for business logic and models
- Widget tests for UI components
- Integration tests for complete workflows
- Convenient Test framework for enhanced testing

### Key Testing Commands
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/models/chat_node_test.dart

# Run integration tests
flutter test integration_test/
```

## Build and Deployment

### Android Build Process
The project uses GitHub Actions for automated builds:
- Automated version increment
- Keystore signing
- APK and AAB generation
- Environment variable injection

### Key Build Files
- `android/key.properties`: Android signing configuration
- `build_number.txt`: Version tracking
- `.github/workflows/build-android.yml`: CI/CD pipeline

### Flutter Build Configuration
- Supports multiple flavors (development/production)
- Multi-platform builds with platform-specific configurations
- Native splash screens and app icons configured

## AI Integration Architecture

### Local LLM Integration
- **maid_llm**: Submodule for local LLM inference
- **Flutter Gemma**: On-device AI models
- **Native bindings**: FFI for C++ integration

### Remote AI Services
- **OpenAI**: GPT models for chat and completion
- **Google AI**: Gemini models for multimodal tasks
- **Anthropic**: Claude models integration
- **Ollama**: Local inference server support

### LangChain Integration
Extensive use of LangChain Dart for:
- Chain of thought reasoning
- Multi-modal processing
- Vector embeddings
- RAG (Retrieval Augmented Generation)

## Development Guidelines

### Code Organization
- Follow feature-first organization
- Use dependency injection for services
- Implement proper error handling with try-catch blocks
- Use environment variables for sensitive configuration

### State Management Best Practices
- Use Provider for app-wide state
- BLoC for complex business logic with events/states
- Keep state immutable where possible
- Dispose controllers and streams properly

### Firebase Integration
- Initialize Firebase in `FirebaseService.initialize()`
- Use Firebase App Check for security
- Handle offline scenarios with local caching
- Proper error handling for network failures

## Common Development Tasks

### Adding a New Feature
1. Create feature directory in `lib/features/`
2. Implement data/domain/presentation layers
3. Add to service locator if needed
4. Register providers in main.dart
5. Add routes in `app_routes.dart`

### API Integration
1. Add Swagger/OpenAPI spec to `lib/swaggers/`
2. Generate client code with build_runner
3. Add service class in `lib/services/`
4. Configure dependency injection

### Adding Environment Variables
1. Add to `.env.development` and `.env.production`
2. Load in main.dart initialization
3. Use `dotenv.env['KEY_NAME']` to access
4. Add to CI/CD pipeline secrets if needed