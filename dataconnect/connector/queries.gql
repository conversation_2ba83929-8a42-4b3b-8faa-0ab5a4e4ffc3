# Chatbot queries

# Sessions for the current user
query ListSessionsByUser($limit: Int = 20, $offset: Int = 0)
@auth(level: USER) {
	chatSessions(
		where: { userId_expr: "auth.uid" }
		orderBy: [{ createdAt: DESC }]
		limit: $limit
		offset: $offset
	) {
		id
		title
		createdAt
		updatedAt
	}
}

# Alternative listing using the relation field on ChatUser (preferred)
query ListMySessions($limit: Int = 20, $offset: Int = 0) @auth(level: USER) {
	chatUser(key: { id_expr: "auth.uid" }) {
		id
		sessions: chatSessions_on_user(
			orderBy: [{ createdAt: DESC }]
			limit: $limit
			offset: $offset
		) {
			id
			title
			createdAt
			updatedAt
		}
	}
}

# Messages by session id
query ListMessagesBySession(
	$sessionId: UUID!
	$limit: Int = 50
	$offset: Int = 0
) @auth(level: USER) {
	chatMessages(
		where: { sessionId: { eq: $sessionId } }
		orderBy: [{ createdAt: ASC }]
		limit: $limit
		offset: $offset
	) {
		id
		role
		content
		index
		createdAt
	}
}

# Get a specific session
query GetSession($id: UUID!) @auth(level: USER) {
	chatSession(id: $id) {
		id
		title
		createdAt
		updatedAt
	}
}
