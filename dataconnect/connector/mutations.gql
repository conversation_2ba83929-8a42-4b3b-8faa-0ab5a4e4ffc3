# Chatbot mutations

# Upsert a chat user for the current auth.uid
mutation UpsertChatUser($displayName: String, $photoUrl: String)
@auth(level: USER) {
	chatUser_upsert(
		data: {
			id_expr: "auth.uid"
			displayName: $displayName
			photoUrl: $photoUrl
		}
	)
}

# Create a chat session for the current user
mutation CreateSession($title: String) @auth(level: USER) {
	chatSession_insert(data: { userId_expr: "auth.uid", title: $title })
}

# Append a message to a session (current user)
mutation AppendMessage(
	$sessionId: UUID!
	$role: String!
	$content: String!
	$index: Int
) @auth(level: USER) {
	chatMessage_insert(
		data: {
			sessionId: $sessionId
			userId_expr: "auth.uid"
			role: $role
			content: $content
			index: $index
		}
	)
}

# Update a session title (owner only)
mutation UpdateSessionTitle($sessionId: UUID!, $title: String!)
@auth(level: USER) {
	chatSession_update(key: { id: $sessionId }, data: { title: $title })
}
