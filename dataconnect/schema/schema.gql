# Chatbot domain schema for Diogenes AI Chatbot
# Core entities: <PERSON>t<PERSON>ser, ChatSession, ChatMessage

# Represents an authenticated Firebase user
# id is the Firebase Auth UID
type ChatUser @table {
	id: String! @col(name: "auth_uid")
	displayName: String
	photoUrl: String
	createdAt: Date @default(expr: "request.time")
}

# A conversation session owned by a user
# A user can have many sessions
type ChatSession @table {
	id: UUID! @default(expr: "uuidV4()")
	user: ChatUser!
	title: String
	createdAt: Date @default(expr: "request.time")
	updatedAt: Date @default(expr: "request.time")
}

# A message within a session
# Optionally associated with a user (null for assistant/system messages if desired)
# role: e.g., "user", "assistant", "system"
type ChatMessage @table {
	id: UUID! @default(expr: "uuidV4()")
	session: ChatSession!
	user: ChatUser
	role: String!
	content: String!
	index: Int
	createdAt: Date @default(expr: "request.time")
}
