# Billing System Environment Variables Template
# Copy this to .env and fill in your actual values

# Stripe Configuration
STRIPE_SECRET=sk_test_... # Replace with your Stripe secret key (test or live)
STRIPE_WEBHOOK_ENCRIPTION_SECRET=whsec_... # Webhook signing secret from Stripe Dashboard

# Price ID Mapping (get these from Stripe Dashboard after creating products/prices)
STRIPE_PRICE_CREDITS_100K=price_1234... # Price ID for 100k tokens package
STRIPE_PRICE_CREDITS_1M=price_5678... # Price ID for 1M tokens package

# Token Amount Configuration (optional overrides)
TOKENS_CREDITS_100K=100000
TOKENS_CREDITS_1M=1000000

# URL Configuration (optional - will use defaults if not set)
BILLING_PORTAL_RETURN_URL=https://your-app.com/profile
CHECKOUT_SUCCESS_URL=https://your-app.com/success
CHECKOUT_CANCEL_URL=https://your-app.com/cancel

# Production flag (affects Stripe customer matching)
PRODUCTION=false # Set to true for live mode
