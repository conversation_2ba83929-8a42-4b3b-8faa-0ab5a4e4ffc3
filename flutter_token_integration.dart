
// Add to BillingService class

/// Deduct tokens after actual API usage
Future<TokenDeductionResult> deductTokens({
  required String endpoint,
  required int actualTokens,
  String? requestId,
}) async {
  try {
    final resp = await _dio.post(
      '$kApiUrl/v1/billing/deduct-tokens',
      data: jsonEncode({
        'endpoint': endpoint,
        'actual_tokens': actualTokens,
        if (requestId != null) 'request_id': requestId,
      }),
      options: Options(headers: {'Content-Type': 'application/json'}),
    );
    dynamic data = resp.data;
    if (data is String) data = jsonDecode(data);
    return TokenDeductionResult.fromJson(data as Map<String, dynamic>);
  } catch (e, st) {
    logger.e('Failed deducting tokens: $e');
    logger.e('$st');
    rethrow;
  }
}

// Add model classes

class TokenDeductionResult {
  final bool success;
  final int tokensDeducted;
  final int remainingTokens;

  TokenDeductionResult({
    required this.success,
    required this.tokensDeducted,
    required this.remainingTokens,
  });

  factory TokenDeductionResult.fromJson(Map<String, dynamic> json) => TokenDeductionResult(
    success: (json['success'] as bool?) ?? false,
    tokensDeducted: (json['tokens_deducted'] as num?)?.toInt() ?? 0,
    remainingTokens: (json['remaining_tokens'] as num?)?.toInt() ?? 0,
  );
}

// Usage in chat controller (add after API usage tracking):

// Deduct tokens after successful API call
try {
  final billing = BillingService();
  final totalTokens = requestTokens + responseTokens;
  await billing.deductTokens(
    endpoint: 'chat',
    actualTokens: totalTokens,
    requestId: responseId,
  );
  logger.d('Deducted $totalTokens tokens for chat usage');
} catch (e) {
  logger.w('Failed to deduct tokens: $e');
  // Don't fail the main flow, but log for monitoring
}
