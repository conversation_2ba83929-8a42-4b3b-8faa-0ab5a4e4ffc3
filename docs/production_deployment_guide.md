# Production Deployment Guide

Complete guide for deploying the billing system to production.

## Prerequisites

- Stripe account (live mode)
- Backend server environment (cloud or VPS)
- Domain with SSL certificate
- Firebase project configured

## Step 1: Stripe Configuration

### Automated Setup (Recommended)

```bash
# Install stripe CLI and Python dependencies
pip install stripe

# Run the setup script
python scripts/stripe_setup.py \
  --api-key sk_live_YOUR_LIVE_KEY \
  --webhook-url https://your-backend.com/payment/webhooks \
  --live
```

### Manual Setup

1. **Create Products in Stripe Dashboard:**
   - Go to Products → Create Product
   - Name: "100k Tokens"
   - Metadata:
     - `package_id`: `credits_100k`
     - `tokens`: `100000`
   - Repeat for "1M Tokens" with `credits_1m` and `1000000`

2. **Create Prices:**
   - For each product, create a one-time price
   - 100k tokens: $10.00
   - 1M tokens: $80.00
   - Copy the price IDs (start with `price_`)

3. **Configure Webhook:**
   - Go to Developers → Webhooks
   - Add endpoint: `https://your-backend.com/payment/webhooks`
   - Events: `checkout.session.completed`, `payment_intent.succeeded`, `payment_intent.payment_failed`
   - Copy the signing secret

## Step 2: Backend Deployment

### Environment Configuration

```bash
# Copy and configure environment
cp .env.billing.template .env

# Edit .env with your values:
STRIPE_SECRET=sk_live_...
STRIPE_WEBHOOK_ENCRIPTION_SECRET=whsec_...
STRIPE_PRICE_CREDITS_100K=price_...
STRIPE_PRICE_CREDITS_1M=price_...
PRODUCTION=true
```

### Deploy with Script

```bash
# Run deployment preparation
./scripts/deploy_backend.sh
```

### Manual Deployment Steps

1. **Install Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure CORS:**
   Update `main.py` origins list with your production domains:
   ```python
   origins = [
       "https://your-app.com",
       "https://your-app.firebaseapp.com",
   ]
   ```

3. **Start Server:**
   ```bash
   # Development
   python main.py
   
   # Production (with gunicorn)
   gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app
   ```

### Docker Deployment

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8080

CMD ["python", "main.py"]
```

```bash
# Build and run
docker build -t billing-backend .
docker run -p 8080:8080 --env-file .env billing-backend
```

## Step 3: Mobile App Configuration

### Update API URL

In `lib/features/payment/data/config.dart`:
```dart
const String kApiUrl = 'https://your-backend.com';
```

### Build and Deploy

```bash
# Build for production
flutter build apk --release  # Android
flutter build ios --release  # iOS

# Deploy to app stores
```

## Step 4: Testing

### Backend Testing

```bash
# Test all endpoints
python scripts/test_billing_endpoints.py \
  --base-url https://your-backend.com \
  --token YOUR_FIREBASE_TOKEN
```

### End-to-End Testing

1. **Credit Purchase Flow:**
   - Open app → Profile → Buy Credits
   - Select package → Complete checkout
   - Verify tokens awarded in profile

2. **Subscription Flow:**
   - Open app → Profile → Manage Subscription
   - Subscribe to plan
   - Verify subscription status in profile

3. **Preflight Testing:**
   - Test chat with/without subscription
   - Test embeddings with/without tokens
   - Verify proper blocking/allowing

## Step 5: Monitoring

### Backend Logs

Monitor these events:
- Webhook processing (`checkout.session.completed`)
- Preflight decisions (allowed/denied)
- Subscription sync operations
- API errors and rate limits

### Key Metrics

- Checkout conversion rate
- Token consumption patterns
- Subscription retention
- API response times
- Error rates by endpoint

### Alerting

Set up alerts for:
- Webhook failures
- High error rates
- Stripe API issues
- Database connection problems

## Step 6: Security Checklist

- [ ] HTTPS enabled with valid SSL certificate
- [ ] Webhook signature verification working
- [ ] Firebase Auth required for all billing endpoints
- [ ] CORS properly configured for production domains
- [ ] Environment variables secured (not in code)
- [ ] Database access restricted
- [ ] Rate limiting configured
- [ ] Logging excludes sensitive data

## Step 7: Backup and Recovery

### Database Backup

```bash
# Firestore backup (if using)
gcloud firestore export gs://your-backup-bucket/firestore-backup
```

### Configuration Backup

- Store environment variables securely
- Document Stripe product/price IDs
- Keep webhook endpoint URLs recorded

## Troubleshooting

### Common Issues

1. **Webhook not receiving events:**
   - Check URL is publicly accessible
   - Verify SSL certificate
   - Check Stripe webhook logs

2. **CORS errors:**
   - Add production domain to origins list
   - Restart backend after changes

3. **Authentication failures:**
   - Verify Firebase service account key
   - Check token expiration

4. **Stripe API errors:**
   - Verify API keys are for correct mode (live/test)
   - Check rate limits
   - Validate product/price IDs

### Debug Commands

```bash
# Test webhook endpoint
curl -X POST https://your-backend.com/payment/webhooks \
  -H "Content-Type: application/json" \
  -d '{"test": true}'

# Check server health
curl https://your-backend.com/

# Test billing endpoints
python scripts/test_billing_endpoints.py --base-url https://your-backend.com --token TOKEN
```

## Rollback Plan

1. **Backend Issues:**
   - Revert to previous deployment
   - Update DNS if needed
   - Notify users of temporary issues

2. **Stripe Issues:**
   - Disable webhook temporarily
   - Switch to manual credit processing
   - Fix configuration and re-enable

3. **Mobile App Issues:**
   - Release hotfix update
   - Use feature flags to disable billing
   - Communicate with users

## Post-Deployment

1. **Monitor for 24-48 hours**
2. **Test all flows with real payments**
3. **Verify webhook processing**
4. **Check user feedback**
5. **Document any issues and resolutions**

## Support

- Backend logs: Check server logs for errors
- Stripe Dashboard: Monitor payments and webhooks
- Firebase Console: Check authentication and database
- App Store Connect/Play Console: Monitor app performance
