# Firebase Integration Design

This document describes how Firebase is used in the project and notes potential improvements.

## Current Usage

- **Authentication**: Implemented via `firebase_auth` with additional sign‑in methods such as Google, Apple, and Twitter supported through the `firebase_ui_auth` packages.
- **Firestore**: Stores chat messages, user profiles, and other dynamic data. The rules files in the root of the repository define security policies.
- **Cloud Functions**: The `functions/` directory contains TypeScript functions for server‑side logic. These functions currently depend on `firebase-admin` and `firebase-functions` v2 APIs.
- **Storage**: Media uploads are stored using `firebase_storage`.
- **Remote Config**: Feature flags are controlled by `firebase_remote_config`.

## Deployment

The `firebase.json` file configures hosting and emulator settings. Deployment is typically performed via `firebase deploy` from within the `functions` directory for Cloud Functions and from the project root for hosting.

## Improvement Ideas

1. **Type Safe Models** – Generate Firestore data models using `flutterfire` or `firestore_serializable` to reduce runtime errors.
2. **Function Testing** – Add unit tests for Cloud Functions using `firebase-functions-test` and run them in CI.
3. **Emulator Workflows** – Document how to run the project entirely with Firebase emulators for local development.
4. **Genkit Exploration** – Evaluate [Firebase Genkit](https://firebase.google.com/products/genkit) as a more scalable serverless framework for AI workflows.
