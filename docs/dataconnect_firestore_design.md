## Diogenes Chatbot Data Architecture: Firestore + Data Connect

Goal: Use Firestore and Firebase Data Connect together intentionally: place data where each shines, avoid duplication, and enable fast product iteration with reliable relational queries where needed.

### Summary
- Firestore remains the real-time, flexible document store for live chat UX, presence, notifications, and user-centric denormalized views.
- Data Connect (PostgreSQL) hosts relational domain data that benefits from integrity, joins, and consistent history (sessions/messages, with normalized relationships), and for server-side analytics.
- No blind duplication: write-once to the system of record; derive read models and caches explicitly with clear ownership.

### Ownership and Boundaries
- System of Record (SoR):
  - Chat sessions and messages: Data Connect (normalized)
  - Users’ public profile and social graph: Firestore (document-centric, real-time)
  - Conversation summaries/embeddings/AI analytics: Data Connect (for SQL queries and joins) or external vector store if needed
  - App settings, feature flags, UI state: Firestore/Remote Config

- Derived/Cache:
  - Recent messages feed, per-room counters, lastMessage preview: Firestore collections updated by Cloud Functions or client after successful Data Connect writes
  - Search indices (if any): managed separately

### Data Model (Data Connect)
Core tables via Data Connect schema:
- ChatUser(id: auth_uid, displayName, photoUrl, createdAt)
- ChatSession(id UUID, user -> ChatUser, title, createdAt, updatedAt)
- ChatMessage(id UUID, session -> ChatSession, user -> ChatUser?, role, content, index, createdAt)

Rationale:
- Sessions/messages have a natural relational model (1-to-many, optional user on messages for assistant/system).
- Data integrity: FK constraints via Data Connect ensure no orphaned messages.
- Efficient server-side queries: time ranges, pagination, per-user/session joins.

### Data Model (Firestore)
- chatRooms (room metadata, memberIds, lastActivity, tokens)
- messages (denormalized live stream for UI; keyed by chatRoomId)
- conversations (bot conversation bundles where applicable)
- profiles (user public profile)
- notification tokens under per-room	

Rationale:
- Firestore gives real-time snapshots, offline persistence, and simpler client-side UX for listing messages.
- These are derived views from Data Connect where appropriate; when we write to Data Connect, we can publish to Firestore as needed (function or client-side after confirm).

### Access Control
- Data Connect operations annotated with @auth(level: USER) and using auth.uid for ownership checks.
- Firestore rules continue to control document access for chatRooms/messages/profiles.
- Avoid storing the same authoritative field in both stores.

### Write Path (Client)
1) Client calls Data Connect mutation to create/update authoritative records (e.g., CreateSession, AppendMessage).
2) On success, client updates Firestore derived collections for immediate UI (e.g., append to messages collection and update room lastActivity).
3) On failure, no Firestore write occurs. Optionally, reconcile via background job.

Alternative:
- Use a Cloud Function triggered by Data Connect event or a client confirmation to write to Firestore to centralize derivation logic.

### Read Path
- Real-time chat UI: Firestore snapshots (messages, rooms) with filters, sort, pagination (limit/startAfter).
- History/analytics/export: Data Connect queries via generated SDK.

### Schema to Operations Mapping
- Mutations (Data Connect):
  - UpsertChatUser(username/displayName)
  - CreateSession(title)
  - AppendMessage(sessionId, role, content, index?)
  - UpdateSessionTitle(sessionId, title)
- Queries (Data Connect):
  - GetSession(id)
  - ListSessionsByUser(limit/offset/order)
  - ListMessagesBySession(sessionId, limit/offset/order)

### Avoiding Duplication
- Single source of truth:
  - Session/message content: Data Connect.
  - Firestore ‘messages’ used as a cache/stream; treat as non-authoritative and reconstructable from Data Connect.
- Do not store authoritative per-message content in both stores. If Firestore needs it for UI, include it but consider TTL or markers.

### Migration Plan (Incremental)
1) Land schema + connector operations.
2) Add Flutter repository layer using Data Connect generated SDK. Add emulator toggle.
3) Update message send flow: write to Data Connect, then write derived Firestore doc on success.
4) Backfill (optional): import recent Firestore messages to Data Connect; mark as historical.
5) Add tests and monitoring.

### Testing
- Emulator scripts for UpsertUser/CreateSession/AppendMessage/List; include auth emulation.
- Dart integration test for repository using the emulator.

### Future Enhancements
- Move embeddings/summaries into Data Connect with vector search (if required).
- Add role-based constraints and indexes for performance.


