# Offline LLM Design

Some features of the app can run completely offline using local language models. This document outlines the current structure and a plan to improve it.

## Existing Pieces

- The `packages/` directory contains source for native LLM bindings (`llama_library_flutter`, `ggml_library_flutter`, etc.).
- Generated FFI bindings live in `lib/generated_bindings_llama.dart`.
- Flutter code under `lib/features/offline_llm` exposes widgets and services to load and run the models.

## Current Limitations

- Platform support varies; iOS builds in particular may fail due to large native libraries.
- Model files are checked into version control or downloaded ad hoc, making updates inconsistent.
- There is no centralized configuration for model versions or storage paths.

## Design Goals

1. **Unified Model Loader** – Provide a single API to load models from assets, application storage, or external sources. This loader should handle updates and checksum validation.
2. **Background Downloading** – Use `flutter_downloader` or similar to fetch large models in the background with progress notifications.
3. **Platform Abstraction** – Wrap native code with a Dart API so that the same Dart interface works across Android, iOS, and desktop.
4. **Fallback Logic** – Automatically fall back to online models if the device lacks the required hardware or disk space for local inference.

## Open Questions

- Should models be compressed and unpacked at runtime to save space during download?
- How can we share model files across multiple Flutter modules if the user installs more than one app?
