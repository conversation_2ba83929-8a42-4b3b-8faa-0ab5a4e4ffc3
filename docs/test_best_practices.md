# Test Best Practices

This project uses Flutter with several types of tests. The following conventions help keep tests organized and maintainable.

## Directory Structure

- **Unit tests**: `test/unit/`
- **Integration tests**: `test/integration_test/`
- **Convenient tests**: placed alongside integration tests but rely on the `convenient_test_dev` package for richer UI flows.

## Writing Unit Tests

- Unit tests verify pure functions or classes with minimal dependencies.
- Test files end with `_test.dart` and reside under `test/unit/`.
- Use `group` to organize related tests and `expect` for assertions.

## Writing Integration Tests

- Integration tests check widget trees and navigation between pages.
- Use `IntegrationTestWidgetsFlutterBinding.ensureInitialized()` in `main()`.
- Launch the app by calling `app.main()` and use `pumpAndSettle()` before expectations.

## Writing Convenient Tests

- Convenient tests leverage the `convenient_test_dev` package with `convenientTestMain` and `tTestWidgets` helpers.
- They provide additional tooling like logging and snapshotting. Structure them similarly to integration tests.

## Running Tests

```
flutter test                # Run unit tests
flutter test integration_test   # Run integration tests (including convenient tests)
```

The scripts in `scripts/` can also be used for specific scenarios.
