# State Management Strategy

This project currently uses a mix of Provider, BLoC, and Riverpod. This document proposes a path toward a more unified approach.

## Current Situation

- Older widgets rely heavily on `Provider` and `ChangeNotifier` classes.
- Many newer features use `flutter_bloc` Cubits for business logic.
- Some experimental code uses `hooks_riverpod` and `riverpod`.

Having multiple patterns increases onboarding time and makes it harder to share utilities.

## Recommendation

1. **Adopt Riverpod** as the primary state management library. It offers testable providers and scales well for large apps.
2. **Gradual Migration** – Continue supporting existing BLoC classes but wrap them with `StateNotifierProvider` to integrate with Riverpod widgets.
3. **Code Generation** – Use `riverpod_generator` where practical to reduce boilerplate and ensure compile-time safety.
4. **Architecture Docs** – Update existing documentation and code samples to reflect the new approach.

## Advantages

- Reduced boilerplate compared to classic BLoC.
- Easier dependency injection via `ProviderContainer`.
- Works well with <PERSON><PERSON><PERSON>'s `FutureProvider` and `StreamProvider` for async data sources.

## Potential Drawbacks

- Migration effort could be significant. We should prioritize new features first and refactor older code gradually.
