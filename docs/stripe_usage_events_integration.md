# Stripe Usage Events Integration

## Current State Analysis

### ✅ What's Working
- Token deduction after API usage (just implemented)
- Preflight checks before expensive operations
- Credit awarding via webhooks after purchase
- Subscription state sync

### ❌ What's Missing
- **Stripe Usage Records** for metered billing
- Integration between token usage and Stripe subscription billing
- Overage billing for subscription users

## Where Usage Events Should Be Emitted

### Current Flow
```
1. User makes API call (chat/embedding)
2. Frontend calls preflight check
3. API call executes
4. Usage tracked in api_usages collection
5. Tokens deducted from credits_remaining_tokens
```

### Missing: Stripe Usage Records
```
6. **MISSING:** Emit usage to <PERSON>e for subscription billing
7. **MISSING:** Handle overage billing for subscribed users
```

## Implementation Plan

### 1. Backend: Add Stripe Usage Record Emission

In `app/api/billing.py`, modify the `deduct_tokens` endpoint:

```python
@router.post("/deduct-tokens")
async def deduct_tokens(req: TokenDeductionRequest, ...):
    # ... existing deduction logic ...
    
    # NEW: Emit usage to Stripe for subscribed users
    if is_subscribed and tokens_to_deduct > 0:
        await _emit_stripe_usage_record(
            user_id=user_id,
            endpoint=req.endpoint,
            tokens=tokens_to_deduct,
            request_id=req.request_id,
            db=db
        )
```

### 2. Stripe Usage Record Helper

```python
async def _emit_stripe_usage_record(
    user_id: str,
    endpoint: str,
    tokens: int,
    request_id: str,
    db: firestore.AsyncClient
):
    """Emit usage record to Stripe for metered billing."""
    try:
        # Get customer ID
        user_doc = await db.collection("users").document(user_id).get()
        if not user_doc.exists:
            return
        
        data = user_doc.to_dict() or {}
        stripe_customer_id = data.get("stripe_customer_id")
        if not stripe_customer_id:
            return
        
        # Get active subscription
        subscriptions = await asyncio.to_thread(
            stripe.Subscription.list,
            customer=stripe_customer_id,
            status="active",
            limit=1
        )
        
        if not subscriptions.data:
            return
        
        subscription = subscriptions.data[0]
        
        # Find the appropriate subscription item for this endpoint
        usage_item_id = None
        for item in subscription.items.data:
            price = item.price
            product = price.product
            
            # Check product metadata for endpoint mapping
            if isinstance(product, str):
                product_obj = await asyncio.to_thread(stripe.Product.retrieve, product)
                metadata = product_obj.metadata
            else:
                metadata = product.metadata or {}
            
            endpoint_mapping = metadata.get("endpoint_type", "")
            if _endpoint_matches(endpoint, endpoint_mapping):
                usage_item_id = item.id
                break
        
        if not usage_item_id:
            logger.w(f"No subscription item found for endpoint {endpoint}")
            return
        
        # Create usage record
        await asyncio.to_thread(
            stripe.UsageRecord.create,
            subscription_item=usage_item_id,
            quantity=tokens,
            timestamp=int(time.time()),
            action="increment",
            idempotency_key=f"{request_id}-{endpoint}-{user_id}"
        )
        
        logger.i(f"Emitted {tokens} tokens to Stripe for user {user_id}, endpoint {endpoint}")
        
    except Exception as e:
        logger.e(f"Failed to emit Stripe usage record: {e}")
        # Don't fail the main flow
```

### 3. Endpoint Mapping Helper

```python
def _endpoint_matches(endpoint: str, endpoint_type: str) -> bool:
    """Check if endpoint matches the subscription item type."""
    ep = endpoint.lower().strip()
    et = endpoint_type.lower().strip()
    
    chat_endpoints = {"chat", "achat", "chatai", "chat_completions"}
    embed_endpoints = {"embeddings", "embedding", "embed", "document_embed"}
    
    if et == "chat" and ep in chat_endpoints:
        return True
    elif et == "embeddings" and ep in embed_endpoints:
        return True
    elif et == "all":  # Universal usage item
        return True
    
    return False
```

## Stripe Product Configuration

### Products with Metadata

1. **Chat Tokens Product**
   - Name: "Chat API Usage"
   - Metadata:
     - `endpoint_type`: `chat`
     - `billing_unit`: `tokens`

2. **Embedding Tokens Product**
   - Name: "Embedding API Usage"
   - Metadata:
     - `endpoint_type`: `embeddings`
     - `billing_unit`: `tokens`

### Metered Prices

1. **Chat Tokens Price**
   - Product: Chat Tokens Product
   - Billing: Metered
   - Unit: per 1,000 tokens
   - Price: $0.002 per 1k tokens

2. **Embedding Tokens Price**
   - Product: Embedding Tokens Product
   - Billing: Metered
   - Unit: per 1,000 tokens
   - Price: $0.0004 per 1k tokens

### Subscription Plans

1. **Pro Plan**
   - Base fee: $20/month
   - Includes: 100k chat tokens, 500k embedding tokens
   - Overage: Metered prices above

2. **Team Plan**
   - Base fee: $50/month
   - Includes: 500k chat tokens, 2M embedding tokens
   - Overage: Metered prices above

## Flutter Integration

### No Changes Needed

The Flutter app already:
- Calls `deductTokens()` after API usage
- The backend will automatically emit to Stripe
- No additional frontend code required

### Optional: Usage Display

Add overage tracking to the usage display:

```dart
class UsageSummary {
  final int? remainingTokens;
  final String? planDisplay;
  final bool isSubscribed;
  final int? overageTokens;  // NEW
  final double? estimatedOverage;  // NEW
  
  // ... existing code ...
}
```

## Testing

### 1. Unit Tests

```python
def test_stripe_usage_emission():
    # Test usage record creation
    # Test endpoint mapping
    # Test error handling
```

### 2. Integration Tests

```bash
# Test complete flow
python scripts/test_stripe_usage_flow.py --token YOUR_TOKEN
```

### 3. Stripe Dashboard Verification

1. Make API calls through the app
2. Check Stripe Dashboard → Billing → Usage records
3. Verify correct quantities and timestamps

## Monitoring

### Key Metrics

- Usage records emitted vs tokens deducted
- Stripe API success/failure rates
- Overage billing accuracy
- Customer billing disputes

### Alerts

- High Stripe API error rates
- Missing usage records for subscribed users
- Unusual usage patterns

## Rollout Strategy

1. **Phase 1:** Deploy backend changes (no usage emission yet)
2. **Phase 2:** Enable for test users with feature flag
3. **Phase 3:** Monitor Stripe usage records accuracy
4. **Phase 4:** Full rollout with monitoring
5. **Phase 5:** Enable overage billing notifications

## Configuration

### Environment Variables

```bash
# Stripe configuration
STRIPE_SECRET=sk_live_...
ENABLE_STRIPE_USAGE_RECORDS=true

# Usage record settings
STRIPE_USAGE_BATCH_SIZE=100
STRIPE_USAGE_RETRY_ATTEMPTS=3
```

### Feature Flags

```python
# In deduct_tokens endpoint
if os.getenv("ENABLE_STRIPE_USAGE_RECORDS", "false").lower() == "true":
    await _emit_stripe_usage_record(...)
```

## Summary

**Current Implementation:** ✅ Token deduction after usage
**Missing Implementation:** ❌ Stripe usage record emission
**Solution:** Add `_emit_stripe_usage_record()` to `deduct_tokens` endpoint
**Impact:** Enables proper metered billing and overage charges for subscribed users
