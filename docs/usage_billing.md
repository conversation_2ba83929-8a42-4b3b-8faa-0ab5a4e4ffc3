# Design Doc: Usage-Based Billing with <PERSON>e for Diogenes AI Chatbot

## Summary

Add usage-based billing to the chatbot with:

-   Subscriptions (tiers with quotas and/or metered usage)
-   One-time credit top-ups
-   Usage events emission to Stripe per API call and by token consumption
-   Preflight enforcement to prevent overage before processing
-   Webhooks to reconcile, grant credits, and keep state in sync

### Repositories

-   Frontend/chatbot: /Users/<USER>/git/diogenesaichatbot
-   Backend/API: /Users/<USER>/git/diogeneschatbotembeddingserver
    Scope covers product/price setup, backend enforcement and metering, webhooks, UI, observability, testing, and rollout.

## Goals and Non‑Goals

### Goals

Allow users to purchase:

-   Subscription tiers with monthly quotas and/or metered overage
-   Prepaid credits via one-time purchase
-   Emit reliable, idempotent usage events to Stripe tied to token usage and endpoint calls
-   Block requests that exceed plan/credit limits before costly work
-   Keep local usage and Stripe usage reconciled
-   Provide clear UI for plans, billing, usage, and top-ups

### Non‑Goals

-   Complex corporate invoicing, taxes, or finance reporting beyond Stripe defaults
-   Marketplace or multi-seller payouts
-   On-prem/offline billing

## Key Use Cases

-   Pay-as-you-go developer buys credits to test embeddings and chat
-   Pro subscriber gets 500k tokens/month; beyond that, metered overage per token
-   Team admin views usage by endpoint, filters by date, exports CSV
-   Preflight rejects a long request when insufficient credits remain, telling user what to do

## Stripe Model

Two complementary approaches:

### Subscriptions with metered prices (Usage Records)

Products/Prices:

-   Chat Tokens (metered, USD per 1k tokens) — overage
-   Embedding Tokens (metered, USD per 1k tokens) — overage
-   Tier plan base fee (recurring flat/monthly) with included quotas (enforced locally)
-   For metered billing, create usage records against the subscription item with sum aggregation

### Credit Top-ups (one-time)

-   One-time product SKUs: e.g., 100k tokens, 1M tokens
-   On payment success, increment local credit ledger for the user
-   Usage decrements credits and does not create Stripe usage for that portion (or optionally can track with $0 price meter for analytics)
    Note: Stripe Billing Meters is a newer feature that can replace usage records with event-based meters. This doc assumes Usage Records for universal availability. TODO: Evaluate Billing Meters for lower integration overhead and better analytics if available on the account.

#### Mermaid: Credit Top-up Flow

```mermaid
flowchart LR
  U[User] --> FE[Frontend]
  FE --> API[Backend API]
  API -->|create Checkout Session| ST[Stripe]
  U -. redirect .-> ST
  ST -->|payment_succeeded| WH[Webhook]
  WH -->|+ credit_ledger, receipt| DB[DB]
  FE -->|poll confirm via session id| API
  API --> FE: show success + updated balance
```

## Pricing and Limits (Example)

-   Free: 50k tokens/mo total; no overage; hard cap
-   Pro: $20/mo includes 500k tokens; overage $0.10 per 1k tokens
-   Team: $99/mo includes 3M tokens; overage $0.06 per 1k tokens
-   Credits: $10 for 100k tokens; $80 for 1M tokens
    Quota semantics:

-   One pool of tokens across chat + embeddings (simpler), or separate pools by endpoint (more control). Recommended: separate pools for clarity.
-   Request count caps optional; prefer token-based metering.

## High-Level Architecture

-   Backend (Embedding/Chat API):
    -   AuthN/Z, Plans, Quotas, Credit Ledger
    -   Preflight enforcement per request (estimate tokens)
    -   Post-call actual usage accounting (adjusts estimate)
    -   Emit Stripe Usage Records (idempotent)
    -   Webhook handler for subscription lifecycle and checkout sessions
-   Frontend (Chatbot):
    -   Pricing & Plan selection
    -   Billing portal link
    -   Usage dashboard with filters/search/sort/pagination
    -   Top-up flow (Checkout Session)
    -   UX hints and actionable errors on limit exceeded

### Mermaid: Architecture Overview

```mermaid
flowchart TD
  U[User] --> FE[Chatbot Frontend]
  FE -->|Auth + call API| BE[Backend API]
  BE -->|Preflight: check plan/credits| QUOTA[Quota Service]
  QUOTA --> DB[(Primary DB)]
  BE -->|Finalize: emit usage| STRIPE[(Stripe Billing)]
  FE -. Billing portal / Checkout .-> STRIPE
  STRIPE -. Webhooks .-> WH[Webhook Handler]
  WH --> DB
  BE <--. background retries .-> QUEUE[(Retry Queue)]

  subgraph Backend
    BE
    QUOTA
    WH
  end
```

## Data Model (Backend)

users
id (UUID), email, stripe_customer_id, status
plans
id, name, monthly_included_chat_tokens, monthly_included_embed_tokens, features, price_ids

subscriptions
id, user_id, stripe_subscription_id, current_period_start/end, status, plan_id

subscription_items
id, subscription_id, stripe_subscription_item_id, type (chat_tokens|embed_tokens)

credit_ledger
id, user_id, delta_tokens (+/-), source (purchase|usage|adjustment), stripe_payment_intent_id, created_at

usage_counters
id, user_id, period_start, period_end, chat_tokens_used, embed_tokens_used, last_reconciled_at

usage_events
id, user_id, request_id, endpoint, estimated_tokens, actual_tokens, created_at, finalized_at, stripe_usage_record_id, idempotency_key, status (estimated|finalized)

locks/reservations (optional)
id, user_id, request_id, reserved_tokens, expires_at, status
Indexes on user_id, period, request_id, idempotency_key for speed and idempotency.

Mermaid: Data Model ER Diagram

```mermaid
erDiagram
  USERS ||--o{ SUBSCRIPTIONS : has
  USERS ||--o{ CREDIT_LEDGER : "top-ups/usage"
  USERS ||--o{ USAGE_COUNTERS : "monthly summaries"
  USERS ||--o{ USAGE_EVENTS : "per request"
  PLANS ||--o{ SUBSCRIPTIONS : offers
  SUBSCRIPTIONS ||--o{ SUBSCRIPTION_ITEMS : contains

  USERS {
    uuid id PK
    string email
    string stripe_customer_id
    string status
  }
  PLANS {
    uuid id PK
    string name
    int included_chat_tokens
    int included_embed_tokens
  }
  SUBSCRIPTIONS {
    uuid id PK
    uuid user_id FK
    string stripe_subscription_id
    datetime current_period_start
    datetime current_period_end
    string status
    uuid plan_id FK
  }
  SUBSCRIPTION_ITEMS {
    uuid id PK
    uuid subscription_id FK
    string stripe_subscription_item_id
    string type
  }
  CREDIT_LEDGER {
    uuid id PK
    uuid user_id FK
    bigint delta_tokens
    string source
    string stripe_payment_intent_id
    datetime created_at
  }
  USAGE_COUNTERS {
    uuid id PK
    uuid user_id FK
    datetime period_start
    datetime period_end
    bigint chat_tokens_used
    bigint embed_tokens_used
  }
  USAGE_EVENTS {
    uuid id PK
    uuid user_id FK
    string request_id
    string endpoint
    bigint estimated_tokens
    bigint actual_tokens
    string idempotency_key
    string status
    datetime created_at
    datetime finalized_at
  }
```

## Enforcement Strategy

### Preflight Check (before compute)

-   Input: endpoint, payload size, model, user_id
-   Estimate tokens using a tokenizer (e.g., `tiktoken` or local tokenizer) based on prompt length and model.
-   Compute user’s available capacity:
    -   Included tokens remaining this period (plan) + credit balance (if any)
    -   Apply per-endpoint caps if configured
-   If insufficient:
    -   Reject with clear error, message on how to upgrade or top up
-   Else:
    -   Option A (simple): Proceed; we’ll bill actual after call (risk: small overage window)
    -   Option B (strict): Reserve tokens (create reservation record), proceed, then finalize usage; if final > reserve, allow small buffer and reconcile; if final < reserve, release difference

### Post-Call Finalization

-   Measure actual tokens (prompt + completion for chat; input for embeddings; include retries if applicable)
-   Deduct from included tokens first; if exhausted, deduct from credits; if both exhausted but call ran:
    -   Option: allow small negative only if Pro/Team (configurable), else hard block preflight prevents this
-   Emit Stripe usage record against the proper subscription item when exceeding included quota and plan has overage; if credit-paid, no Stripe usage emission needed for that portion
-   Record usage_event and tie Stripe usage record id with idempotency key (request_id)

### Period Reset

-   On subscription period boundary (from Stripe webhook), reset included usage counters

### Mermaid: Preflight → Finalize Request Sequence

```mermaid
sequenceDiagram
  autonumber
  participant U as User/Client
  participant FE as Frontend
  participant BE as Backend API
  participant Q as Quota Service
  participant T as Tokenizer
  participant DB as DB
  participant ST as Stripe

  U->>FE: Submit request (chat/embedding)
  FE->>BE: API request (payload, user_id, request_id)
  BE->>T: Estimate tokens
  T-->>BE: estimated_tokens
  BE->>Q: Preflight(user_id, endpoint, estimated_tokens)
  Q->>DB: Read plan, usage, credits
  DB-->>Q: balances
  alt Allowed
    Q-->>BE: allowed=true, reserve(optional)
    BE->>BE: Execute model call
    BE->>T: Measure actual tokens
    T-->>BE: actual_tokens
    BE->>Q: Finalize(user_id, endpoint, actual_tokens, request_id)
    Q->>DB: Update counters & credits
    DB-->>Q: ok
    opt Overage portion
      BE->>ST: Create usage record (idempotency_key=request_id)
      ST-->>BE: usage_record_id
    end
    BE-->>FE: 200 OK + result
  else Blocked
    Q-->>BE: allowed=false, reason, remaining
    BE-->>FE: 402/429 with upgrade/top-up hints
  end
```

## Usage Emission to Stripe

For each subscription item corresponding to a metered price:
Create usage records via Stripe API:
Quantity: token count (choose base unit: e.g., 1 = 1 token or 1 = 1k tokens; recommend 1 = 1 token for precision; price configured per 1k tokens)
Timestamp: now or event timestamp
Action: increment
Idempotency key: request_id (per item) to avoid duplicates
Mapping:
chat tokens -> chat subscription item
embedding tokens -> embedding subscription item
For users on credit-only (no subscription), do not emit usage records; usage is covered by credit ledger and we rely on one-time charges.
Webhooks confirm invoice creation/finalization; we can optionally reconcile totals versus our counters for audit.

## Webhooks and Stripe Events

Handle:

### Mermaid: Subscription Lifecycle

```mermaid
stateDiagram-v2
  [*] --> trial
  trial --> active: trial_end / payment_succeeded
  trial --> canceled: user_cancel
  active --> past_due: invoice_payment_failed
  past_due --> active: payment_succeeded
  past_due --> canceled: canceled_by_system
  active --> canceled: user_cancel
  canceled --> [*]
```

checkout.session.completed
If one-time credit purchase:
Locate Lookup by checkout session metadata: user_id, credit package
Verify payment succeeded
Create credit_ledger entry (+ tokens)
If subscription purchase:
Create stripe_customer_id if new
Record subscription and its items, map item types by price metadata (e.g., price.metadata.type = "chat_tokens"|"embed_tokens")
customer.subscription.created/updated/deleted
Update subscriptions table and items; set plan_id; set period boundaries
On updated: adjust plan entitlements for next period; if canceled, schedule downgrades
invoice.finalized/payment_succeeded/payment_failed
Use for auditing and dunning flags if needed; optional notifications on payment_failed
Security:

Verify webhook signatures
Idempotent processing (event id stored)
Robust error handling and retry-safe design

Mermaid: Webhook Processing Flow

```mermaid
flowchart LR
  STRIPE[Stripe] -- checkout.session.completed --> WH[Webhook Handler]
  STRIPE -- customer.subscription.* --> WH
  STRIPE -- invoice.* --> WH
  WH -->|verify sig + dedupe| EVT[(webhook_events)]
  WH -->|credit purchase| CL[(credit_ledger)]
  WH -->|subscription upsert| SUBS[(subscriptions/items)]
  WH -->|period boundary| CNT[(usage_counters reset)]
  WH --> LOG[(audit logs)]
  WH --> ERR{errors?}
  ERR -- yes --> RETRY[Retry/Queue]
  ERR -- no --> OK[200]
```

## Backend API Changes

### Public API enforcement points (examples):

POST /v1/embeddings
POST /v1/chat/completions

### Internal/Support endpoints:

GET /v1/billing/usage (per user, paginated, filters)
POST /v1/billing/preflight
POST /v1/billing/stripe/webhook
GET /v1/billing/portal-link (creates Stripe billing portal session)
POST /v1/billing/checkout-session (credits)
GET /v1/billing/plans

### Preflight flow:

Client (frontend) may optionally call preflight to inform UX
Backend always performs server-side enforcement even if preflight was not called

### Error responses:

Include UI-friendly hints: remaining tokens, link to upgrade/top up, plan suggestions

### Logging:

Structured logs on each step; include request_id and user_id

### Exception handling:

Fail closed: if we cannot validate quotas (e.g., DB down), reject with a friendly error and next steps

### Type annotations:

Ensure API request/response models are typed (e.g., `Pydantic`) and validated

## Frontend (Chatbot) UX

### Pages:

Pricing & Plans
Clear plan comparison table with included quotas, overage rates
CTA: Start subscription; sign in required
Billing & Usage Dashboard
Filters: date range, endpoint, request status (estimated/finalized)
Search by request_id
Sort by tokens, time
Pagination
Charts: tokens over time; included vs. overage vs. credits used
Buttons: Upgrade plan, Buy credits, Open billing portal
UI hints: show remaining tokens and next reset date prominently
Buy Credits Flow
Select package (100k, 1M); create checkout session; return and show confirmation
In-Product Notifications
Warn when approaching limits (80%, 95%), with links
On block: show reasons and next steps

### TODO:

Accessibility pass for all billing pages
Empty states and skeleton loaders for usage dashboard
Export CSV on usage table

## Token Metering Details

Use consistent tokenizer across backend paths (e.g., `tiktoken` for OpenAI models)
Chat:
Tokens = prompt tokens + completion tokens
For preflight, estimate completion tokens from max_tokens or historical averages with safety margin
Embeddings:
Tokens = tokens in the input text(s)
Record per-endpoint token counts; store in usage_events

### Edge cases:

Streaming chat: completion length unknown; finalize after stream ends
Retries: count only successful call tokens; optionally include retries if compute consumed
Batch embeddings: sum across inputs; enforce per-request caps for DoS safety

## Idempotency and Concurrency

Every API call carries a request_id (server-generated if missing)
Usage event writes include idempotency_key = request_id
Stripe usage records created with idempotency key to prevent duplicates
Reservations table (optional) to reduce race conditions for concurrent requests
Use db transactions for decrementing credits and writing events
If finalization fails (e.g., Stripe outage), mark usage_event pending; background job retries

## Observability and Auditing

Logs: structured; include user_id, request_id, endpoint, estimated_tokens, actual_tokens, counters before/after

### Metrics:

Requests blocked due to quota
Total tokens used per endpoint
Credits purchased vs. consumed
Stripe API call latency/error rate

### Dashboards/Alerts:

Spike detection on usage
Stripe webhook failure alerts
Reconciliation gap alerts (Stripe invoice totals vs local totals)

### Audit trail:

credit_ledger doubles as audit
webhook_events table for deduplication and troubleshooting

## Security and Compliance

Secure storage of STRIPE_SECRET_KEY and webhook signing secret
Principle of least privilege for API keys
Do not expose internal identifiers in client; use short opaque ids
PII minimization in logs
GDPR: allow account deletion and billing data handling per Stripe guidelines

## Testing Strategy

### Unit tests:

Token estimation and measurement
Quota calculation and enforcement
Credit ledger arithmetic including edge cases
Usage event idempotency and retries

### Integration tests:

Simulated API call flow: preflight -> finalize -> Stripe usage emit (mocked)
Webhook flows: checkout.session.completed for credits and subscriptions; subscription updated
Race conditions: concurrent requests consuming remaining quota

### End-to-end (staging):

Stripe test mode with test cards
Full purchase + usage + invoice cycle

### Developer ergonomics:

Fixtures for Stripe mocks
Seed scripts for products/prices in test/staging

## Implementation Plan (Phased)

Phase 1: Foundations

Create products/prices in Stripe (dev and prod); price metadata mapping (e.g., type=chat_tokens|embed_tokens, unit=token)
Backend:
Data models and migrations
Tokenization utility
Quota service (plan entitlements + credit ledger + per-endpoint caps)
Stripe client wrapper with retries and idempotency
Webhook endpoint + verification + event idempotency
Frontend:
Pricing page (static)
Billing portal link
Phase 2: Credits

One-time credit checkout session endpoint and flow
On webhook, credit_ledger update
UI: Buy Credits page and balance display
Usage dashboard (MVP): table with filters/search/sort/pagination
Phase 3: Subscriptions + Metered Overage

Subscription checkout flow (or via portal)
Subscription items mapping
Emit usage records on finalize for overage portion
Period boundary reset via webhook
Phase 4: Enforcement + Polish

Preflight hard block + reservations (if chosen)
Alerts near limits
Charts and CSV export
Reconciliation job and alerting
Phase 5: Hardening

Load testing, chaos testing on Stripe outages
Security review, logging review
Documentation and `runbooks`

Mermaid: Rollout Plan (Gantt)

```mermaid
gantt
  dateFormat  YYYY-MM-DD
  title Rollout Plan
  section Phase 1 Foundations
    Models, Quotas, Webhooks   :done, p1, 2025-08-05, 2025-08-20
  section Phase 2 Credits
    Credit checkout + ledger   :active, p2, 2025-08-21, 2025-09-02
  section Phase 3 Subscriptions
    Metered usage + items map  :p3, 2025-09-03, 2025-09-15
  section Phase 4 Enforcement + Polish
    Preflight + alerts + UX    :p4, 2025-09-16, 2025-09-26
  section Phase 5 Hardening
    E2E test + security review :p5, 2025-09-29, 2025-10-06
```

## API Sketches (Backend)

### Public

POST /v1/billing/preflight
Request: endpoint, estimated_tokens, request_id?
Response: allowed, reason, remaining_tokens, suggestions
POST /v1/embeddings
Server-side: preflight -> compute -> finalize -> respond
POST /v1/chat/completions
Same pattern

### Billing

POST /v1/billing/checkout-session
Request: package_id (100k, 1M)
Response: url
GET /v1/billing/portal-link
Response: url
POST /v1/billing/stripe/webhook
Handles events
GET /v1/billing/usage?from=&to=&endpoint=&q=&page=&page_size=
Returns paginated usage_events and summarized counters

### Notes:

Consistent 4xx/5xx error schema with user-friendly messages and action links

## Frontend Integration Notes

Show remaining tokens on chat/embedding UI, with tooltip explaining plan reset date
If preflight denies, surface:
“You have X tokens remaining, this request needs Y”
Buttons: Upgrade plan, Buy credits, Reduce request size
Usage dashboard:
Columns: time, request_id, endpoint, estimated_tokens, actual_tokens, status
Filters: date range preset shortcuts (7d, 30d), endpoint
Search by request_id
Sort by time/usage
Pagination controls and per-page size
Export CSV button

### Billing:

“Manage Subscription” opens Stripe billing portal
“Buy Credits” opens checkout; show current credit balance

### TODO:

Add demo/educational hints about tokens and cost estimation on long prompts
Add loading and error states on all billing components

## Rollout and Migration

Create Stripe products/prices in test; validate flows end-to-end
Feature flag: enforce_billing = off initially; only record usage
Dry run: compare recorded usage with expected invoices
Turn on preflight enforcement for a small cohort
Announce changes, update terms
Monitor metrics and iterate

## Risks and Mitigations

Token estimation inaccuracies
Mitigate with conservative over-estimation and post-call reconciliation
Stripe outage during finalize
Queue/retry usage emissions; do not block user response; reconcile later
Double-charging via duplicate events
Idempotency keys everywhere; webhook event deduplication
Race conditions when multiple requests arrive
DB transactions + optional reservations; limits per-second rate
User confusion over credits vs subscriptions
Clear UI copy, combined balance view, “Which is right for me?” helper

## Configuration and Environment

### Backend env vars:

STRIPE*SECRET_KEY
STRIPE_WEBHOOK_SECRET
STRIPE_PRICE_IDS*{PLAN} (base fees)
STRIPE*PRICE_ID_CHAT_TOKENS_METERED
STRIPE_PRICE_ID_EMBED_TOKENS_METERED
STRIPE_PRICE_ID_CREDITS*{PACKAGE}
BILLING_ENFORCEMENT=true|false
BILLING_USAGE_UNIT=tokens|kilotokens (must match price unit)

### Frontend env vars:

NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY (if applicable)
Backend API base URL

## Documentation and Runbooks

### Developer docs:

How to run in test mode
How to seed products/prices
How to test webhooks locally (stripe CLI)

### Support runbook:

How to grant manual credits
How to reconcile a user’s usage vs invoice
Handling disputed charges and refunds

### Admin tooling:

Minimal UI to view user balances and usage logs; filters, search, sort, pagination

## Open Questions / TODOs

Decide single token pool vs per-endpoint pools (recommended: per-endpoint)
Choose strict reservations vs simple optimistic finalize (start with optimistic; add reservations if needed)
Evaluate Stripe Billing Meters vs Usage Records (TODO)
Finalize pricing and unit granularity (token vs 1k tokens)
Determine how to meter other potential resources (files stored, RAG queries, etc.)
Data retention policy for usage_events
Localization for billing UI and emails

## Next Steps

Confirm pricing, pools, and units
Provision Stripe products/prices in test; add price metadata for mapping
Implement backend:
Models/migrations
Tokenization utility
Quota service and preflight/finalize hooks
Stripe client wrapper and webhook handler
Implement frontend billing pages and usage dashboard (with filters/search/sort/pagination)
Add unit/integration tests; run end-to-end in Stripe test mode
Roll out behind feature flag and monitor
