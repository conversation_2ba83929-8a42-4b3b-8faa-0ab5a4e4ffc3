# Usage Event Integration Guide

## Current State Analysis

✅ **What's Working:**
- Preflight checks before API calls (chat requires subscription, embeddings require tokens)
- Token estimation using Util_Token.countTokens()
- Usage tracking in Firestore (api_usages collection)
- Credit awarding via Stripe webhooks
- Usage display in profile

❌ **What's Missing:**
- Actual token deduction after API usage
- Integration between usage tracking and billing system
- Token consumption from credits_remaining_tokens

## Implementation Plan

### 1. Backend Token Deduction

Add POST /v1/billing/deduct-tokens endpoint:
- Called after successful API operations
- Deducts actual tokens based on policy B
- Logs deduction for audit trail

### 2. Flutter Integration

Update chat controllers to call deduction after usage:
- ChatAIController: after handleEventData
- ChatBotController: after response completion
- AI Tutor services: after content generation

### 3. Usage Flow

```
1. User initiates chat/embedding request
2. Frontend calls preflight check
3. If allowed, proceed with API call
4. Track usage in api_usages collection (existing)
5. **NEW:** Deduct actual tokens from credits_remaining_tokens
6. Update UI with new token balance
```

### 4. Error Handling

- Deduction failures don't break main flow
- Log all deduction attempts for monitoring
- Graceful degradation if billing service unavailable

## Code Changes Required

### Backend (app/api/billing.py)
- Add TokenDeductionRequest model
- Add deduct_tokens endpoint
- Add token_usage_log collection for audit

### Frontend (lib/services/billing_service.dart)
- Add deductTokens method
- Add TokenDeductionResult model

### Controllers
- lib/controllers/chat_ai_controller.dart
- lib/controllers/chat_bot_controller.dart
- lib/features/ai_tutor/domain/services/ai_content_service.dart

## Testing

1. **Unit Tests:**
   - Test deduction with/without subscription
   - Test different endpoint types
   - Test error scenarios

2. **Integration Tests:**
   - Complete chat flow with token deduction
   - Embedding flow with token deduction
   - Verify balance updates in UI

3. **Load Tests:**
   - High-frequency deduction calls
   - Concurrent user scenarios
   - Database performance under load

## Monitoring

Track these metrics:
- Token deduction success/failure rates
- Deduction latency
- Balance accuracy (credits awarded vs deducted)
- User complaints about incorrect balances

## Rollout Strategy

1. **Phase 1:** Deploy backend endpoint (no callers yet)
2. **Phase 2:** Add Flutter integration (feature flag controlled)
3. **Phase 3:** Enable for subset of users
4. **Phase 4:** Full rollout with monitoring
5. **Phase 5:** Remove old usage tracking if redundant
