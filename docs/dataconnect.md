## Firebase Data Connect: Local Setup and Test

This repo is already configured for Firebase Data Connect.

-   Config
    -   firebase.json: dataconnect source (dataconnect) and emulator at port 9399
    -   dataconnect/dataconnect.yaml: service definition, schema, connector
    -   dataconnect/connector: operations (mutations.gql, queries.gql)
    -   dataconnect-generated/dart/default_connector: generated Dart SDK

### Prerequisites

-   Firebase CLI (firebase-tools) installed and authenticated
-   curl installed
-   Optional: jq for pretty printing JSON

### Run the local smoke test

We provide scripts that bring up the Data Connect and Auth emulators and execute mutations/queries.

-   Quick smoke test: scripts/test_dataconnect_local.sh
    -   UpsertUser using anonymous Auth emulator token (Authorization: Bearer)
-   Full coverage test: test/dataconnect_emulator_test.sh
    -   UpsertUser, CreateMovie, GetMovieById, ListMovies

Usage

-   bash scripts/test_dataconnect_local.sh
-   bash test/dataconnect_emulator_test.sh

Expected output

-   For smoke test: JSON with data.user_upsert.id
-   For full test: JSON outputs and a final [TEST] Success line

Notes

-   The provided example operations in dataconnect/connector require authenticated users. The CreateMovie mutation is declared with @auth(level: USER_EMAIL_VERIFIED). The emulator often accepts requests without tokens for quick local iteration; if you see an auth error, see the Auth Emulator section below to mint an ID token and pass it with Authorization: Bearer.
-   TODO: Add automated test coverage for queries and other mutations once we finalize the schema.

### GraphQL endpoint shape

For emulator:

-   http://localhost:9399/v1/projects/<projectId>/locations/<region>/services/<serviceId>:executeGraphql?connector=<connectorId>

This script constructs the exact endpoint based on project, region, service, and connector in this repo.

### Using the generated Dart SDK (optional)

Minimal example to call the emulator from Dart/Flutter:

-   Add to your app init code:

<augment_code_snippet path="dataconnect-generated/dart/default_connector/README.md" mode="EXCERPT">

```dart
String host = 'localhost';
int port = 9399;
DefaultConnector.instance.dataConnect.useDataConnectEmulator(host, port);
```

</augment_code_snippet>

-   Then call a mutation:

<augment_code_snippet path="dataconnect-generated/dart/default_connector/README.md" mode="EXCERPT">

```dart
final res = await DefaultConnector.instance.createMovie(
  title: 'Inception',
  genre: 'Sci-Fi',
  imageUrl: 'https://example.com',
).execute();
print(res.data.movie_insert.id);
```

</augment_code_snippet>

UI hint

-   If you add UI around this, surface errors and include a toggle to "Use Data Connect Emulator" with host/port fields. Default to localhost:9399 in debug.

### Auth Emulator (if needed)

To satisfy @auth(level: USER or USER_EMAIL_VERIFIED) locally:

1. Start both dataconnect and auth emulators:

-   firebase emulators:start --only dataconnect,auth

2. Create a test user and mark emailVerified true via the Auth emulator REST API. Then pass the returned idToken to the Data Connect call as an Authorization: Bearer header.

References

-   https://firebase.google.com/docs/emulator-suite
-   https://firebase.google.com/docs/data-connect
