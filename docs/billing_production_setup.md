# Billing System Production Setup

This guide walks through setting up the complete billing system for production.

## 1. Stripe Configuration

### Create Products with Metadata

In your Stripe Dashboard, create Products with the following metadata:

**Product 1: 100k Tokens**
- Name: "100k Tokens"
- Metadata:
  - `package_id`: `credits_100k`
  - `tokens`: `100000`

**Product 2: 1M Tokens**
- Name: "1M Tokens"  
- Metadata:
  - `package_id`: `credits_1m`
  - `tokens`: `1000000`

### Create Prices

For each product, create a Price:
- Currency: USD
- Amount: $10.00 (for 100k), $80.00 (for 1M)
- Billing: One-time
- Copy the Price ID (starts with `price_`)

### Configure Webhooks

Add webhook endpoint: `https://your-backend.com/payment/webhooks`

Events to listen for:
- `checkout.session.completed`
- `payment_intent.succeeded` (legacy)
- `payment_intent.payment_failed`

Copy the webhook signing secret.

## 2. Backend Environment Variables

Set these in your backend environment:

```bash
# Stripe Configuration
STRIPE_SECRET=sk_live_... # or sk_test_... for testing
STRIPE_WEBHOOK_ENCRIPTION_SECRET=whsec_...

# Price ID Mapping (from Stripe Dashboard)
STRIPE_PRICE_CREDITS_100K=price_1234...
STRIPE_PRICE_CREDITS_1M=price_5678...

# Token Amount Overrides (optional)
TOKENS_CREDITS_100K=100000
TOKENS_CREDITS_1M=1000000

# Portal/Checkout URLs (optional)
BILLING_PORTAL_RETURN_URL=https://your-app.com/profile
CHECKOUT_SUCCESS_URL=https://your-app.com/success
CHECKOUT_CANCEL_URL=https://your-app.com/cancel
```

## 3. Firebase Firestore Schema

Ensure your `users/{uid}` documents support these fields:

```json
{
  "credits_remaining_tokens": 0,
  "is_subscribed": false,
  "plan_display": null,
  "balance": 0.0
}
```

## 4. Mobile App Configuration

### Enable Post-Checkout Sync

In `lib/pages/user_profile_page.dart`, uncomment the sync call:

```dart
} else {
  // Sync subscription state after successful checkout
  try {
    await BillingService().syncSubscription();
    // Optionally refresh usage display
  } catch (e) {
    // Handle sync error silently or show toast
  }
}
```

### Add Usage Display

Add to your profile page to show current state:

```dart
FutureBuilder<UsageSummary>(
  future: BillingService().getUsageSummary(),
  builder: (context, snapshot) {
    if (snapshot.hasData) {
      final usage = snapshot.data!;
      return Column(
        children: [
          Text('Tokens: ${usage.remainingTokens ?? 0}'),
          if (usage.isSubscribed) 
            Text('Plan: ${usage.planDisplay ?? "Active"}'),
        ],
      );
    }
    return CircularProgressIndicator();
  },
)
```

## 5. Testing Flow

### Test Mode Setup
1. Use Stripe test keys (sk_test_...)
2. Create test products/prices with same metadata structure
3. Use test webhook endpoint

### Test Cards
- Success: `4242 4242 4242 4242`
- Decline: `4000 0000 0000 0002`
- Any future expiry, any CVC

### Verification Steps
1. Complete checkout flow
2. Check webhook logs for `checkout.session.completed`
3. Verify `credits_remaining_tokens` incremented in Firestore
4. Call `/v1/billing/usage` to confirm state
5. Test preflight with chat/embedding endpoints

## 6. Monitoring & Observability

### Backend Logs to Monitor
- Webhook processing success/failure
- Preflight decisions (allowed/denied)
- Subscription sync operations
- Credit package loading from Stripe

### Key Metrics
- Checkout conversion rate
- Token consumption patterns
- Subscription retention
- API error rates

## 7. Security Considerations

- Webhook signature verification (already implemented)
- Firebase Auth required for all billing endpoints
- CORS properly configured for your domains
- Rate limiting on billing endpoints (recommended)

## 8. Troubleshooting

### Common Issues
- **Packages not loading**: Check Stripe API keys and product metadata
- **Checkout not working**: Verify price IDs in environment variables
- **Credits not awarded**: Check webhook endpoint and signing secret
- **Preflight blocking**: Verify user subscription/token state in Firestore

### Debug Endpoints
- `GET /v1/billing/usage` - Check user state
- `POST /v1/billing/sync-subscription` - Force subscription refresh
- Backend logs for webhook processing

## 9. Production Checklist

- [ ] Stripe products created with correct metadata
- [ ] Prices created and IDs configured in backend
- [ ] Webhook endpoint configured and tested
- [ ] Environment variables set
- [ ] Firebase schema supports billing fields
- [ ] Mobile app sync enabled
- [ ] Test flow completed end-to-end
- [ ] Monitoring/logging configured
- [ ] Error handling tested
