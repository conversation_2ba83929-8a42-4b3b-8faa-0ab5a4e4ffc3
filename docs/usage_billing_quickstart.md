# Billing UI Quickstart (Profile-integrated)

This quickstart explains how to use the profile-integrated billing UI in the chat app.

## What’s included

-   Manage Subscription button (opens Stripe Billing Portal)
-   Buy Credits flow using Stripe Checkout (select a package, then Buy)
-   iOS/macOS: legacy Add Funds button for in-app purchase
-   Server-side preflight before expensive chat requests (hard block with friendly reason)

## Where to find it

-   Go to your user profile page
-   In the Wallet Balance section:
    -   iOS/macOS: tap Add Funds (in-app purchase)
    -   Other platforms: use Manage Subscription, then choose a credit package from the dropdown and click Buy

## Packages (example)

-   100k tokens — $10
-   1M tokens — $80

Note: Prices and package IDs are placeholders. The app includes TODOs to fetch from backend and display dynamically.

## Testing in Stripe Test Mode

1. Backend prerequisites

    - Implement endpoints in the embedding server:
        - POST /v1/billing/checkout-session -> { url }
        - GET /v1/billing/portal-link -> { url }
        - POST /v1/billing/preflight -> { allowed, reason, remaining_tokens }
    - Enable CORS and Firebase auth
    - Configure Stripe test products/prices
    - Webhook: checkout.session.completed updates credit ledger

2. Frontend

    - Run the app
    - Open your profile -> Wallet Balance
    - Choose a package and click Buy (non-iOS/macOS)
    - Or open Manage Subscription

3. Stripe test cards

    - Use 4242 4242 4242 4242 (any CVV, any future expiry)

4. Verify
    - After successful checkout, the backend webhook should update the user credit ledger
    - Refresh profile to verify new balance (or add a temporary toast/log while backend finishes wiring)

## Backend API Reference

### GET /v1/billing/credit-packages

Returns available credit packages dynamically loaded from Stripe Prices.

**Response:**

```json
{
	"packages": [
		{
			"id": "credits_100k",
			"label": "100k tokens",
			"price_display": "$10",
			"tokens": 100000,
			"stripe_price_id": "price_1234..."
		}
	]
}
```

### POST /v1/billing/checkout-session

Creates a Stripe checkout session for purchasing credits.

**Request:**

```json
{
	"package_id": "credits_100k",
	"quantity": 1,
	"success_url": "https://app.example.com/success",
	"cancel_url": "https://app.example.com/cancel"
}
```

### GET /v1/billing/portal-link

Creates a Stripe billing portal session.

**Query params:** `return_url` (optional)

### POST /v1/billing/sync-subscription

Syncs subscription state from Stripe to Firestore.

**Response:**

```json
{
	"is_subscribed": true,
	"plan_display": "Pro Plan"
}
```

### GET /v1/billing/usage

Returns current usage and subscription state.

**Response:**

```json
{
	"remaining_tokens": 50000,
	"plan_display": "Pro Plan",
	"is_subscribed": true
}
```

### POST /v1/billing/preflight

Checks if user can perform action based on Policy B:

-   Chat endpoints require active subscription
-   Embedding endpoints require sufficient tokens

**Request:**

```json
{
	"endpoint": "chat",
	"estimated_tokens": 1000
}
```

## Client Integration Flow

1. **Profile Load**: Call GET /v1/billing/usage to display tokens/plan
2. **After Portal/Checkout**: Call POST /v1/billing/sync-subscription then refresh usage
3. **Before Expensive Operations**: Call POST /v1/billing/preflight

## Production Setup

For production deployment, see comprehensive guides:

-   `docs/billing_production_setup.md` - Complete setup instructions
-   `docs/production_deployment_guide.md` - Step-by-step deployment
-   `docs/deployment_checklist.md` - Pre/post deployment checklist

### Quick Setup Scripts

```bash
# 1. Configure Stripe products and webhooks (credit-based)
python scripts/stripe_setup.py --api-key sk_live_... --webhook-url https://your-backend.com/payment/webhooks --live

# 2. Configure Stripe metered billing (subscription-based)
python scripts/setup_stripe_metered_billing.py --api-key sk_live_... --live

# 3. Deploy backend with environment check
./scripts/deploy_backend.sh

# 4. Test all endpoints
python scripts/test_billing_endpoints.py --base-url https://your-backend.com --token YOUR_TOKEN

# 5. Test Stripe usage records
python scripts/test_stripe_usage_records.py --base-url https://your-backend.com --token YOUR_TOKEN

# 6. Run end-to-end tests
python scripts/test_end_to_end_billing.py --base-url https://your-backend.com --token YOUR_TOKEN
```

## Implementation Status

### ✅ Completed Features

-   **Dynamic packages** via Stripe Prices API with metadata
-   **Subscription sync** keeps plan_display and is_subscribed current
-   **Usage display** shows tokens/plan/subscription status in profile
-   **Post-checkout sync** automatically updates subscription state
-   **Policy B enforcement** (chat requires subscription, embeddings require tokens)
-   **Credit awarding** via Stripe webhooks after purchase
-   **Token deduction** after actual API usage with audit logging
-   **Stripe usage records** emission for metered subscription billing
-   **Comprehensive testing** with automated scripts and usage record validation
-   **Production deployment** guides and checklists

### ✅ Complete Implementation

-   ✅ **Token deduction** after API usage (deducts from credits_remaining_tokens)
-   ✅ **Usage integration** between api_usages collection and credits_remaining_tokens
-   ✅ **Stripe usage records** for metered billing (emitted for subscribed users)
-   ✅ **Comprehensive testing** with automated scripts and end-to-end validation

### 🧪 Testing

-   Unit tests: `test/services/billing_service_test.dart`
-   Integration tests: `scripts/test_billing_endpoints.py`
-   End-to-end tests: `scripts/test_end_to_end_billing.py`
-   Preflight invoked in ChatAIController before streaming; blocks with friendly dialog

## Troubleshooting

-   Portal/Checkout do not open
    -   Ensure the backend returns a valid https URL
    -   Check device pop-up/browser permissions
-   402/429 on API after Buy
    -   Webhook delay: credits update after Stripe event is processed
    -   Verify backend logs and ledger updates
-   iOS/macOS in-app purchase
    -   Stripe flows are disabled by design on these platforms to comply with App Store policies
