#!/usr/bin/env python3
"""
Stripe Setup Script for Billing System

This script helps you create the necessary Stripe products and prices
for the billing system. Run this after getting your Stripe API keys.

Usage:
    python scripts/stripe_setup.py --api-key sk_test_... [--live]
"""

import argparse
import stripe
import json
import sys
from typing import Dict, List


def create_product_with_metadata(name: str, package_id: str, tokens: int, description: str = None) -> Dict:
    """Create a Stripe product with billing metadata."""
    print(f"Creating product: {name}")
    
    product_data = {
        "name": name,
        "metadata": {
            "package_id": package_id,
            "tokens": str(tokens),
        }
    }
    
    if description:
        product_data["description"] = description
    
    try:
        product = stripe.Product.create(**product_data)
        print(f"  ✓ Created product: {product.id}")
        return product
    except Exception as e:
        print(f"  ✗ Failed to create product: {e}")
        return None


def create_price_for_product(product_id: str, amount_cents: int, currency: str = "usd") -> Dict:
    """Create a one-time price for a product."""
    print(f"Creating price for product {product_id}: ${amount_cents/100:.2f}")
    
    try:
        price = stripe.Price.create(
            product=product_id,
            unit_amount=amount_cents,
            currency=currency,
            billing_scheme="per_unit",
        )
        print(f"  ✓ Created price: {price.id}")
        return price
    except Exception as e:
        print(f"  ✗ Failed to create price: {e}")
        return None


def setup_webhook_endpoint(url: str, events: List[str]) -> Dict:
    """Create or update webhook endpoint."""
    print(f"Setting up webhook endpoint: {url}")
    
    try:
        # Check if endpoint already exists
        existing = stripe.WebhookEndpoint.list(limit=100)
        for endpoint in existing.data:
            if endpoint.url == url:
                print(f"  ! Webhook endpoint already exists: {endpoint.id}")
                return endpoint
        
        # Create new endpoint
        webhook = stripe.WebhookEndpoint.create(
            url=url,
            enabled_events=events,
        )
        print(f"  ✓ Created webhook endpoint: {webhook.id}")
        print(f"  ✓ Webhook secret: {webhook.secret}")
        return webhook
    except Exception as e:
        print(f"  ✗ Failed to create webhook: {e}")
        return None


def main():
    parser = argparse.ArgumentParser(description="Set up Stripe products and prices for billing")
    parser.add_argument("--api-key", required=True, help="Stripe API key (secret key)")
    parser.add_argument("--webhook-url", help="Webhook endpoint URL (e.g., https://your-backend.com/payment/webhooks)")
    parser.add_argument("--live", action="store_true", help="Use live mode (default: test mode)")
    args = parser.parse_args()
    
    # Set up Stripe
    stripe.api_key = args.api_key
    mode = "live" if args.live else "test"
    print(f"Setting up Stripe billing in {mode} mode")
    print("=" * 50)
    
    # Define products to create
    products_config = [
        {
            "name": "100k Tokens",
            "package_id": "credits_100k",
            "tokens": 100000,
            "price_cents": 1000,  # $10.00
            "description": "100,000 AI tokens for chat and embeddings"
        },
        {
            "name": "1M Tokens",
            "package_id": "credits_1m", 
            "tokens": 1000000,
            "price_cents": 8000,  # $80.00
            "description": "1,000,000 AI tokens for chat and embeddings"
        }
    ]
    
    created_items = []
    
    # Create products and prices
    for config in products_config:
        print(f"\n📦 Setting up {config['name']}")
        
        # Create product
        product = create_product_with_metadata(
            name=config["name"],
            package_id=config["package_id"],
            tokens=config["tokens"],
            description=config["description"]
        )
        
        if not product:
            continue
            
        # Create price
        price = create_price_for_product(
            product_id=product.id,
            amount_cents=config["price_cents"]
        )
        
        if price:
            created_items.append({
                "package_id": config["package_id"],
                "product_id": product.id,
                "price_id": price.id,
                "name": config["name"],
                "tokens": config["tokens"],
                "price": f"${config['price_cents']/100:.2f}"
            })
    
    # Set up webhook if URL provided
    if args.webhook_url:
        print(f"\n🔗 Setting up webhook")
        webhook_events = [
            "checkout.session.completed",
            "payment_intent.succeeded",
            "payment_intent.payment_failed"
        ]
        webhook = setup_webhook_endpoint(args.webhook_url, webhook_events)
    
    # Output configuration
    print("\n" + "=" * 50)
    print("✅ Setup Complete!")
    print("\nEnvironment Variables to Set:")
    print("-" * 30)
    
    for item in created_items:
        env_var = f"STRIPE_PRICE_{item['package_id'].upper()}"
        print(f"{env_var}={item['price_id']}")
    
    if args.webhook_url and 'webhook' in locals():
        print(f"STRIPE_WEBHOOK_ENCRIPTION_SECRET={webhook.secret}")
    
    print(f"\nCreated Items Summary:")
    print("-" * 30)
    for item in created_items:
        print(f"• {item['name']}: {item['price']} ({item['tokens']:,} tokens)")
        print(f"  Product ID: {item['product_id']}")
        print(f"  Price ID: {item['price_id']}")
        print()
    
    # Generate .env snippet
    env_content = f"""
# Stripe Configuration (generated by stripe_setup.py)
STRIPE_SECRET={args.api_key}
"""
    
    for item in created_items:
        env_var = f"STRIPE_PRICE_{item['package_id'].upper()}"
        env_content += f"{env_var}={item['price_id']}\n"
    
    if args.webhook_url and 'webhook' in locals():
        env_content += f"STRIPE_WEBHOOK_ENCRIPTION_SECRET={webhook.secret}\n"
    
    with open(".env.generated", "w") as f:
        f.write(env_content.strip())
    
    print(f"💾 Environment variables saved to .env.generated")
    print("   Copy these to your backend .env file")


if __name__ == "__main__":
    main()
