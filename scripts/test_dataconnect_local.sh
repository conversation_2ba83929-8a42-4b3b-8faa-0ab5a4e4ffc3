#!/usr/bin/env bash
# Simple local smoke test for Firebase Data Connect emulator
# - Starts the Data Connect and Auth emulators for the duration of the test
# - Signs in anonymously against Auth emulator to obtain an ID token
# - Executes UpsertUser mutation against the Data Connect emulator with Authorization header
# - Prints the response JSON
#
# Requirements:
# - firebase-tools installed and logged in (or using application default creds)
# - curl installed; jq optional (pretty print). If jq is missing, script will still work.
#
# Usage:
#   bash scripts/test_dataconnect_local.sh
#
# TODO: Extend to cover other mutations and queries and add negative tests

set -euo pipefail

# Enable verbose output
echo "[INFO] Starting Firebase Data Connect emulator smoke test..."
echo "[INFO] Script: $0"
echo "[INFO] Working directory: $(pwd)"

PROJECT_ID="diogenesaichatbot"
REGION="us-central1"
SERVICE_ID="diogenesaichatbot"
CONNECTOR_ID="default"
PORT="9399"
AUTH_PORT="9099"

# GraphQL mutation and variables
GQL=$(cat <<'EOF'
mutation UpsertUser($username: String!) {
  user_upsert(
    data: {
      id_expr: "auth.uid"
      username: $username
    }
  ) { id }
}
EOF
)

USERNAME="local-user-$(date +%s)"

ENDPOINT="http://localhost:${PORT}/v1/projects/${PROJECT_ID}/locations/${REGION}/services/${SERVICE_ID}:executeGraphql?connector=${CONNECTOR_ID}"
AUTH_SIGNUP_URL="http://localhost:${AUTH_PORT}/identitytoolkit.googleapis.com/v1/accounts:signUp?key=dummy"

echo "[INFO] Will POST to: ${ENDPOINT}"

build_request_body() {
  if command -v jq >/dev/null 2>&1; then
    jq -n --arg q "$GQL" --arg username "$USERNAME" '{query: $q, variables: {username: $username}, operationName: "UpsertUser"}'
  else
    echo "[WARN] jq not found, falling back to basic JSON construction"
    GQL_ONELINE=$(printf '%s' "$GQL" | python3 - <<'PY'
import json, sys
print(json.dumps(sys.stdin.read())[1:-1])
PY
)
    printf '{\n  "query": "%s",\n  "operationName": "UpsertUser",\n  "variables": {"username": "%s"}\n}\n' "$GQL_ONELINE" "$USERNAME"
  fi
}

REQUEST_BODY=$(build_request_body)

echo "[INFO] Checking Firebase CLI..."
firebase --version || { echo "[ERROR] Firebase CLI not found"; exit 1; }

echo "[INFO] Launching dataconnect+auth emulators via firebase emulators:exec..."
# Build command that will run under emulators:exec
read -r INNER_CMD <<'BASH'
set -euo pipefail
set -x
# 1) Get anonymous ID token from Auth emulator
TOKEN=$(curl -sS -X POST "$AUTH_SIGNUP_URL" -H 'Content-Type: application/json' -d '{"returnSecureToken": true}' | \
  (jq -r .idToken 2>/dev/null || python3 -c 'import sys,json; print(json.load(sys.stdin).get("idToken",""))'))
if [ -z "$TOKEN" ]; then
  echo "[ERROR] Failed to obtain ID token from Auth emulator" >&2
  exit 1
fi
# 2) Call Data Connect emulator with Authorization header
curl -sS -X POST "$ENDPOINT" \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer $TOKEN" \
  -d '$REQUEST_BODY' | (jq . 2>/dev/null || cat)
BASH

firebase emulators:exec --only dataconnect,auth -- bash -lc "$INNER_CMD" || { echo "[ERROR] emulators:exec failed"; exit 1; }

echo "[INFO] UpsertUser completed. Next steps: run test/dataconnect_emulator_test.sh for more coverage."
# Use emulators:exec to bring up only Data Connect and Auth emulators for the duration of curl
set -x
echo "[INFO] Launching dataconnect+auth emulators via firebase emulators:exec..."
firebase emulators:exec --only dataconnect,auth -- bash -lc "$INNER_CMD" || { echo "[ERROR] emulators:exec failed"; exit 1; }
set +x

echo "[INFO] UpsertUser completed. Next steps: run test/dataconnect_emulator_test.sh for more coverage."
