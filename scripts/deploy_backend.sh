#!/bin/bash
# Backend Deployment Script for Billing System
# This script helps deploy the backend with proper environment configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_DIR="."
ENV_FILE=".env"
REQUIRED_VARS=(
    "STRIPE_SECRET"
    "STRIPE_WEBHOOK_ENCRIPTION_SECRET"
    "STRIPE_PRICE_CREDITS_100K"
    "STRIPE_PRICE_CREDITS_1M"
)

echo -e "${BLUE}🚀 Backend Deployment Script${NC}"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "main.py" ]; then
    echo -e "${RED}❌ Error: main.py not found. Please run this script from the backend directory.${NC}"
    exit 1
fi

# Check for environment file
if [ ! -f "$ENV_FILE" ]; then
    echo -e "${YELLOW}⚠️  Warning: $ENV_FILE not found.${NC}"
    echo "Creating from template..."
    
    if [ -f ".env.billing.template" ]; then
        cp .env.billing.template $ENV_FILE
        echo -e "${GREEN}✓ Created $ENV_FILE from template${NC}"
        echo -e "${YELLOW}📝 Please edit $ENV_FILE with your actual values before continuing.${NC}"
        exit 1
    else
        echo -e "${RED}❌ Error: No .env template found. Please create $ENV_FILE manually.${NC}"
        exit 1
    fi
fi

# Load environment variables
echo -e "${BLUE}📋 Checking environment configuration...${NC}"
source $ENV_FILE

# Check required variables
missing_vars=()
for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    else
        echo -e "${GREEN}✓ $var${NC} is set"
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo -e "${RED}❌ Missing required environment variables:${NC}"
    for var in "${missing_vars[@]}"; do
        echo -e "   - $var"
    done
    echo -e "${YELLOW}Please set these in $ENV_FILE${NC}"
    exit 1
fi

# Check Python dependencies
echo -e "${BLUE}📦 Checking Python dependencies...${NC}"
if [ -f "requirements.txt" ]; then
    echo "Installing/updating dependencies..."
    pip install -r requirements.txt
    echo -e "${GREEN}✓ Dependencies installed${NC}"
else
    echo -e "${YELLOW}⚠️  No requirements.txt found${NC}"
fi

# Test Stripe connection
echo -e "${BLUE}🔗 Testing Stripe connection...${NC}"
python3 -c "
import stripe
import os
stripe.api_key = os.getenv('STRIPE_SECRET')
try:
    account = stripe.Account.retrieve()
    print(f'✓ Connected to Stripe account: {account.display_name or account.id}')
except Exception as e:
    print(f'❌ Stripe connection failed: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Stripe connection test failed${NC}"
    exit 1
fi

# Test billing endpoints (if server is running)
echo -e "${BLUE}🧪 Testing billing endpoints...${NC}"
if pgrep -f "main.py" > /dev/null; then
    echo "Server appears to be running, testing endpoints..."
    
    # Test health endpoint
    if curl -s http://localhost:8080/ > /dev/null; then
        echo -e "${GREEN}✓ Server is responding${NC}"
        
        # Test billing endpoints (requires auth token)
        echo -e "${YELLOW}ℹ️  To test billing endpoints, run:${NC}"
        echo "   python scripts/test_billing_endpoints.py --token YOUR_FIREBASE_TOKEN"
    else
        echo -e "${YELLOW}⚠️  Server not responding on localhost:8080${NC}"
    fi
else
    echo -e "${YELLOW}ℹ️  Server not running. Start with: python main.py${NC}"
fi

# Deployment options
echo -e "${BLUE}🚀 Deployment Options:${NC}"
echo "1. Local development:"
echo "   python main.py"
echo ""
echo "2. Production (Docker):"
echo "   docker build -t billing-backend ."
echo "   docker run -p 8080:8080 --env-file .env billing-backend"
echo ""
echo "3. Cloud deployment:"
echo "   - Ensure environment variables are set in your cloud platform"
echo "   - Configure webhook URL in Stripe Dashboard"
echo "   - Update CORS origins in main.py for your domain"

# Generate deployment checklist
echo -e "${BLUE}📋 Deployment Checklist:${NC}"
cat << EOF
□ Environment variables configured
□ Stripe products/prices created
□ Webhook endpoint configured in Stripe
□ CORS origins updated for production domain
□ Firebase service account key configured
□ Database connection tested
□ SSL certificate configured (for webhooks)
□ Monitoring/logging configured
□ Backup strategy in place
EOF

echo ""
echo -e "${GREEN}✅ Backend deployment preparation complete!${NC}"
echo -e "${YELLOW}💡 Next steps:${NC}"
echo "1. Start the server: python main.py"
echo "2. Test endpoints: python scripts/test_billing_endpoints.py --token YOUR_TOKEN"
echo "3. Configure webhook URL in Stripe Dashboard"
echo "4. Test end-to-end billing flow"
