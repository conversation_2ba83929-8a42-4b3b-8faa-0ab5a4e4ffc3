#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add token deduction logic to the backend.

This script adds the missing piece: actual token deduction after API usage.
Currently, the system has preflight checks but doesn't deduct tokens post-usage.

Usage:
    python scripts/add_token_deduction.py
"""

import os
import sys


def create_token_deduction_endpoint():
    """Create the token deduction endpoint in the billing API."""
    
    endpoint_code = '''
@router.post("/deduct-tokens")
async def deduct_tokens(
    req: TokenDeductionRequest,
    db: firestore.AsyncClient = Depends(get_db),
    decoded_token=Depends(verify_firebase_token),
):
    """
    Deduct tokens after actual API usage.
    Called by backend services after completing AI operations.
    """
    try:
        user_id: str = decoded_token["user_id"]
        
        # Validate request
        if req.actual_tokens <= 0:
            raise HTTPException(status_code=400, detail="Invalid token amount")
        
        # Get current user state
        user_doc_ref = db.collection("users").document(user_id)
        user_doc = await user_doc_ref.get()
        
        if not user_doc.exists:
            raise HTTPException(status_code=404, detail="User not found")
        
        data = user_doc.to_dict() or {}
        current_tokens = int(data.get("credits_remaining_tokens") or 0)
        is_subscribed = bool(data.get("is_subscribed") or False)
        
        # Apply policy B deduction rules
        ep = (req.endpoint or "").lower().strip()
        chat_eps = {"chat", "achat", "chatai", "chat_completions"}
        embed_eps = {"embeddings", "embedding", "embed", "document_embed"}
        
        tokens_to_deduct = 0
        
        if ep in chat_eps:
            # Chat endpoints: only deduct if not subscribed (pay-per-use fallback)
            if not is_subscribed:
                tokens_to_deduct = req.actual_tokens
        elif ep in embed_eps:
            # Embedding endpoints: always deduct tokens
            tokens_to_deduct = req.actual_tokens
        else:
            # Other endpoints: configurable (default: deduct)
            tokens_to_deduct = req.actual_tokens
        
        # Perform deduction
        if tokens_to_deduct > 0:
            new_balance = max(0, current_tokens - tokens_to_deduct)
            await user_doc_ref.set(
                {"credits_remaining_tokens": new_balance}, merge=True
            )
            
            # Log the deduction
            await db.collection("token_usage_log").add({
                "user_id": user_id,
                "endpoint": req.endpoint,
                "tokens_deducted": tokens_to_deduct,
                "tokens_before": current_tokens,
                "tokens_after": new_balance,
                "request_id": req.request_id,
                "timestamp": firestore.SERVER_TIMESTAMP,
            })
        
        return {
            "success": True,
            "tokens_deducted": tokens_to_deduct,
            "remaining_tokens": current_tokens - tokens_to_deduct,
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Token deduction error: {e}")


class TokenDeductionRequest(BaseModel):
    endpoint: str
    actual_tokens: int
    request_id: str | None = None
'''
    
    return endpoint_code


def create_flutter_integration():
    """Create Flutter integration for token deduction."""
    
    flutter_code = '''
// Add to BillingService class

/// Deduct tokens after actual API usage
Future<TokenDeductionResult> deductTokens({
  required String endpoint,
  required int actualTokens,
  String? requestId,
}) async {
  try {
    final resp = await _dio.post(
      '$kApiUrl/v1/billing/deduct-tokens',
      data: jsonEncode({
        'endpoint': endpoint,
        'actual_tokens': actualTokens,
        if (requestId != null) 'request_id': requestId,
      }),
      options: Options(headers: {'Content-Type': 'application/json'}),
    );
    dynamic data = resp.data;
    if (data is String) data = jsonDecode(data);
    return TokenDeductionResult.fromJson(data as Map<String, dynamic>);
  } catch (e, st) {
    logger.e('Failed deducting tokens: $e');
    logger.e('$st');
    rethrow;
  }
}

// Add model classes

class TokenDeductionResult {
  final bool success;
  final int tokensDeducted;
  final int remainingTokens;

  TokenDeductionResult({
    required this.success,
    required this.tokensDeducted,
    required this.remainingTokens,
  });

  factory TokenDeductionResult.fromJson(Map<String, dynamic> json) => TokenDeductionResult(
    success: (json['success'] as bool?) ?? false,
    tokensDeducted: (json['tokens_deducted'] as num?)?.toInt() ?? 0,
    remainingTokens: (json['remaining_tokens'] as num?)?.toInt() ?? 0,
  );
}

// Usage in chat controller (add after API usage tracking):

// Deduct tokens after successful API call
try {
  final billing = BillingService();
  final totalTokens = requestTokens + responseTokens;
  await billing.deductTokens(
    endpoint: 'chat',
    actualTokens: totalTokens,
    requestId: responseId,
  );
  logger.d('Deducted $totalTokens tokens for chat usage');
} catch (e) {
  logger.w('Failed to deduct tokens: $e');
  // Don't fail the main flow, but log for monitoring
}
'''
    
    return flutter_code


def create_usage_event_documentation():
    """Create documentation for proper usage event handling."""
    
    doc_content = '''# Usage Event Integration Guide

## Current State Analysis

✅ **What's Working:**
- Preflight checks before API calls (chat requires subscription, embeddings require tokens)
- Token estimation using Util_Token.countTokens()
- Usage tracking in Firestore (api_usages collection)
- Credit awarding via Stripe webhooks
- Usage display in profile

❌ **What's Missing:**
- Actual token deduction after API usage
- Integration between usage tracking and billing system
- Token consumption from credits_remaining_tokens

## Implementation Plan

### 1. Backend Token Deduction

Add POST /v1/billing/deduct-tokens endpoint:
- Called after successful API operations
- Deducts actual tokens based on policy B
- Logs deduction for audit trail

### 2. Flutter Integration

Update chat controllers to call deduction after usage:
- ChatAIController: after handleEventData
- ChatBotController: after response completion
- AI Tutor services: after content generation

### 3. Usage Flow

```
1. User initiates chat/embedding request
2. Frontend calls preflight check
3. If allowed, proceed with API call
4. Track usage in api_usages collection (existing)
5. **NEW:** Deduct actual tokens from credits_remaining_tokens
6. Update UI with new token balance
```

### 4. Error Handling

- Deduction failures don't break main flow
- Log all deduction attempts for monitoring
- Graceful degradation if billing service unavailable

## Code Changes Required

### Backend (app/api/billing.py)
- Add TokenDeductionRequest model
- Add deduct_tokens endpoint
- Add token_usage_log collection for audit

### Frontend (lib/services/billing_service.dart)
- Add deductTokens method
- Add TokenDeductionResult model

### Controllers
- lib/controllers/chat_ai_controller.dart
- lib/controllers/chat_bot_controller.dart
- lib/features/ai_tutor/domain/services/ai_content_service.dart

## Testing

1. **Unit Tests:**
   - Test deduction with/without subscription
   - Test different endpoint types
   - Test error scenarios

2. **Integration Tests:**
   - Complete chat flow with token deduction
   - Embedding flow with token deduction
   - Verify balance updates in UI

3. **Load Tests:**
   - High-frequency deduction calls
   - Concurrent user scenarios
   - Database performance under load

## Monitoring

Track these metrics:
- Token deduction success/failure rates
- Deduction latency
- Balance accuracy (credits awarded vs deducted)
- User complaints about incorrect balances

## Rollout Strategy

1. **Phase 1:** Deploy backend endpoint (no callers yet)
2. **Phase 2:** Add Flutter integration (feature flag controlled)
3. **Phase 3:** Enable for subset of users
4. **Phase 4:** Full rollout with monitoring
5. **Phase 5:** Remove old usage tracking if redundant
'''
    
    return doc_content


def main():
    print("🔧 Token Deduction Integration Script")
    print("=" * 40)
    
    # Create backend endpoint code
    print("\n📝 Creating backend endpoint code...")
    backend_code = create_token_deduction_endpoint()
    with open("backend_token_deduction.py", "w") as f:
        f.write(backend_code)
    print("✅ Saved to backend_token_deduction.py")
    
    # Create Flutter integration code
    print("\n📱 Creating Flutter integration code...")
    flutter_code = create_flutter_integration()
    with open("flutter_token_integration.dart", "w") as f:
        f.write(flutter_code)
    print("✅ Saved to flutter_token_integration.dart")
    
    # Create documentation
    print("\n📚 Creating usage event documentation...")
    doc_content = create_usage_event_documentation()
    with open("docs/usage_event_integration.md", "w") as f:
        f.write(doc_content)
    print("✅ Saved to docs/usage_event_integration.md")
    
    print("\n🎯 Next Steps:")
    print("1. Review generated files")
    print("2. Add backend endpoint to app/api/billing.py")
    print("3. Add Flutter methods to lib/services/billing_service.dart")
    print("4. Update chat controllers to call deduction")
    print("5. Test end-to-end token flow")
    print("6. Monitor token balance accuracy")
    
    print("\n⚠️  Important Notes:")
    print("- Current system tracks usage but doesn't deduct tokens")
    print("- Preflight checks work but need post-usage deduction")
    print("- Consider feature flag for gradual rollout")
    print("- Monitor for double-deduction bugs")


if __name__ == "__main__":
    main()
