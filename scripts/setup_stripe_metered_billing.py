#!/usr/bin/env python3
"""
Setup Stripe Metered Billing Products and Subscriptions

This script creates the necessary Stripe products, prices, and subscription plans
for metered billing with usage records.

Usage:
    python scripts/setup_stripe_metered_billing.py --api-key sk_test_... [--live]
"""

import argparse
import stripe
import json
import sys
from typing import Dict, List


def create_metered_product(name: str, endpoint_type: str, description: str = None) -> Dict:
    """Create a Stripe product for metered billing."""
    print(f"Creating metered product: {name}")
    
    product_data = {
        "name": name,
        "metadata": {
            "endpoint_type": endpoint_type,
            "billing_unit": "tokens",
            "billing_type": "metered"
        }
    }
    
    if description:
        product_data["description"] = description
    
    try:
        product = stripe.Product.create(**product_data)
        print(f"  ✓ Created product: {product.id}")
        return product
    except Exception as e:
        print(f"  ✗ Failed to create product: {e}")
        return None


def create_metered_price(product_id: str, unit_amount_per_1k: int, currency: str = "usd") -> Dict:
    """Create a metered price for a product (per 1k tokens)."""
    print(f"Creating metered price for product {product_id}: ${unit_amount_per_1k/100:.4f} per 1k tokens")
    
    try:
        price = stripe.Price.create(
            product=product_id,
            unit_amount=unit_amount_per_1k,  # Price per 1k tokens in cents
            currency=currency,
            billing_scheme="per_unit",
            recurring={
                "usage_type": "metered",
                "interval": "month",
                "aggregate_usage": "sum"
            },
            transform_quantity={
                "divide_by": 1000,  # Convert individual tokens to 1k token units
                "round": "up"
            }
        )
        print(f"  ✓ Created metered price: {price.id}")
        return price
    except Exception as e:
        print(f"  ✗ Failed to create price: {e}")
        return None


def create_subscription_plan(
    name: str, 
    base_amount: int, 
    metered_prices: List[str],
    currency: str = "usd"
) -> Dict:
    """Create a subscription plan with base fee + metered usage."""
    print(f"Creating subscription plan: {name}")
    
    try:
        # Create base product for the plan
        base_product = stripe.Product.create(
            name=f"{name} - Base Plan",
            metadata={
                "plan_type": "subscription_base",
                "plan_name": name.lower().replace(" ", "_")
            }
        )
        
        # Create base price (monthly recurring)
        base_price = stripe.Price.create(
            product=base_product.id,
            unit_amount=base_amount,
            currency=currency,
            recurring={"interval": "month"}
        )
        
        print(f"  ✓ Created base plan: {base_product.id} with price: {base_price.id}")
        
        return {
            "product": base_product,
            "base_price": base_price,
            "metered_prices": metered_prices,
            "name": name
        }
    except Exception as e:
        print(f"  ✗ Failed to create subscription plan: {e}")
        return None


def main():
    parser = argparse.ArgumentParser(description="Set up Stripe metered billing products")
    parser.add_argument("--api-key", required=True, help="Stripe API key (secret key)")
    parser.add_argument("--live", action="store_true", help="Use live mode (default: test mode)")
    args = parser.parse_args()
    
    # Set up Stripe
    stripe.api_key = args.api_key
    mode = "live" if args.live else "test"
    print(f"Setting up Stripe metered billing in {mode} mode")
    print("=" * 60)
    
    created_items = []
    
    # 1. Create metered products
    print("\n📦 Creating Metered Products")
    
    chat_product = create_metered_product(
        name="Chat API Usage",
        endpoint_type="chat",
        description="Metered billing for chat API usage"
    )
    
    embedding_product = create_metered_product(
        name="Embedding API Usage", 
        endpoint_type="embeddings",
        description="Metered billing for embedding API usage"
    )
    
    if not chat_product or not embedding_product:
        print("❌ Failed to create required products")
        return
    
    # 2. Create metered prices
    print("\n💰 Creating Metered Prices")
    
    chat_price = create_metered_price(
        product_id=chat_product.id,
        unit_amount_per_1k=200  # $0.002 per 1k tokens
    )
    
    embedding_price = create_metered_price(
        product_id=embedding_product.id,
        unit_amount_per_1k=40   # $0.0004 per 1k tokens
    )
    
    if not chat_price or not embedding_price:
        print("❌ Failed to create required prices")
        return
    
    metered_prices = [chat_price.id, embedding_price.id]
    
    # 3. Create subscription plans
    print("\n📋 Creating Subscription Plans")
    
    pro_plan = create_subscription_plan(
        name="Pro Plan",
        base_amount=2000,  # $20/month
        metered_prices=metered_prices
    )
    
    team_plan = create_subscription_plan(
        name="Team Plan", 
        base_amount=5000,  # $50/month
        metered_prices=metered_prices
    )
    
    # 4. Output configuration
    print("\n" + "=" * 60)
    print("✅ Metered Billing Setup Complete!")
    
    print("\n📋 Created Items:")
    print("-" * 30)
    print(f"Chat Product: {chat_product.id}")
    print(f"Chat Metered Price: {chat_price.id} ($0.002 per 1k tokens)")
    print(f"Embedding Product: {embedding_product.id}")
    print(f"Embedding Metered Price: {embedding_price.id} ($0.0004 per 1k tokens)")
    
    if pro_plan:
        print(f"Pro Plan Product: {pro_plan['product'].id}")
        print(f"Pro Plan Base Price: {pro_plan['base_price'].id} ($20/month)")
    
    if team_plan:
        print(f"Team Plan Product: {team_plan['product'].id}")
        print(f"Team Plan Base Price: {team_plan['base_price'].id} ($50/month)")
    
    print("\n🔧 Environment Variables:")
    print("-" * 30)
    print(f"ENABLE_STRIPE_USAGE_RECORDS=true")
    print(f"STRIPE_CHAT_METERED_PRICE={chat_price.id}")
    print(f"STRIPE_EMBEDDING_METERED_PRICE={embedding_price.id}")
    
    if pro_plan:
        print(f"STRIPE_PRO_PLAN_PRICE={pro_plan['base_price'].id}")
    if team_plan:
        print(f"STRIPE_TEAM_PLAN_PRICE={team_plan['base_price'].id}")
    
    print("\n📖 Next Steps:")
    print("-" * 30)
    print("1. Add environment variables to your backend")
    print("2. Create subscriptions in Stripe Dashboard or via API")
    print("3. Test usage record emission with test API calls")
    print("4. Monitor usage in Stripe Dashboard → Billing → Usage records")
    
    print("\n💡 Subscription Creation Example:")
    print("-" * 30)
    print("# Create a Pro subscription with metered usage")
    print("stripe.Subscription.create(")
    print("  customer='cus_...',")
    print("  items=[")
    print(f"    {{'price': '{pro_plan['base_price'].id if pro_plan else 'BASE_PRICE_ID'}'}},  # Base fee")
    print(f"    {{'price': '{chat_price.id}'}},  # Chat usage")
    print(f"    {{'price': '{embedding_price.id}'}},  # Embedding usage")
    print("  ]")
    print(")")
    
    # Generate configuration file
    config = {
        "mode": mode,
        "products": {
            "chat": {
                "product_id": chat_product.id,
                "metered_price_id": chat_price.id,
                "endpoint_type": "chat"
            },
            "embeddings": {
                "product_id": embedding_product.id,
                "metered_price_id": embedding_price.id,
                "endpoint_type": "embeddings"
            }
        },
        "plans": {}
    }
    
    if pro_plan:
        config["plans"]["pro"] = {
            "product_id": pro_plan["product"].id,
            "base_price_id": pro_plan["base_price"].id,
            "name": "Pro Plan"
        }
    
    if team_plan:
        config["plans"]["team"] = {
            "product_id": team_plan["product"].id,
            "base_price_id": team_plan["base_price"].id,
            "name": "Team Plan"
        }
    
    with open("stripe_metered_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print(f"\n💾 Configuration saved to stripe_metered_config.json")


if __name__ == "__main__":
    main()
