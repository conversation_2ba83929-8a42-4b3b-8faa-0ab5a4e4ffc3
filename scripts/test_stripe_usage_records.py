#!/usr/bin/env python3
"""
Test Stripe Usage Records Integration

This script tests the complete flow of token deduction and Stripe usage record emission.

Usage:
    python scripts/test_stripe_usage_records.py --base-url https://your-backend.com --token YOUR_FIREBASE_TOKEN
"""

import argparse
import json
import requests
import time
import sys
from typing import Dict, Any


class StripeUsageRecordTester:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        self.test_results = []
    
    def log_test(self, name: str, success: bool, details: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
        if details:
            print(f"    {details}")
        self.test_results.append({
            "name": name,
            "success": success,
            "details": details
        })
    
    def api_call(self, method: str, path: str, data: Dict = None) -> Dict[str, Any]:
        """Make API call and return response."""
        url = f"{self.base_url}{path}"
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=self.headers, timeout=10)
            elif method.upper() == "POST":
                response = requests.post(url, headers=self.headers, json=data, timeout=10)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            if response.status_code == 200:
                return {"success": True, "data": response.json(), "status": response.status_code}
            else:
                return {"success": False, "error": response.text, "status": response.status_code}
        except Exception as e:
            return {"success": False, "error": str(e), "status": 0}
    
    def test_token_deduction_endpoint(self):
        """Test the token deduction endpoint."""
        print("\n🔻 Testing Token Deduction Endpoint")
        
        # Test chat endpoint deduction
        result = self.api_call("POST", "/v1/billing/deduct-tokens", {
            "endpoint": "chat",
            "actual_tokens": 1000,
            "request_id": f"test-chat-{int(time.time())}"
        })
        
        if result["success"]:
            deduction_data = result["data"]
            self.log_test("Chat token deduction", True, 
                         f"Deducted: {deduction_data.get('tokens_deducted', 0)} tokens, "
                         f"Remaining: {deduction_data.get('remaining_tokens', 0)}")
        else:
            self.log_test("Chat token deduction", False, result["error"])
        
        # Test embedding endpoint deduction
        result = self.api_call("POST", "/v1/billing/deduct-tokens", {
            "endpoint": "embeddings",
            "actual_tokens": 500,
            "request_id": f"test-embed-{int(time.time())}"
        })
        
        if result["success"]:
            deduction_data = result["data"]
            self.log_test("Embedding token deduction", True,
                         f"Deducted: {deduction_data.get('tokens_deducted', 0)} tokens, "
                         f"Remaining: {deduction_data.get('remaining_tokens', 0)}")
        else:
            self.log_test("Embedding token deduction", False, result["error"])
    
    def test_subscription_sync(self):
        """Test subscription sync to ensure user is subscribed for usage records."""
        print("\n🔄 Testing Subscription Sync")
        
        result = self.api_call("POST", "/v1/billing/sync-subscription")
        
        if result["success"]:
            sync_data = result["data"]
            is_subscribed = sync_data.get("is_subscribed", False)
            plan_display = sync_data.get("plan_display")
            
            self.log_test("Subscription sync", True, 
                         f"Subscribed: {is_subscribed}, Plan: {plan_display}")
            return is_subscribed
        else:
            self.log_test("Subscription sync", False, result["error"])
            return False
    
    def test_usage_summary(self):
        """Test usage summary to check current state."""
        print("\n📊 Testing Usage Summary")
        
        result = self.api_call("GET", "/v1/billing/usage")
        
        if result["success"]:
            usage = result["data"]
            self.log_test("Usage summary", True, 
                         f"Tokens: {usage.get('remaining_tokens')}, "
                         f"Subscribed: {usage.get('is_subscribed')}, "
                         f"Plan: {usage.get('plan_display')}")
            return usage
        else:
            self.log_test("Usage summary", False, result["error"])
            return {}
    
    def test_multiple_deductions(self, is_subscribed: bool):
        """Test multiple token deductions to generate usage records."""
        print("\n🔄 Testing Multiple Token Deductions")
        
        if not is_subscribed:
            self.log_test("Multiple deductions", False, 
                         "User not subscribed - Stripe usage records won't be emitted")
            return
        
        # Test multiple chat deductions
        for i in range(3):
            result = self.api_call("POST", "/v1/billing/deduct-tokens", {
                "endpoint": "chat",
                "actual_tokens": 250,
                "request_id": f"test-multi-chat-{int(time.time())}-{i}"
            })
            
            if result["success"]:
                self.log_test(f"Chat deduction {i+1}", True, "250 tokens deducted")
            else:
                self.log_test(f"Chat deduction {i+1}", False, result["error"])
            
            time.sleep(1)  # Avoid rate limits
        
        # Test multiple embedding deductions
        for i in range(2):
            result = self.api_call("POST", "/v1/billing/deduct-tokens", {
                "endpoint": "embeddings", 
                "actual_tokens": 500,
                "request_id": f"test-multi-embed-{int(time.time())}-{i}"
            })
            
            if result["success"]:
                self.log_test(f"Embedding deduction {i+1}", True, "500 tokens deducted")
            else:
                self.log_test(f"Embedding deduction {i+1}", False, result["error"])
            
            time.sleep(1)
    
    def test_error_handling(self):
        """Test error handling for invalid requests."""
        print("\n🚨 Testing Error Handling")
        
        # Test invalid token amount
        result = self.api_call("POST", "/v1/billing/deduct-tokens", {
            "endpoint": "chat",
            "actual_tokens": -100,
            "request_id": "test-invalid"
        })
        
        if not result["success"] and result["status"] == 400:
            self.log_test("Invalid token amount handling", True, "Properly rejected negative tokens")
        else:
            self.log_test("Invalid token amount handling", False, "Should reject negative tokens")
        
        # Test missing endpoint
        result = self.api_call("POST", "/v1/billing/deduct-tokens", {
            "endpoint": "",
            "actual_tokens": 100,
            "request_id": "test-missing-endpoint"
        })
        
        if result["success"]:
            # Empty endpoint should still work (falls to default case)
            self.log_test("Empty endpoint handling", True, "Handled empty endpoint gracefully")
        else:
            self.log_test("Empty endpoint handling", False, "Should handle empty endpoint")
    
    def run_all_tests(self):
        """Run all Stripe usage record tests."""
        print("🧪 Starting Stripe Usage Records Tests")
        print("=" * 60)
        
        # Get initial state
        usage_data = self.test_usage_summary()
        is_subscribed = self.test_subscription_sync()
        
        # Test token deduction
        self.test_token_deduction_endpoint()
        
        # Test multiple deductions (generates usage records if subscribed)
        self.test_multiple_deductions(is_subscribed)
        
        # Test error handling
        self.test_error_handling()
        
        # Final state check
        print("\n📊 Final Usage Check")
        final_usage = self.test_usage_summary()
        
        # Summary
        self.print_summary(usage_data, final_usage, is_subscribed)
    
    def print_summary(self, initial_usage: Dict, final_usage: Dict, is_subscribed: bool):
        """Print test summary and usage record information."""
        print("\n" + "=" * 60)
        print("📋 Test Summary")
        print("-" * 30)
        
        passed = sum(1 for test in self.test_results if test["success"])
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total*100):.1f}%")
        
        # Usage comparison
        print("\n📊 Token Usage Comparison")
        print("-" * 30)
        initial_tokens = initial_usage.get("remaining_tokens", 0) or 0
        final_tokens = final_usage.get("remaining_tokens", 0) or 0
        tokens_used = initial_tokens - final_tokens
        
        print(f"Initial Tokens: {initial_tokens}")
        print(f"Final Tokens: {final_tokens}")
        print(f"Tokens Used: {tokens_used}")
        
        # Stripe usage records info
        print("\n🔗 Stripe Usage Records")
        print("-" * 30)
        if is_subscribed:
            print("✅ User is subscribed - Usage records should be emitted to Stripe")
            print("📍 Check Stripe Dashboard → Billing → Usage records")
            print("🔍 Look for records with quantities matching token deductions")
            print("⏰ Records may take a few minutes to appear in dashboard")
        else:
            print("❌ User not subscribed - No usage records emitted")
            print("💡 Create a subscription to test usage record emission")
        
        # Environment check
        print("\n⚙️ Environment Configuration")
        print("-" * 30)
        print("Required environment variables:")
        print("- ENABLE_STRIPE_USAGE_RECORDS=true")
        print("- STRIPE_SECRET=sk_...")
        print("- Products with endpoint_type metadata")
        
        # Next steps
        print("\n🎯 Next Steps")
        print("-" * 30)
        if passed == total:
            if is_subscribed:
                print("1. ✅ All tests passed!")
                print("2. 🔍 Check Stripe Dashboard for usage records")
                print("3. 📊 Monitor billing accuracy")
                print("4. 🚀 Ready for production!")
            else:
                print("1. ✅ Token deduction working correctly")
                print("2. 📝 Create test subscription to verify usage records")
                print("3. 🔄 Re-run tests with subscribed user")
        else:
            print("1. 🔧 Fix failing tests")
            print("2. 📋 Check backend logs for errors")
            print("3. 🔄 Re-run tests after fixes")


def main():
    parser = argparse.ArgumentParser(description="Test Stripe usage records integration")
    parser.add_argument("--base-url", required=True, help="Backend base URL")
    parser.add_argument("--token", required=True, help="Firebase auth token")
    args = parser.parse_args()
    
    tester = StripeUsageRecordTester(args.base_url, args.token)
    tester.run_all_tests()


if __name__ == "__main__":
    main()
