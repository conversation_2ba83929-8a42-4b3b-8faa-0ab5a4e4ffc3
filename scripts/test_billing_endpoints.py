#!/usr/bin/env python3
"""
Test script for billing endpoints.
Run this after setting up the backend to verify all endpoints work correctly.

Usage:
    python scripts/test_billing_endpoints.py --base-url http://localhost:8080 --token YOUR_FIREBASE_TOKEN
"""

import argparse
import json
import requests
import sys
from typing import Dict, Any


def test_endpoint(base_url: str, token: str, method: str, path: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
    """Test a single endpoint and return the response."""
    url = f"{base_url}{path}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data)
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        print(f"{method} {path} -> {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✓ Success: {json.dumps(result, indent=2)}")
            return result
        else:
            print(f"  ✗ Error: {response.text}")
            return {"error": response.text, "status_code": response.status_code}
            
    except Exception as e:
        print(f"  ✗ Exception: {e}")
        return {"error": str(e)}


def main():
    parser = argparse.ArgumentParser(description="Test billing endpoints")
    parser.add_argument("--base-url", default="http://localhost:8080", help="Backend base URL")
    parser.add_argument("--token", required=True, help="Firebase auth token")
    args = parser.parse_args()
    
    print(f"Testing billing endpoints at {args.base_url}")
    print("=" * 50)
    
    # Test credit packages
    print("\n1. Testing credit packages...")
    packages = test_endpoint(args.base_url, args.token, "GET", "/v1/billing/credit-packages")
    
    # Test usage
    print("\n2. Testing usage...")
    usage = test_endpoint(args.base_url, args.token, "GET", "/v1/billing/usage")
    
    # Test preflight for chat
    print("\n3. Testing preflight (chat)...")
    preflight_chat = test_endpoint(
        args.base_url, args.token, "POST", "/v1/billing/preflight",
        {"endpoint": "chat", "estimated_tokens": 1000}
    )
    
    # Test preflight for embeddings
    print("\n4. Testing preflight (embeddings)...")
    preflight_embed = test_endpoint(
        args.base_url, args.token, "POST", "/v1/billing/preflight",
        {"endpoint": "embeddings", "estimated_tokens": 500}
    )
    
    # Test subscription sync
    print("\n5. Testing subscription sync...")
    sync = test_endpoint(args.base_url, args.token, "POST", "/v1/billing/sync-subscription")
    
    # Test portal link
    print("\n6. Testing portal link...")
    portal = test_endpoint(args.base_url, args.token, "GET", "/v1/billing/portal-link")
    
    # Test checkout session (if packages available)
    if packages and "packages" in packages and packages["packages"]:
        package_id = packages["packages"][0]["id"]
        print(f"\n7. Testing checkout session (package: {package_id})...")
        checkout = test_endpoint(
            args.base_url, args.token, "POST", "/v1/billing/checkout-session",
            {"package_id": package_id, "quantity": 1}
        )
    else:
        print("\n7. Skipping checkout test (no packages available)")
    
    print("\n" + "=" * 50)
    print("Billing endpoint testing complete!")
    
    # Summary
    print("\nSummary:")
    if usage and "remaining_tokens" in usage:
        print(f"  • Remaining tokens: {usage['remaining_tokens']}")
    if usage and "is_subscribed" in usage:
        print(f"  • Subscribed: {usage['is_subscribed']}")
    if usage and "plan_display" in usage:
        print(f"  • Plan: {usage['plan_display']}")
    
    if packages and "packages" in packages:
        print(f"  • Available packages: {len(packages['packages'])}")
        for pkg in packages["packages"]:
            print(f"    - {pkg.get('label', pkg.get('id'))}: {pkg.get('price_display', 'N/A')}")


if __name__ == "__main__":
    main()
