#!/usr/bin/env python3
"""
End-to-End Billing System Test Script

This script tests the complete billing flow from purchase to token deduction.
Run this after deploying the billing system to verify everything works.

Usage:
    python scripts/test_end_to_end_billing.py --base-url https://your-backend.com --token YOUR_FIREBASE_TOKEN
"""

import argparse
import json
import requests
import time
import sys
from typing import Dict, Any


class BillingTester:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        self.test_results = []
    
    def log_test(self, name: str, success: bool, details: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
        if details:
            print(f"    {details}")
        self.test_results.append({
            "name": name,
            "success": success,
            "details": details
        })
    
    def api_call(self, method: str, path: str, data: Dict = None) -> Dict[str, Any]:
        """Make API call and return response."""
        url = f"{self.base_url}{path}"
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=self.headers, timeout=10)
            elif method.upper() == "POST":
                response = requests.post(url, headers=self.headers, json=data, timeout=10)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            if response.status_code == 200:
                return {"success": True, "data": response.json(), "status": response.status_code}
            else:
                return {"success": False, "error": response.text, "status": response.status_code}
        except Exception as e:
            return {"success": False, "error": str(e), "status": 0}
    
    def test_credit_packages(self):
        """Test credit packages endpoint."""
        print("\n🏪 Testing Credit Packages")
        result = self.api_call("GET", "/v1/billing/credit-packages")
        
        if result["success"]:
            packages = result["data"].get("packages", [])
            if packages:
                self.log_test("Credit packages loaded", True, f"Found {len(packages)} packages")
                for pkg in packages:
                    required_fields = ["id", "label"]
                    missing = [f for f in required_fields if f not in pkg]
                    if missing:
                        self.log_test(f"Package {pkg.get('id', 'unknown')} validation", False, f"Missing: {missing}")
                    else:
                        self.log_test(f"Package {pkg['id']} validation", True, f"{pkg['label']} - {pkg.get('price_display', 'N/A')}")
                return packages
            else:
                self.log_test("Credit packages loaded", False, "No packages found")
        else:
            self.log_test("Credit packages loaded", False, result["error"])
        return []
    
    def test_usage_summary(self):
        """Test usage summary endpoint."""
        print("\n📊 Testing Usage Summary")
        result = self.api_call("GET", "/v1/billing/usage")
        
        if result["success"]:
            usage = result["data"]
            self.log_test("Usage summary loaded", True, f"Tokens: {usage.get('remaining_tokens')}, Subscribed: {usage.get('is_subscribed')}")
            return usage
        else:
            self.log_test("Usage summary loaded", False, result["error"])
        return {}
    
    def test_subscription_sync(self):
        """Test subscription sync endpoint."""
        print("\n🔄 Testing Subscription Sync")
        result = self.api_call("POST", "/v1/billing/sync-subscription")
        
        if result["success"]:
            sync_data = result["data"]
            self.log_test("Subscription sync", True, f"Subscribed: {sync_data.get('is_subscribed')}, Plan: {sync_data.get('plan_display')}")
            return sync_data
        else:
            self.log_test("Subscription sync", False, result["error"])
        return {}
    
    def test_preflight_checks(self, usage_data: Dict):
        """Test preflight checks for different endpoints."""
        print("\n🚦 Testing Preflight Checks")
        
        # Test chat endpoint (requires subscription)
        chat_result = self.api_call("POST", "/v1/billing/preflight", {
            "endpoint": "chat",
            "estimated_tokens": 1000
        })
        
        if chat_result["success"]:
            chat_data = chat_result["data"]
            is_subscribed = usage_data.get("is_subscribed", False)
            expected_allowed = is_subscribed
            actual_allowed = chat_data.get("allowed", False)
            
            if actual_allowed == expected_allowed:
                self.log_test("Chat preflight check", True, f"Allowed: {actual_allowed} (subscription: {is_subscribed})")
            else:
                self.log_test("Chat preflight check", False, f"Expected: {expected_allowed}, Got: {actual_allowed}")
        else:
            self.log_test("Chat preflight check", False, chat_result["error"])
        
        # Test embedding endpoint (requires tokens)
        embed_result = self.api_call("POST", "/v1/billing/preflight", {
            "endpoint": "embeddings",
            "estimated_tokens": 500
        })
        
        if embed_result["success"]:
            embed_data = embed_result["data"]
            remaining_tokens = usage_data.get("remaining_tokens", 0) or 0
            expected_allowed = remaining_tokens >= 500
            actual_allowed = embed_data.get("allowed", False)
            
            if actual_allowed == expected_allowed:
                self.log_test("Embedding preflight check", True, f"Allowed: {actual_allowed} (tokens: {remaining_tokens})")
            else:
                self.log_test("Embedding preflight check", False, f"Expected: {expected_allowed}, Got: {actual_allowed}")
        else:
            self.log_test("Embedding preflight check", False, embed_result["error"])
    
    def test_portal_link(self):
        """Test billing portal link generation."""
        print("\n🔗 Testing Portal Link")
        result = self.api_call("GET", "/v1/billing/portal-link")
        
        if result["success"]:
            url = result["data"].get("url", "")
            if url and url.startswith("https://"):
                self.log_test("Portal link generation", True, "Valid URL generated")
            else:
                self.log_test("Portal link generation", False, "Invalid URL format")
        else:
            self.log_test("Portal link generation", False, result["error"])
    
    def test_checkout_session(self, packages: list):
        """Test checkout session creation."""
        print("\n💳 Testing Checkout Session")
        
        if not packages:
            self.log_test("Checkout session creation", False, "No packages available")
            return
        
        package_id = packages[0]["id"]
        result = self.api_call("POST", "/v1/billing/checkout-session", {
            "package_id": package_id,
            "quantity": 1
        })
        
        if result["success"]:
            url = result["data"].get("url", "")
            if url and "checkout.stripe.com" in url:
                self.log_test("Checkout session creation", True, f"Valid checkout URL for {package_id}")
            else:
                self.log_test("Checkout session creation", False, "Invalid checkout URL")
        else:
            self.log_test("Checkout session creation", False, result["error"])
    
    def test_token_deduction(self):
        """Test token deduction endpoint (if implemented)."""
        print("\n🔻 Testing Token Deduction")
        result = self.api_call("POST", "/v1/billing/deduct-tokens", {
            "endpoint": "embeddings",
            "actual_tokens": 100,
            "request_id": "test-request-123"
        })
        
        if result["success"]:
            deduction_data = result["data"]
            self.log_test("Token deduction", True, f"Deducted: {deduction_data.get('tokens_deducted', 0)} tokens")
        else:
            if result["status"] == 404:
                self.log_test("Token deduction", False, "Endpoint not implemented yet")
            else:
                self.log_test("Token deduction", False, result["error"])
    
    def test_error_handling(self):
        """Test error handling and edge cases."""
        print("\n🚨 Testing Error Handling")
        
        # Test invalid package ID
        result = self.api_call("POST", "/v1/billing/checkout-session", {
            "package_id": "invalid-package",
            "quantity": 1
        })
        
        if not result["success"] and result["status"] in [400, 404]:
            self.log_test("Invalid package handling", True, "Properly rejected invalid package")
        else:
            self.log_test("Invalid package handling", False, "Should reject invalid package")
        
        # Test invalid preflight request
        result = self.api_call("POST", "/v1/billing/preflight", {
            "endpoint": "",
            "estimated_tokens": -1
        })
        
        if not result["success"] or not result["data"].get("allowed", True):
            self.log_test("Invalid preflight handling", True, "Properly handled invalid request")
        else:
            self.log_test("Invalid preflight handling", False, "Should reject invalid preflight")
    
    def run_all_tests(self):
        """Run all billing tests."""
        print("🧪 Starting End-to-End Billing Tests")
        print("=" * 50)
        
        # Test basic endpoints
        packages = self.test_credit_packages()
        usage_data = self.test_usage_summary()
        self.test_subscription_sync()
        
        # Test business logic
        self.test_preflight_checks(usage_data)
        self.test_portal_link()
        self.test_checkout_session(packages)
        self.test_token_deduction()
        
        # Test error handling
        self.test_error_handling()
        
        # Summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 50)
        print("📋 Test Summary")
        print("-" * 20)
        
        passed = sum(1 for test in self.test_results if test["success"])
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total*100):.1f}%")
        
        # List failures
        failures = [test for test in self.test_results if not test["success"]]
        if failures:
            print("\n❌ Failed Tests:")
            for test in failures:
                print(f"  - {test['name']}: {test['details']}")
        
        # Recommendations
        print("\n💡 Recommendations:")
        if passed == total:
            print("  🎉 All tests passed! Billing system is ready for production.")
        else:
            print("  🔧 Fix failing tests before production deployment.")
            print("  📊 Monitor error rates and user feedback after deployment.")
            print("  🔄 Re-run tests after fixes.")


def main():
    parser = argparse.ArgumentParser(description="Test end-to-end billing system")
    parser.add_argument("--base-url", required=True, help="Backend base URL")
    parser.add_argument("--token", required=True, help="Firebase auth token")
    args = parser.parse_args()
    
    tester = BillingTester(args.base_url, args.token)
    tester.run_all_tests()


if __name__ == "__main__":
    main()
