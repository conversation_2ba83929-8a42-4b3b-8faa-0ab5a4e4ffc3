import { onDocumentCreated } from "firebase-functions/v2/firestore";
import * as admin from "firebase-admin";

admin.initializeApp({
  credential: admin.credential.applicationDefault(),
});

export const onMessageSent = onDocumentCreated("messages/{messageId}", async (event) => {
    const snapshot = event.data; 
    if (!snapshot || !snapshot.exists) {
      console.error("Snapshot is undefined or document does not exist.");
      return;
    }
    
    const messageData = snapshot.data();
    if (!messageData) return;

    const chatRoomRef = admin.firestore().doc(`chatRooms/${messageData.chatRoomId}`);
    const chatRoomDoc = await chatRoomRef.get();
    
    const memberIds = chatRoomDoc.get("memberIds") || [];
    const recipientIds = memberIds.filter(
      (id: string) => id !== messageData.senderId
    );

    const tokens: string[] = [];
    const userTokensMap: { [key: string]: string[] } = {}; 

    for (const recipientId of recipientIds) {
      const userRef = admin.firestore().doc(`users/${recipientId}`);
      const userDoc = await userRef.get();

      if (userDoc.exists && userDoc.get("deviceTokens")) {
        const userDeviceTokens: string[] = userDoc.get("deviceTokens");
        tokens.push(...userDeviceTokens); 
        
        userDeviceTokens.forEach((token: string) => { 
          if (!userTokensMap[recipientId]) {
            userTokensMap[recipientId] = []; 
          }
          userTokensMap[recipientId].push(token); 
        });
      }
    }

    if (tokens.length === 0) {
      console.log("No device tokens found for recipients.");
      return;
    }

    const payload = {
      notification: {
        title: "New message",
        body: `${messageData.content}`,
      },
      data: {
        chat_room_id: messageData.chatRoomId,
      },
    };

    const message = {
      notification: payload.notification,
      data: payload.data,
      tokens: tokens,
    };

    const response = await admin.messaging().sendEachForMulticast(message);
    console.log(response.successCount + " messages were sent successfully");

    if (response.failureCount > 0) {
      const failedTokens: string[] = [];
      const invalidTokenReasons: { [token: string]: string } = {}; 

      response.responses.forEach((resp, idx) => {
        const failedToken = tokens[idx]; // Store the current token

        if (!resp.success) {
          failedTokens.push(failedToken);
          console.error(`Failed to send to token: ${failedToken}`);

          // Safely check for resp.error before accessing it
          if (resp.error) {
            console.error(`Error: ${resp.error.message || 'Unknown error'}`);
            invalidTokenReasons[failedToken] = resp.error.message || 'Unknown error'; // Store the error reason
          } else {
            console.error('Error: Unknown error'); // Handle undefined error case
            invalidTokenReasons[failedToken] = 'Unknown error'; // Store a default error message
          }
        }
      });

      console.log("List of tokens that caused failures: " + failedTokens);

      // Only remove invalid tokens based on specific error reasons
      for (const failedToken of failedTokens) {
        if (invalidTokenReasons[failedToken] === "NotRegistered" || invalidTokenReasons[failedToken] === "InvalidRegistration") {
          for (const recipientId of Object.keys(userTokensMap)) {
            if (userTokensMap[recipientId].includes(failedToken)) {
              const userRef = admin.firestore().doc(`users/${recipientId}`);
              await userRef.update({
                deviceTokens: admin.firestore.FieldValue.arrayRemove(failedToken)
              });
            }
          }
        }
      }
    }
});
