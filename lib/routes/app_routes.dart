import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/pages/model_settings/antrhopic_ai_page.dart';
import 'package:diogeneschatbot/features/payment/presentation/pages/balance_page.dart';
import 'package:diogeneschatbot/models/bot.dart';
import 'package:diogeneschatbot/models/conversation.dart';
import 'package:diogeneschatbot/pages/chat_bot_page.dart';
import 'package:diogeneschatbot/pages/delete_account_page.dart';
import 'package:diogeneschatbot/pages/post_details_page.dart';
import 'package:diogeneschatbot/services/auth_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/app_preferences.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/pages/home_page.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/desktop/pages/home_page.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/desktop/settings_panels/app_settings_panel.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/shared/panel/log_panel.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/desktop/settings_panels/user_panel.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/desktop/side_panels/characters_panel.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/desktop/side_panels/model_settings_panel.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/desktop/side_panels/sessions_panel.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/shared/pages/character_customization_page.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/pages/character_browser_page.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/pages/model_settings/google_gemini_page.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/pages/model_settings/llama_cpp_page.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/pages/model_settings/mistral_ai_page.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/pages/model_settings/ollama_page.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/pages/model_settings/open_ai_page.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/pages/settings_page.dart';

class AppRoutes {
  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    final uri = Uri.parse(settings.name ?? '');

    switch (uri.path) {
      case '/chat':
        return _handleChatRoute(uri);
      case '/pay':
        return MaterialPageRoute(builder: (context) => BalancePage(userId: null));

      case '/conversation':
        return _handleConversationRoute(uri);
      case '/delete_account':
        return _handleDeleteAccountRoute(); // New delete account route
      default:
        if (uri.pathSegments.length == 2 && uri.pathSegments.first == 'posts') {
          return _handlePostRoute(uri);
        } else {
          // Retrieve app preferences to determine platform
          final isDesktop = AppPreferences.of(Get.context!).isDesktop;

          if (isDesktop) {
            return _handleDesktopRoute(uri);
          } else {
            return _handleMobileRoute(uri);
          }
        }
    }
  }

  static Route<dynamic>? _handleDesktopRoute(Uri uri) {
    switch (uri.path) {
      case '/desktop-home':
        return MaterialPageRoute(builder: (context) => const DesktopHomePage());
      case '/sessions':
        return MaterialPageRoute(builder: (context) => const SessionsPanel());
      case '/character':
        return MaterialPageRoute(builder: (context) => const CharacterCustomizationPage());
      case '/characters':
        return MaterialPageRoute(builder: (context) => const CharactersPanel());
      case '/model-settings':
        return MaterialPageRoute(builder: (context) => const ModelSettingsPanel());
      case '/user-settings':
        return MaterialPageRoute(builder: (context) => const UserPanel());
      case '/settings':
        return MaterialPageRoute(builder: (context) => const AppSettingsPanel());
      case '/log':
        return MaterialPageRoute(builder: (context) => const LogPanel());
      // Add more desktop-specific routes as needed
      default:
        return null; // Handle unknown routes
    }
  }

  static Route<dynamic>? _handleMobileRoute(Uri uri) {
    switch (uri.path) {
      case '/mobile-home':
        return MaterialPageRoute(builder: (context) => const MobileHomePage());
      case '/character':
        return MaterialPageRoute(builder: (context) => const CharacterCustomizationPage());
      case '/characters':
        return MaterialPageRoute(builder: (context) => const CharacterBrowserPage());
      case '/llamacpp':
        return MaterialPageRoute(builder: (context) => const LlamaCppPage());
      case '/ollama':
        return MaterialPageRoute(builder: (context) => const OllamaPage());
      case '/openai':
        return MaterialPageRoute(builder: (context) => const OpenAiPage());
      case '/anthropic':
        return MaterialPageRoute(builder: (context) => const AnthropicPage());
      case '/mistralai':
        return MaterialPageRoute(builder: (context) => const MistralAiPage());
      case '/gemini':
        return MaterialPageRoute(builder: (context) => const GoogleGeminiPage());
      case '/settings':
        return MaterialPageRoute(builder: (context) => const SettingsPage());
      case '/log':
        return MaterialPageRoute(builder: (context) => const LogPanel());
      // Add more mobile-specific routes as needed
      default:
        return null; // Handle unknown routes
    }
  }

  static Route<dynamic>? _handleDeleteAccountRoute() {
    return MaterialPageRoute(
      builder: (context) {
        final user = FirebaseAuth.instance.currentUser;
        if (user != null) {
          return DeleteAccountPage(); // Return the page that shows the confirmation dialog
        } else {
          return const Center(child: Text('You must be logged in to delete your account'));
        }
      },
    );
  }

  static Route<dynamic>? _handleChatRoute(Uri uri) {
    final botId = uri.queryParameters['botId'];
    if (botId == null) return null;

    return MaterialPageRoute(
      builder: (context) => FutureBuilder<Bot>(
        future: BotRepository().getBot(botId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            if (snapshot.hasData) {
              return BotChatPage(userId: FirebaseAuth.instance.currentUser?.uid, selectedBot: snapshot.data!);
            } else {
              return const Center(child: Text('Error fetching bot data'));
            }
          } else {
            return const Center(child: CircularProgressIndicator());
          }
        },
      ),
    );
  }

  static Route<dynamic>? _handleConversationRoute(Uri uri) {
    final conversationId = uri.queryParameters['conversationId'];
    if (conversationId == null) return null;

    return MaterialPageRoute(
      builder: (context) => FutureBuilder<Bot?>(
        future: _fetchBotForConversation(conversationId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            if (snapshot.hasData && snapshot.data != null) {
              return BotChatPage(
                userId: FirebaseAuth.instance.currentUser?.uid ?? "",
                conversationId: conversationId,
                selectedBot: snapshot.data!,
              );
            } else {
              return const Center(child: Text('Bot not found'));
            }
          } else {
            return const Center(child: CircularProgressIndicator());
          }
        },
      ),
    );
  }

  static Route<dynamic>? _handlePostRoute(Uri uri) {
    final postId = uri.pathSegments[1];
    return MaterialPageRoute(
      builder: (context) => Consumer<AuthService>(
        builder: (context, authService, _) =>
            PostDetailsPage(postId: postId, currentUserId: AuthService.currentUserId()),
      ),
    );
  }

  static Future<Bot?> _fetchBotForConversation(String conversationId) async {
    final conversation = await ConversationRepository().getConversationById(conversationId);
    final botId = conversation?.botId;
    return botId != null ? await BotRepository().getBot(botId) : null;
  }
}
