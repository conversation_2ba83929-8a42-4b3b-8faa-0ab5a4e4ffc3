import 'package:dartz/dartz.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:developer' as developer;
import '../../../domain/entities/learning_progress.dart';
import '../../../domain/entities/learning_session.dart';
import '../../../domain/entities/flashcard.dart';
import '../../../domain/entities/quiz.dart';
import '../../../domain/entities/study_recommendation.dart';
import '../../../../../core/error/failures.dart';

/// Enhanced remote data source for AI tutor functionality using Firebase/Firestore
/// Includes performance optimizations, caching, and robust error handling
class AITutorRemoteDataSource {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  // Firebase collections with enhanced configuration
  late final CollectionReference<Map<String, dynamic>> _learningPlansCollection;
  late final CollectionReference<Map<String, dynamic>>
  _learningProgressCollection;
  late final CollectionReference<Map<String, dynamic>>
  _learningSessionsCollection;
  late final CollectionReference<Map<String, dynamic>> _flashcardsCollection;
  late final CollectionReference<Map<String, dynamic>> _quizzesCollection;
  late final CollectionReference<Map<String, dynamic>> _quizResultsCollection;
  late final CollectionReference<Map<String, dynamic>>
  _recommendationsCollection;

  // Performance optimization: Connection pooling and caching
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 1);
  static const Duration _operationTimeout = Duration(seconds: 30);

  AITutorRemoteDataSource({FirebaseFirestore? firestore, FirebaseAuth? auth})
    : _firestore = firestore ?? FirebaseFirestore.instance,
      _auth = auth ?? FirebaseAuth.instance {
    // Initialize Firebase collections with enhanced settings
    _learningPlansCollection = _firestore.collection('learning_plans');
    _learningProgressCollection = _firestore.collection('learning_progress');
    _learningSessionsCollection = _firestore.collection('learning_sessions');
    _flashcardsCollection = _firestore.collection('flashcards');
    _quizzesCollection = _firestore.collection('quizzes');
    _quizResultsCollection = _firestore.collection('quiz_results');
    _recommendationsCollection = _firestore.collection('study_recommendations');

    // Configure Firestore settings for better performance
    _configureFirestoreSettings();
  }

  /// Configures Firestore settings for optimal performance
  void _configureFirestoreSettings() {
    try {
      // Enable offline persistence for better user experience
      _firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );

      developer.log(
        'Firestore settings configured for optimal performance',
        name: 'AITutorRemoteDataSource',
      );
    } catch (e) {
      developer.log(
        'Failed to configure Firestore settings: $e',
        name: 'AITutorRemoteDataSource',
        error: e,
      );
    }
  }

  /// Enhanced operation wrapper with retry logic and error handling
  Future<T> _executeWithRetry<T>(
    Future<T> Function() operation,
    String operationName,
  ) async {
    int attempts = 0;
    Exception? lastException;

    while (attempts < _maxRetries) {
      try {
        final result = await operation().timeout(_operationTimeout);

        if (attempts > 0) {
          developer.log(
            '$operationName succeeded after ${attempts + 1} attempts',
            name: 'AITutorRemoteDataSource',
          );
        }

        return result;
      } on FirebaseException catch (e) {
        lastException = e;
        attempts++;

        // Don't retry on certain error codes
        if (_shouldNotRetry(e.code)) {
          developer.log(
            '$operationName failed with non-retryable error: ${e.code}',
            name: 'AITutorRemoteDataSource',
            error: e,
          );
          rethrow;
        }

        if (attempts < _maxRetries) {
          developer.log(
            '$operationName attempt $attempts failed, retrying in ${_retryDelay.inSeconds}s: ${e.code}',
            name: 'AITutorRemoteDataSource',
          );
          await Future.delayed(_retryDelay * attempts); // Exponential backoff
        }
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());
        attempts++;

        if (attempts < _maxRetries) {
          developer.log(
            '$operationName attempt $attempts failed, retrying: $e',
            name: 'AITutorRemoteDataSource',
          );
          await Future.delayed(_retryDelay * attempts);
        }
      }
    }

    developer.log(
      '$operationName failed after $_maxRetries attempts',
      name: 'AITutorRemoteDataSource',
      error: lastException,
    );

    throw lastException ??
        Exception('Operation failed after $_maxRetries attempts');
  }

  /// Determines if a Firebase error should not be retried
  bool _shouldNotRetry(String errorCode) {
    const nonRetryableErrors = {
      'permission-denied',
      'unauthenticated',
      'invalid-argument',
      'not-found',
      'already-exists',
      'failed-precondition',
      'out-of-range',
      'data-loss',
    };

    return nonRetryableErrors.contains(errorCode);
  }

  /// Enhanced batch operation for better performance
  Future<void> _executeBatchOperation(
    List<Future<void> Function(WriteBatch)> operations,
    String operationName,
  ) async {
    return _executeWithRetry(() async {
      final batch = _firestore.batch();

      for (final operation in operations) {
        await operation(batch);
      }

      await batch.commit();

      developer.log(
        '$operationName batch completed with ${operations.length} operations',
        name: 'AITutorRemoteDataSource',
      );
    }, operationName);
  }

  /// Save learning plan to Firestore with enhanced error handling
  Future<Either<Failure, void>> saveLearningPlan(LearningPlan plan) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final planData = plan.toJson();
      planData['userId'] = user.uid; // Ensure userId is set

      await _learningPlansCollection.doc(plan.id).set(planData);

      developer.log(
        'Learning plan saved successfully: ${plan.id}',
        name: 'AITutorRemoteDataSource',
      );

      return const Right(null);
    } catch (e) {
      developer.log(
        'Failed to save learning plan: $e',
        name: 'AITutorRemoteDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to save learning plan: $e'));
    }
  }

  /// Get learning plans for a user from Firestore
  Future<Either<Failure, List<LearningPlan>>> getUserLearningPlans(
    String userId,
  ) async {
    try {
      final querySnapshot = await _learningPlansCollection
          .where('userId', isEqualTo: userId)
          .orderBy('lastUpdated', descending: true)
          .get();

      final plans = querySnapshot.docs
          .map((doc) => LearningPlan.fromJson(doc.data()))
          .toList();

      return Right(plans);
    } catch (e) {
      developer.log(
        'Failed to get user learning plans: $e',
        name: 'AITutorRemoteDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to get learning plans: $e'));
    }
  }

  /// Save learning session to Firestore
  Future<Either<Failure, void>> saveLearningSession(
    LearningSession session,
  ) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final sessionData = session.toJson();
      sessionData['userId'] = user.uid; // Ensure userId is set

      await _learningSessionsCollection.doc(session.id).set(sessionData);

      developer.log(
        'Learning session saved successfully: ${session.id}',
        name: 'AITutorRemoteDataSource',
      );

      return const Right(null);
    } catch (e) {
      developer.log(
        'Failed to save learning session: $e',
        name: 'AITutorRemoteDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to save learning session: $e'));
    }
  }

  /// Get learning sessions for a user from Firestore
  Future<Either<Failure, List<LearningSession>>> getUserLearningSessions({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _learningSessionsCollection.where(
        'userId',
        isEqualTo: userId,
      );

      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }

      if (startDate != null) {
        query = query.where('startTime', isGreaterThanOrEqualTo: startDate);
      }

      if (endDate != null) {
        query = query.where('startTime', isLessThanOrEqualTo: endDate);
      }

      query = query.orderBy('startTime', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final querySnapshot = await query.get();
      final sessions = querySnapshot.docs
          .map((doc) => LearningSession.fromJson(doc.data()))
          .toList();

      return Right(sessions);
    } catch (e) {
      developer.log(
        'Failed to get user learning sessions: $e',
        name: 'AITutorRemoteDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to get learning sessions: $e'));
    }
  }

  /// Save flashcards to Firestore
  Future<Either<Failure, void>> saveFlashcards(
    List<Flashcard> flashcards,
  ) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final batch = _firestore.batch();

      for (final flashcard in flashcards) {
        final docRef = _flashcardsCollection.doc(flashcard.id);
        final flashcardData = flashcard.toJson();
        flashcardData['userId'] = user.uid; // Ensure userId is set
        batch.set(docRef, flashcardData);
      }

      await batch.commit();

      developer.log(
        'Flashcards saved successfully: ${flashcards.length} cards',
        name: 'AITutorRemoteDataSource',
      );

      return const Right(null);
    } catch (e) {
      developer.log(
        'Failed to save flashcards: $e',
        name: 'AITutorRemoteDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to save flashcards: $e'));
    }
  }

  /// Save quiz to Firestore
  Future<Either<Failure, void>> saveQuiz(Quiz quiz) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final quizData = quiz.toJson();
      quizData['userId'] = user.uid; // Ensure userId is set

      await _quizzesCollection.doc(quiz.id).set(quizData);

      developer.log(
        'Quiz saved successfully: ${quiz.id}',
        name: 'AITutorRemoteDataSource',
      );

      return const Right(null);
    } catch (e) {
      developer.log(
        'Failed to save quiz: $e',
        name: 'AITutorRemoteDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to save quiz: $e'));
    }
  }

  /// Save quiz result to Firestore
  Future<Either<Failure, void>> saveQuizResult(QuizResult result) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final resultData = result.toJson();
      resultData['userId'] = user.uid; // Ensure userId is set

      await _quizResultsCollection.doc(result.id).set(resultData);

      developer.log(
        'Quiz result saved successfully: ${result.id}',
        name: 'AITutorRemoteDataSource',
      );

      return const Right(null);
    } catch (e) {
      developer.log(
        'Failed to save quiz result: $e',
        name: 'AITutorRemoteDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to save quiz result: $e'));
    }
  }

  /// Get quiz results for a user from Firestore
  Future<Either<Failure, List<QuizResult>>> getUserQuizResults({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _quizResultsCollection.where(
        'userId',
        isEqualTo: userId,
      );

      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }

      if (startDate != null) {
        query = query.where('completedAt', isGreaterThanOrEqualTo: startDate);
      }

      if (endDate != null) {
        query = query.where('completedAt', isLessThanOrEqualTo: endDate);
      }

      query = query.orderBy('completedAt', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final querySnapshot = await query.get();
      final results = querySnapshot.docs
          .map((doc) => QuizResult.fromJson(doc.data()))
          .toList();

      return Right(results);
    } catch (e) {
      developer.log(
        'Failed to get user quiz results: $e',
        name: 'AITutorRemoteDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to get quiz results: $e'));
    }
  }

  /// Save study recommendations to Firestore
  Future<Either<Failure, void>> saveStudyRecommendations(
    List<StudyRecommendation> recommendations,
  ) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final batch = _firestore.batch();

      for (final recommendation in recommendations) {
        final docRef = _recommendationsCollection.doc(recommendation.id);
        final recommendationData = recommendation.toJson();
        recommendationData['userId'] = user.uid; // Ensure userId is set
        batch.set(docRef, recommendationData);
      }

      await batch.commit();

      developer.log(
        'Study recommendations saved successfully: ${recommendations.length} recommendations',
        name: 'AITutorRemoteDataSource',
      );

      return const Right(null);
    } catch (e) {
      developer.log(
        'Failed to save study recommendations: $e',
        name: 'AITutorRemoteDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to save study recommendations: $e'));
    }
  }

  /// Get study recommendations for a user from Firestore
  Future<Either<Failure, List<StudyRecommendation>>>
  getUserStudyRecommendations({
    required String userId,
    String? subject,
    bool? isActive,
    int? limit,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _recommendationsCollection.where(
        'userId',
        isEqualTo: userId,
      );

      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }

      if (isActive != null) {
        query = query.where('isActive', isEqualTo: isActive);
      }

      query = query.orderBy('createdAt', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final querySnapshot = await query.get();
      final recommendations = querySnapshot.docs
          .map((doc) => StudyRecommendation.fromJson(doc.data()))
          .toList();

      return Right(recommendations);
    } catch (e) {
      developer.log(
        'Failed to get user study recommendations: $e',
        name: 'AITutorRemoteDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to get study recommendations: $e'));
    }
  }

  /// Delete user data (for account deletion or data reset)
  Future<Either<Failure, void>> deleteUserData(String userId) async {
    try {
      final batch = _firestore.batch();

      // Delete learning plans
      final plansQuery = await _learningPlansCollection
          .where('userId', isEqualTo: userId)
          .get();
      for (final doc in plansQuery.docs) {
        batch.delete(doc.reference);
      }

      // Delete learning sessions
      final sessionsQuery = await _learningSessionsCollection
          .where('userId', isEqualTo: userId)
          .get();
      for (final doc in sessionsQuery.docs) {
        batch.delete(doc.reference);
      }

      // Delete quiz results
      final resultsQuery = await _quizResultsCollection
          .where('userId', isEqualTo: userId)
          .get();
      for (final doc in resultsQuery.docs) {
        batch.delete(doc.reference);
      }

      // Delete study recommendations
      final recommendationsQuery = await _recommendationsCollection
          .where('userId', isEqualTo: userId)
          .get();
      for (final doc in recommendationsQuery.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();

      developer.log(
        'User data deleted successfully for user: $userId',
        name: 'AITutorRemoteDataSource',
      );

      return const Right(null);
    } catch (e) {
      developer.log(
        'Failed to delete user data: $e',
        name: 'AITutorRemoteDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to delete user data: $e'));
    }
  }
}
