import 'package:dartz/dartz.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/entities/learning_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:developer' as developer;
import '../../../domain/entities/learning_session.dart';
import '../../../domain/entities/quiz.dart';
import '../../../../../core/error/failures.dart';

/// Remote data source for learning analytics using Firebase/Firestore
class LearningAnalyticsDataSource {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  // Firebase collections
  late final CollectionReference<Map<String, dynamic>> _analyticsCollection;
  late final CollectionReference<Map<String, dynamic>>
  _learningSessionsCollection;
  late final CollectionReference<Map<String, dynamic>> _quizResultsCollection;
  late final CollectionReference<Map<String, dynamic>> _progressCollection;

  LearningAnalyticsDataSource({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  }) : _firestore = firestore ?? FirebaseFirestore.instance,
       _auth = auth ?? FirebaseAuth.instance {
    // Initialize Firebase collections
    _analyticsCollection = _firestore.collection('learning_analytics');
    _learningSessionsCollection = _firestore.collection('learning_sessions');
    _quizResultsCollection = _firestore.collection('quiz_results');
    _progressCollection = _firestore.collection('learning_progress');
  }

  /// Generate and save learning analytics for a user
  Future<Either<Failure, LearningAnalytics>> generateLearningAnalytics({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Check user authentication
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      // Ensure user can only access their own analytics
      if (user.uid != userId) {
        return const Left(AuthFailure('Unauthorized access to user analytics'));
      }

      // Get learning sessions
      final sessionsResult = await _getUserLearningSessions(
        userId: userId,
        subject: subject,
        startDate: startDate,
        endDate: endDate,
      );

      if (sessionsResult.isLeft()) {
        return Left(sessionsResult.fold((l) => l, (r) => throw Exception()));
      }

      final sessions = sessionsResult.fold(
        (l) => <LearningSession>[],
        (r) => r,
      );

      // Get quiz results
      final quizResultsResult = await _getUserQuizResults(
        userId: userId,
        subject: subject,
        startDate: startDate,
        endDate: endDate,
      );

      if (quizResultsResult.isLeft()) {
        return Left(quizResultsResult.fold((l) => l, (r) => throw Exception()));
      }

      final quizResults = quizResultsResult.fold(
        (l) => <QuizResult>[],
        (r) => r,
      );

      // Calculate analytics
      final analytics = _calculateAnalytics(
        userId: userId,
        sessions: sessions,
        quizResults: quizResults,
        startDate: startDate,
        endDate: endDate,
        subject: subject,
      );

      // Save analytics to Firestore
      await _saveAnalytics(analytics);

      return Right(analytics);
    } catch (e) {
      developer.log(
        'Failed to generate learning analytics: $e',
        name: 'LearningAnalyticsDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to generate analytics: $e'));
    }
  }

  /// Get cached learning analytics from Firestore
  Future<Either<Failure, LearningAnalytics?>> getCachedAnalytics({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Check user authentication
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      // Ensure user can only access their own analytics
      if (user.uid != userId) {
        return const Left(AuthFailure('Unauthorized access to user analytics'));
      }

      final analyticsId = _generateAnalyticsId(
        userId: userId,
        subject: subject,
        startDate: startDate,
        endDate: endDate,
      );

      final doc = await _analyticsCollection.doc(analyticsId).get();

      if (!doc.exists || doc.data() == null) {
        return const Right(null);
      }

      final analytics = LearningAnalytics.fromJson(doc.data()!);

      // Check if analytics are still fresh (less than 1 hour old)
      final now = DateTime.now();
      final cacheAge = now.difference(analytics.generatedAt);

      if (cacheAge.inHours > 1) {
        // Analytics are stale, return null to trigger regeneration
        return const Right(null);
      }

      return Right(analytics);
    } catch (e) {
      developer.log(
        'Failed to get cached analytics: $e',
        name: 'LearningAnalyticsDataSource',
        error: e,
      );
      return Left(ServerFailure('Failed to get cached analytics: $e'));
    }
  }

  /// Get learning sessions for analytics calculation
  Future<Either<Failure, List<LearningSession>>> _getUserLearningSessions({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _learningSessionsCollection.where(
        'userId',
        isEqualTo: userId,
      );

      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }

      if (startDate != null) {
        query = query.where('startTime', isGreaterThanOrEqualTo: startDate);
      }

      if (endDate != null) {
        query = query.where('startTime', isLessThanOrEqualTo: endDate);
      }

      final querySnapshot = await query.get();
      final sessions = querySnapshot.docs
          .map((doc) => LearningSession.fromJson(doc.data()))
          .toList();

      return Right(sessions);
    } catch (e) {
      return Left(ServerFailure('Failed to get learning sessions: $e'));
    }
  }

  /// Get quiz results for analytics calculation
  Future<Either<Failure, List<QuizResult>>> _getUserQuizResults({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _quizResultsCollection.where(
        'userId',
        isEqualTo: userId,
      );

      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }

      if (startDate != null) {
        query = query.where('completedAt', isGreaterThanOrEqualTo: startDate);
      }

      if (endDate != null) {
        query = query.where('completedAt', isLessThanOrEqualTo: endDate);
      }

      final querySnapshot = await query.get();
      final results = querySnapshot.docs
          .map((doc) => QuizResult.fromJson(doc.data()))
          .toList();

      return Right(results);
    } catch (e) {
      return Left(ServerFailure('Failed to get quiz results: $e'));
    }
  }

  /// Calculate learning analytics from sessions and quiz results
  LearningAnalytics _calculateAnalytics({
    required String userId,
    required List<LearningSession> sessions,
    required List<QuizResult> quizResults,
    DateTime? startDate,
    DateTime? endDate,
    String? subject,
  }) {
    final now = DateTime.now();
    final effectiveStartDate =
        startDate ?? now.subtract(const Duration(days: 30));
    final effectiveEndDate = endDate ?? now;

    // Calculate total study time
    final totalStudyTime = sessions
        .where((s) => s.duration != null)
        .map((s) => s.duration!.inMinutes)
        .fold(0, (sum, minutes) => sum + minutes);

    // Calculate average session duration
    final averageSessionDuration = sessions.isNotEmpty
        ? totalStudyTime / sessions.length
        : 0.0;

    // Calculate quiz performance
    final averageQuizScore = quizResults.isNotEmpty
        ? quizResults.map((r) => r.score).reduce((a, b) => a + b) /
              quizResults.length
        : 0.0;

    // Calculate learning streak
    final studyDates =
        sessions
            .map(
              (s) => DateTime(
                s.startTime.year,
                s.startTime.month,
                s.startTime.day,
              ),
            )
            .toSet()
            .toList()
          ..sort();

    int currentStreak = 0;
    if (studyDates.isNotEmpty) {
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = today.subtract(const Duration(days: 1));

      // Check if studied today or yesterday
      if (studyDates.contains(today) || studyDates.contains(yesterday)) {
        DateTime checkDate = studyDates.contains(today) ? today : yesterday;

        while (studyDates.contains(checkDate)) {
          currentStreak++;
          checkDate = checkDate.subtract(const Duration(days: 1));
        }
      }
    }

    // Calculate weekly activity
    final weeklyActivity = <String, int>{};
    for (int i = 0; i < 7; i++) {
      final date = now.subtract(Duration(days: i));
      final dateKey =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

      final dayMinutes = sessions
          .where(
            (s) =>
                s.startTime.year == date.year &&
                s.startTime.month == date.month &&
                s.startTime.day == date.day,
          )
          .where((s) => s.duration != null)
          .map((s) => s.duration!.inMinutes)
          .fold(0, (sum, minutes) => sum + minutes);

      weeklyActivity[dateKey] = dayMinutes;
    }

    // Calculate subject breakdown
    final subjectBreakdown = <String, Map<String, dynamic>>{};
    final subjectSessions = <String, List<LearningSession>>{};

    for (final session in sessions) {
      subjectSessions.putIfAbsent(session.subject, () => []);
      subjectSessions[session.subject]!.add(session);
    }

    for (final entry in subjectSessions.entries) {
      final subjectName = entry.key;
      final subjectSessionList = entry.value;

      final subjectTime = subjectSessionList
          .where((s) => s.duration != null)
          .map((s) => s.duration!.inMinutes)
          .fold(0, (sum, minutes) => sum + minutes);

      final subjectQuizResults = quizResults
          .where((r) => r.subject == subjectName)
          .toList();

      final subjectAvgScore = subjectQuizResults.isNotEmpty
          ? subjectQuizResults.map((r) => r.score).reduce((a, b) => a + b) /
                subjectQuizResults.length
          : 0.0;

      subjectBreakdown[subjectName] = {
        'totalTime': subjectTime,
        'sessionCount': subjectSessionList.length,
        'averageScore': subjectAvgScore,
        'quizCount': subjectQuizResults.length,
      };
    }

    return LearningAnalytics(
      id: _generateAnalyticsId(
        userId: userId,
        subject: subject,
        startDate: effectiveStartDate,
        endDate: effectiveEndDate,
      ),
      userId: userId,
      subject: subject,
      startDate: effectiveStartDate,
      endDate: effectiveEndDate,
      totalStudyTime: totalStudyTime,
      totalSessions: sessions.length,
      averageSessionDuration: averageSessionDuration,
      totalQuizzes: quizResults.length,
      averageQuizScore: averageQuizScore,
      currentStreak: currentStreak,
      weeklyActivity: weeklyActivity,
      subjectBreakdown: subjectBreakdown,
      generatedAt: now,
      metadata: {
        'version': '1.0',
        'calculationMethod': 'firestore_aggregation',
      },
    );
  }

  /// Save analytics to Firestore
  Future<void> _saveAnalytics(LearningAnalytics analytics) async {
    await _analyticsCollection.doc(analytics.id).set(analytics.toJson());
  }

  /// Generate a unique ID for analytics based on parameters
  String _generateAnalyticsId({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    final subjectPart = subject ?? 'all';
    final startPart = startDate?.millisecondsSinceEpoch.toString() ?? 'null';
    final endPart = endDate?.millisecondsSinceEpoch.toString() ?? 'null';

    return '${userId}_${subjectPart}_${startPart}_$endPart';
  }
}
