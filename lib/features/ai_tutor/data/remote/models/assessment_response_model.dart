import '../../../domain/entities/quiz.dart';

/// Response model for assessment and analytics API calls
/// Handles JSON serialization for Firebase and AI API responses
class AssessmentResponseModel {
  final String id;
  final String userId;
  final String subject;
  final String topic;
  final List<String> identifiedGaps;
  final List<String> strengths;
  final List<RecommendationResponseModel> recommendations;
  final double overallScore;
  final DateTime assessmentDate;
  final Map<String, dynamic> metadata;

  AssessmentResponseModel({
    required this.id,
    required this.userId,
    required this.subject,
    required this.topic,
    required this.identifiedGaps,
    required this.strengths,
    required this.recommendations,
    required this.overallScore,
    required this.assessmentDate,
    required this.metadata,
  });

  /// Converts to JSON for API requests/responses
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'subject': subject,
      'topic': topic,
      'identifiedGaps': identifiedGaps,
      'strengths': strengths,
      'recommendations': recommendations.map((r) => r.toJson()).toList(),
      'overallScore': overallScore,
      'assessmentDate': assessmentDate.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Creates model from JSON response
  factory AssessmentResponseModel.fromJson(Map<String, dynamic> json) {
    return AssessmentResponseModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      subject: json['subject'] as String,
      topic: json['topic'] as String,
      identifiedGaps: List<String>.from(json['identifiedGaps'] ?? []),
      strengths: List<String>.from(json['strengths'] ?? []),
      recommendations:
          (json['recommendations'] as List<dynamic>?)
              ?.map(
                (r) => RecommendationResponseModel.fromJson(
                  r as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      overallScore: (json['overallScore'] as num).toDouble(),
      assessmentDate: DateTime.parse(json['assessmentDate'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'AssessmentResponseModel(id: $id, subject: $subject, overallScore: $overallScore)';
  }
}

/// Response model for study recommendations
class RecommendationResponseModel {
  final String id;
  final String title;
  final String description;
  final String type;
  final String subject;
  final int priority;
  final int estimatedMinutes;
  final Map<String, dynamic> metadata;

  RecommendationResponseModel({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.subject,
    required this.priority,
    required this.estimatedMinutes,
    required this.metadata,
  });

  /// Converts to JSON for API requests/responses
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type,
      'subject': subject,
      'priority': priority,
      'estimatedMinutes': estimatedMinutes,
      'metadata': metadata,
    };
  }

  /// Creates model from JSON response
  factory RecommendationResponseModel.fromJson(Map<String, dynamic> json) {
    return RecommendationResponseModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      subject: json['subject'] as String,
      priority: json['priority'] as int,
      estimatedMinutes: json['estimatedMinutes'] as int,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'RecommendationResponseModel(id: $id, title: $title, type: $type)';
  }
}

/// Response model for quiz results and analytics
class QuizResultResponseModel {
  final String id;
  final String quizId;
  final String userId;
  final List<QuizAnswerResponseModel> answers;
  final double score;
  final int totalQuestions;
  final int correctAnswers;
  final DateTime completedAt;
  final int timeSpentSeconds;
  final Map<String, dynamic> analytics;
  final DateTime startTime;
  final String subject;
  final String topic;
  final int totalPoints;

  QuizResultResponseModel({
    required this.id,
    required this.quizId,
    required this.userId,
    required this.answers,
    required this.score,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.completedAt,
    required this.timeSpentSeconds,
    required this.analytics,
    required this.startTime,
    required this.subject,
    required this.topic,
    required this.totalPoints,
  });

  /// Converts domain entity to response model
  factory QuizResultResponseModel.fromEntity(QuizResult result) {
    return QuizResultResponseModel(
      id: result.id,
      quizId: result.quizId,
      userId: result.userId,
      answers: result.answers
          .map((a) => QuizAnswerResponseModel.fromEntity(a))
          .toList(),
      score: result.score,
      totalQuestions: result.totalQuestions,
      correctAnswers: result.correctAnswers,
      completedAt: result.completedAt,
      timeSpentSeconds: result.timeSpent.inSeconds,
      analytics: {
        'averageTimePerQuestion':
            result.timeSpent.inSeconds / result.totalQuestions,
        'accuracy': result.score / 100.0,
        'completionRate': 1.0,
      },
      startTime: result.startTime,
      subject: result.subject,
      topic: result.topic,
      totalPoints: result.totalPoints,
    );
  }

  /// Converts response model to domain entity
  QuizResult toEntity() {
    return QuizResult(
      id: id,
      quizId: quizId,
      userId: userId,
      answers: answers.map((a) => a.toEntity()).toList(),
      score: score,
      totalPoints: totalPoints,
      startTime: startTime,
      endTime: completedAt,
      timeSpent: Duration(seconds: timeSpentSeconds),
      subject: subject,
      topic: topic,
      completedAt: completedAt,
    );
  }

  /// Converts to JSON for API requests/responses
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quizId': quizId,
      'userId': userId,
      'answers': answers.map((a) => a.toJson()).toList(),
      'score': score,
      'totalQuestions': totalQuestions,
      'correctAnswers': correctAnswers,
      'completedAt': completedAt.toIso8601String(),
      'timeSpentSeconds': timeSpentSeconds,
      'analytics': analytics,
      'startTime': startTime.toIso8601String(),
      'subject': subject,
      'topic': topic,
      'totalPoints': totalPoints,
    };
  }

  /// Creates model from JSON response
  factory QuizResultResponseModel.fromJson(Map<String, dynamic> json) {
    return QuizResultResponseModel(
      id: json['id'] as String,
      quizId: json['quizId'] as String,
      userId: json['userId'] as String,
      answers: (json['answers'] as List<dynamic>)
          .map(
            (a) => QuizAnswerResponseModel.fromJson(a as Map<String, dynamic>),
          )
          .toList(),
      score: (json['score'] as num).toDouble(),
      totalQuestions: json['totalQuestions'] as int,
      correctAnswers: json['correctAnswers'] as int,
      completedAt: DateTime.parse(json['completedAt'] as String),
      timeSpentSeconds: json['timeSpentSeconds'] as int,
      analytics: Map<String, dynamic>.from(json['analytics'] ?? {}),
      startTime: DateTime.parse(
        json['startTime'] as String? ?? json['completedAt'] as String,
      ),
      subject: json['subject'] as String? ?? 'General',
      topic: json['topic'] as String? ?? 'General',
      totalPoints:
          json['totalPoints'] as int? ?? (json['totalQuestions'] as int) * 10,
    );
  }

  @override
  String toString() {
    return 'QuizResultResponseModel(id: $id, score: $score, correctAnswers: $correctAnswers/$totalQuestions)';
  }
}

/// Response model for quiz answers
class QuizAnswerResponseModel {
  final String questionId;
  final List<String> selectedAnswers;
  final bool isCorrect;
  final String concept;
  final int timeSpentSeconds;
  final int pointsEarned;

  QuizAnswerResponseModel({
    required this.questionId,
    required this.selectedAnswers,
    required this.isCorrect,
    required this.concept,
    required this.timeSpentSeconds,
    required this.pointsEarned,
  });

  /// Converts domain entity to response model
  factory QuizAnswerResponseModel.fromEntity(QuizAnswer answer) {
    return QuizAnswerResponseModel(
      questionId: answer.questionId,
      selectedAnswers: answer.userAnswers,
      isCorrect: answer.isCorrect,
      concept: answer.concept,
      timeSpentSeconds:
          0, // Default value since QuizAnswer doesn't have timeSpent
      pointsEarned: answer.pointsEarned,
    );
  }

  /// Converts response model to domain entity
  QuizAnswer toEntity() {
    return QuizAnswer(
      questionId: questionId,
      userAnswers: selectedAnswers,
      isCorrect: isCorrect,
      pointsEarned: pointsEarned,
      concept: concept,
    );
  }

  /// Converts to JSON for API requests/responses
  Map<String, dynamic> toJson() {
    return {
      'questionId': questionId,
      'selectedAnswers': selectedAnswers,
      'isCorrect': isCorrect,
      'concept': concept,
      'timeSpentSeconds': timeSpentSeconds,
      'pointsEarned': pointsEarned,
    };
  }

  /// Creates model from JSON response
  factory QuizAnswerResponseModel.fromJson(Map<String, dynamic> json) {
    return QuizAnswerResponseModel(
      questionId: json['questionId'] as String,
      selectedAnswers: List<String>.from(json['selectedAnswers'] ?? []),
      isCorrect: json['isCorrect'] as bool,
      concept: json['concept'] as String,
      timeSpentSeconds: json['timeSpentSeconds'] as int,
      pointsEarned:
          json['pointsEarned'] as int? ?? (json['isCorrect'] as bool ? 10 : 0),
    );
  }

  @override
  String toString() {
    return 'QuizAnswerResponseModel(questionId: $questionId, isCorrect: $isCorrect, concept: $concept)';
  }
}
