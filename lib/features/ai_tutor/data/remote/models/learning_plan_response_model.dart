import '../../../domain/entities/learning_progress.dart';
import '../../../domain/entities/flashcard.dart'; // For DifficultyLevel

/// Response model for learning plan API calls
/// Handles JSON serialization for Firebase and AI API responses
class LearningPlanResponseModel {
  final String id;
  final String userId;
  final String subject;
  final String title;
  final String description;
  final List<LearningMilestoneResponseModel> milestones;
  final DateTime startDate;
  final DateTime targetEndDate;
  final String difficulty;
  final List<String> learningGoals;
  final Map<String, dynamic> preferences;
  final DateTime createdAt;
  final DateTime lastUpdated;

  LearningPlanResponseModel({
    required this.id,
    required this.userId,
    required this.subject,
    required this.title,
    required this.description,
    required this.milestones,
    required this.startDate,
    required this.targetEndDate,
    required this.difficulty,
    required this.learningGoals,
    required this.preferences,
    required this.createdAt,
    required this.lastUpdated,
  });

  /// Converts domain entity to response model
  factory LearningPlanResponseModel.fromEntity(LearningPlan plan) {
    return LearningPlanResponseModel(
      id: plan.id,
      userId: plan.userId,
      subject: plan.subject,
      title: plan.title,
      description: plan.description,
      milestones: plan.milestones
          .map((m) => LearningMilestoneResponseModel.fromEntity(m))
          .toList(),
      startDate: plan.startDate,
      targetEndDate: plan.targetEndDate,
      difficulty: plan.difficulty.toString(),
      learningGoals: plan.learningGoals,
      preferences: plan.preferences,
      createdAt: plan.createdAt,
      lastUpdated: plan.lastUpdated,
    );
  }

  /// Converts response model to domain entity
  LearningPlan toEntity() {
    return LearningPlan(
      id: id,
      userId: userId,
      subject: subject,
      title: title,
      description: description,
      milestones: milestones.map((m) => m.toEntity()).toList(),
      startDate: startDate,
      targetEndDate: targetEndDate,
      difficulty: _parseDifficultyLevel(difficulty),
      learningGoals: learningGoals,
      preferences: preferences,
      createdAt: createdAt,
      lastUpdated: lastUpdated,
    );
  }

  /// Converts to JSON for API requests/responses
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'subject': subject,
      'title': title,
      'description': description,
      'milestones': milestones.map((m) => m.toJson()).toList(),
      'startDate': startDate.toIso8601String(),
      'targetEndDate': targetEndDate.toIso8601String(),
      'difficulty': difficulty,
      'learningGoals': learningGoals,
      'preferences': preferences,
      'createdAt': createdAt.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Creates model from JSON response
  factory LearningPlanResponseModel.fromJson(Map<String, dynamic> json) {
    return LearningPlanResponseModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      subject: json['subject'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      milestones: (json['milestones'] as List<dynamic>)
          .map(
            (m) => LearningMilestoneResponseModel.fromJson(
              m as Map<String, dynamic>,
            ),
          )
          .toList(),
      startDate: DateTime.parse(json['startDate'] as String),
      targetEndDate: DateTime.parse(json['targetEndDate'] as String),
      difficulty: json['difficulty'] as String,
      learningGoals: List<String>.from(json['learningGoals'] ?? []),
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  /// Helper method to parse difficulty level
  DifficultyLevel _parseDifficultyLevel(String difficulty) {
    switch (difficulty) {
      case 'DifficultyLevel.easy':
        return DifficultyLevel.easy;
      case 'DifficultyLevel.medium':
        return DifficultyLevel.medium;
      case 'DifficultyLevel.hard':
        return DifficultyLevel.hard;
      case 'DifficultyLevel.expert':
        return DifficultyLevel.expert;
      default:
        return DifficultyLevel.medium;
    }
  }

  @override
  String toString() {
    return 'LearningPlanResponseModel(id: $id, title: $title, subject: $subject)';
  }
}

/// Response model for learning milestones
class LearningMilestoneResponseModel {
  final String id;
  final String title;
  final String description;
  final List<String> concepts;
  final DateTime targetDate;
  final bool isCompleted;
  final DateTime? completedAt;
  final List<String> resources;
  final Map<String, dynamic> metadata;

  LearningMilestoneResponseModel({
    required this.id,
    required this.title,
    required this.description,
    required this.concepts,
    required this.targetDate,
    required this.isCompleted,
    this.completedAt,
    required this.resources,
    required this.metadata,
  });

  /// Converts domain entity to response model
  factory LearningMilestoneResponseModel.fromEntity(
    LearningMilestone milestone,
  ) {
    return LearningMilestoneResponseModel(
      id: milestone.id,
      title: milestone.title,
      description: milestone.description,
      concepts: milestone.concepts,
      targetDate: milestone.targetDate,
      isCompleted: milestone.isCompleted,
      completedAt: milestone.completedAt,
      resources: milestone.resources,
      metadata: milestone.metadata,
    );
  }

  /// Converts response model to domain entity
  LearningMilestone toEntity() {
    return LearningMilestone(
      id: id,
      title: title,
      description: description,
      concepts: concepts,
      targetDate: targetDate,
      isCompleted: isCompleted,
      completedAt: completedAt,
      resources: resources,
      metadata: metadata,
    );
  }

  /// Converts to JSON for API requests/responses
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'concepts': concepts,
      'targetDate': targetDate.toIso8601String(),
      'isCompleted': isCompleted,
      'completedAt': completedAt?.toIso8601String(),
      'resources': resources,
      'metadata': metadata,
    };
  }

  /// Creates model from JSON response
  factory LearningMilestoneResponseModel.fromJson(Map<String, dynamic> json) {
    return LearningMilestoneResponseModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      concepts: List<String>.from(json['concepts'] ?? []),
      targetDate: DateTime.parse(json['targetDate'] as String),
      isCompleted: json['isCompleted'] as bool,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      resources: List<String>.from(json['resources'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'LearningMilestoneResponseModel(id: $id, title: $title, isCompleted: $isCompleted)';
  }
}
