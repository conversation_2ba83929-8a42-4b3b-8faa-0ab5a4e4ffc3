import 'package:dartz/dartz.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:developer' as developer;

import '../../domain/entities/learning_session.dart';
import '../../../../core/error/failures.dart';

/// Repository for managing learning sessions with Firebase integration
/// Tracks user study sessions, duration, and activity for analytics
class LearningSessionRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  late final CollectionReference<Map<String, dynamic>> _sessionsCollection;

  LearningSessionRepository({FirebaseFirestore? firestore, FirebaseAuth? auth})
    : _firestore = firestore ?? FirebaseFirestore.instance,
      _auth = auth ?? FirebaseAuth.instance {
    _sessionsCollection = _firestore.collection('learning_sessions');
  }

  /// Start a new learning session
  Future<Either<Failure, LearningSession>> startSession({
    required String subject,
    required String topic,
    required String activityType,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final session = LearningSession(
        id: 'session_${DateTime.now().millisecondsSinceEpoch}',
        userId: user.uid,
        subject: subject,
        topic: topic,
        startTime: DateTime.now(),
        endTime: null,
        status: LearningSessionStatus.active,
        conceptsCovered: [], // Will be updated when session ends
        comprehensionScore: 0.0, // Will be calculated when session ends
        metadata: {'activityType': activityType, ...metadata ?? {}},
      );

      await _sessionsCollection.doc(session.id).set(session.toJson());

      developer.log(
        'Learning session started: ${session.id}',
        name: 'LearningSessionRepository',
      );

      return Right(session);
    } catch (e) {
      developer.log(
        'Failed to start learning session: $e',
        name: 'LearningSessionRepository',
        error: e,
      );
      return Left(ServerFailure('Failed to start session: $e'));
    }
  }

  /// End a learning session
  Future<Either<Failure, LearningSession>> endSession({
    required String sessionId,
    Map<String, dynamic>? additionalMetadata,
  }) async {
    try {
      final doc = await _sessionsCollection.doc(sessionId).get();
      if (!doc.exists || doc.data() == null) {
        return const Left(DataFailure('Session not found'));
      }

      final session = LearningSession.fromJson(doc.data()!);
      final endTime = DateTime.now();
      final duration = endTime.difference(session.startTime);

      final updatedMetadata = Map<String, dynamic>.from(session.metadata);
      if (additionalMetadata != null) {
        updatedMetadata.addAll(additionalMetadata);
      }

      final updatedSession = session.copyWith(
        endTime: endTime,
        status: LearningSessionStatus.completed,
        metadata: updatedMetadata,
      );

      await _sessionsCollection.doc(sessionId).update({
        'endTime': endTime.toIso8601String(),
        'duration': duration.inMilliseconds,
        'metadata': updatedMetadata,
      });

      developer.log(
        'Learning session ended: $sessionId, Duration: ${duration.inMinutes} minutes',
        name: 'LearningSessionRepository',
      );

      return Right(updatedSession);
    } catch (e) {
      developer.log(
        'Failed to end learning session: $e',
        name: 'LearningSessionRepository',
        error: e,
      );
      return Left(ServerFailure('Failed to end session: $e'));
    }
  }

  /// Get recent learning sessions for a user
  Future<Either<Failure, List<LearningSession>>> getRecentSessions({
    String? userId,
    String? subject,
    int limit = 10,
    DateTime? since,
  }) async {
    try {
      final user = _auth.currentUser;
      final targetUserId = userId ?? user?.uid;

      if (targetUserId == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      Query<Map<String, dynamic>> query = _sessionsCollection
          .where('userId', isEqualTo: targetUserId)
          .orderBy('startTime', descending: true)
          .limit(limit);

      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }

      if (since != null) {
        query = query.where('startTime', isGreaterThanOrEqualTo: since);
      }

      final querySnapshot = await query.get();
      final sessions = querySnapshot.docs
          .map((doc) => LearningSession.fromJson(doc.data()))
          .toList();

      return Right(sessions);
    } catch (e) {
      developer.log(
        'Failed to get recent sessions: $e',
        name: 'LearningSessionRepository',
        error: e,
      );
      return Left(ServerFailure('Failed to get sessions: $e'));
    }
  }

  /// Get learning sessions for a specific date range
  Future<Either<Failure, List<LearningSession>>> getSessionsInRange({
    String? userId,
    required DateTime startDate,
    required DateTime endDate,
    String? subject,
  }) async {
    try {
      final user = _auth.currentUser;
      final targetUserId = userId ?? user?.uid;

      if (targetUserId == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      Query<Map<String, dynamic>> query = _sessionsCollection
          .where('userId', isEqualTo: targetUserId)
          .where('startTime', isGreaterThanOrEqualTo: startDate)
          .where('startTime', isLessThanOrEqualTo: endDate)
          .orderBy('startTime', descending: true);

      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }

      final querySnapshot = await query.get();
      final sessions = querySnapshot.docs
          .map((doc) => LearningSession.fromJson(doc.data()))
          .toList();

      return Right(sessions);
    } catch (e) {
      developer.log(
        'Failed to get sessions in range: $e',
        name: 'LearningSessionRepository',
        error: e,
      );
      return Left(ServerFailure('Failed to get sessions: $e'));
    }
  }

  /// Get learning session statistics
  Future<Either<Failure, Map<String, dynamic>>> getSessionStats({
    String? userId,
    DateTime? since,
  }) async {
    try {
      final user = _auth.currentUser;
      final targetUserId = userId ?? user?.uid;

      if (targetUserId == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final sinceDate =
          since ?? DateTime.now().subtract(const Duration(days: 30));

      final sessionsResult = await getSessionsInRange(
        userId: targetUserId,
        startDate: sinceDate,
        endDate: DateTime.now(),
      );

      if (sessionsResult.isLeft()) {
        return Left(sessionsResult.fold((l) => l, (r) => throw Exception()));
      }

      final sessions = sessionsResult.fold(
        (l) => <LearningSession>[],
        (r) => r,
      );

      // Calculate statistics
      final totalSessions = sessions.length;
      final completedSessions = sessions
          .where((s) => s.duration != null)
          .length;
      final totalStudyTime = sessions
          .where((s) => s.duration != null)
          .map((s) => s.duration!.inMinutes)
          .fold(0, (total, minutes) => total + minutes);

      final averageSessionDuration = completedSessions > 0
          ? totalStudyTime / completedSessions
          : 0.0;

      // Subject breakdown
      final subjectBreakdown = <String, Map<String, dynamic>>{};
      for (final session in sessions) {
        if (!subjectBreakdown.containsKey(session.subject)) {
          subjectBreakdown[session.subject] = {
            'sessionCount': 0,
            'totalTime': 0,
            'averageTime': 0.0,
          };
        }

        subjectBreakdown[session.subject]!['sessionCount'] =
            (subjectBreakdown[session.subject]!['sessionCount'] as int) + 1;

        if (session.duration != null) {
          subjectBreakdown[session.subject]!['totalTime'] =
              (subjectBreakdown[session.subject]!['totalTime'] as int) +
              session.duration!.inMinutes;
        }
      }

      // Calculate average times
      for (final entry in subjectBreakdown.entries) {
        final data = entry.value;
        final sessionCount = data['sessionCount'] as int;
        final totalTime = data['totalTime'] as int;
        data['averageTime'] = sessionCount > 0 ? totalTime / sessionCount : 0.0;
      }

      final stats = {
        'totalSessions': totalSessions,
        'completedSessions': completedSessions,
        'totalStudyTime': totalStudyTime,
        'averageSessionDuration': averageSessionDuration,
        'subjectBreakdown': subjectBreakdown,
        'period': {
          'startDate': sinceDate.toIso8601String(),
          'endDate': DateTime.now().toIso8601String(),
        },
      };

      return Right(stats);
    } catch (e) {
      developer.log(
        'Failed to get session stats: $e',
        name: 'LearningSessionRepository',
        error: e,
      );
      return Left(ServerFailure('Failed to get session stats: $e'));
    }
  }

  /// Delete a learning session
  Future<Either<Failure, void>> deleteSession(String sessionId) async {
    try {
      await _sessionsCollection.doc(sessionId).delete();

      developer.log(
        'Learning session deleted: $sessionId',
        name: 'LearningSessionRepository',
      );

      return const Right(null);
    } catch (e) {
      developer.log(
        'Failed to delete learning session: $e',
        name: 'LearningSessionRepository',
        error: e,
      );
      return Left(ServerFailure('Failed to delete session: $e'));
    }
  }
}
