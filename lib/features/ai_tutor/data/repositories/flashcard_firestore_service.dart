import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/flashcard.dart';
import '../../../../core/error/failures.dart';
import 'package:dartz/dartz.dart';

/// Service class for Firestore operations related to Flashcards
class FlashcardFirestoreService {
  final FirebaseFirestore _firestore;
  final CollectionReference<Map<String, dynamic>> _flashcardsCollection;
  final CollectionReference<Map<String, dynamic>> _reviewsCollection;

  FlashcardFirestoreService({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance,
        _flashcardsCollection = (firestore ?? FirebaseFirestore.instance).collection('flashcards'),
        _reviewsCollection = (firestore ?? FirebaseFirestore.instance).collection('flashcard_reviews');

  Future<Either<Failure, void>> saveFlashcard(Flashcard flashcard) async {
    try {
      await _flashcardsCollection.doc(flashcard.id).set(flashcard.toJson());
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to save flashcard: ${e.toString()}'));
    }
  }

  Future<Either<Failure, void>> saveFlashcards(List<Flashcard> flashcards) async {
    final batch = _firestore.batch();
    try {
      for (final flashcard in flashcards) {
        final docRef = _flashcardsCollection.doc(flashcard.id);
        batch.set(docRef, flashcard.toJson());
      }
      await batch.commit();
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to save flashcards batch: ${e.toString()}'));
    }
  }

  Future<Either<Failure, Flashcard?>> getFlashcard(String id) async {
    try {
      final doc = await _flashcardsCollection.doc(id).get();
      if (!doc.exists) {
        return const Right(null);
      }
      final data = doc.data()!;
      final flashcard = Flashcard.fromJson(data);
      return Right(flashcard);
    } catch (e) {
      return Left(DataFailure('Failed to get flashcard: ${e.toString()}'));
    }
  }

  Future<Either<Failure, List<Flashcard>>> getFlashcardsByTopic({
    required String topic,
    String? subject,
    DifficultyLevel? difficulty,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _flashcardsCollection.where('topic', isEqualTo: topic);
      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }
      if (difficulty != null) {
        query = query.where('difficulty', isEqualTo: difficulty.toString());
      }
      final querySnapshot = await query.get();
      final flashcards = querySnapshot.docs.map((doc) => Flashcard.fromJson(doc.data())).toList();
      return Right(flashcards);
    } catch (e) {
      return Left(DataFailure('Failed to get flashcards by topic: ${e.toString()}'));
    }
  }

  Future<Either<Failure, List<Flashcard>>> getFlashcardsBySubject(String subject) async {
    try {
      final querySnapshot = await _flashcardsCollection.where('subject', isEqualTo: subject).get();
      final flashcards = querySnapshot.docs.map((doc) => Flashcard.fromJson(doc.data())).toList();
      return Right(flashcards);
    } catch (e) {
      return Left(DataFailure('Failed to get flashcards by subject: ${e.toString()}'));
    }
  }

  Future<Either<Failure, List<Flashcard>>> getDueFlashcards({
    String? userId,
    String? subject,
    String? topic,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _flashcardsCollection.where('nextReview', isLessThanOrEqualTo: DateTime.now());
      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }
      if (topic != null) {
        query = query.where('topic', isEqualTo: topic);
      }
      final querySnapshot = await query.get();
      final flashcards = querySnapshot.docs.map((doc) => Flashcard.fromJson(doc.data())).toList();
      return Right(flashcards);
    } catch (e) {
      return Left(DataFailure('Failed to get due flashcards: ${e.toString()}'));
    }
  }

  Future<Either<Failure, void>> deleteFlashcard(String id) async {
    try {
      await _flashcardsCollection.doc(id).delete();
      final reviewsQuery = await _reviewsCollection.where('flashcardId', isEqualTo: id).get();
      for (final doc in reviewsQuery.docs) {
        await _reviewsCollection.doc(doc.id).delete();
      }
      return const Right(null);
    } catch (e) {
      return Left(DataFailure('Failed to delete flashcard: ${e.toString()}'));
    }
  }

  Future<Either<Failure, void>> deleteFlashcards(List<String> ids) async {
    final batch = _firestore.batch();
    try {
      for (final id in ids) {
        final docRef = _flashcardsCollection.doc(id);
        batch.delete(docRef);
        final reviewsQuery = await _reviewsCollection.where('flashcardId', isEqualTo: id).get();
        for (final doc in reviewsQuery.docs) {
          batch.delete(_reviewsCollection.doc(doc.id));
        }
      }
      await batch.commit();
      return const Right(null);
    } catch (e) {
      return Left(DataFailure('Failed to delete flashcards batch: ${e.toString()}'));
    }
  }

  Future<Either<Failure, void>> toggleFlashcardFavorite(String flashcardId) async {
    try {
      final docRef = _flashcardsCollection.doc(flashcardId);
      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(DataFailure('Flashcard not found'));
      }
      final data = doc.data()!;
      final flashcard = Flashcard.fromJson(data);
      final tags = List<String>.from(flashcard.tags);
      if (tags.contains('favorite')) {
        tags.remove('favorite');
      } else {
        tags.add('favorite');
      }
      await docRef.update({'tags': tags});
      return const Right(null);
    } catch (e) {
      return Left(DataFailure('Failed to toggle favorite: ${e.toString()}'));
    }
  }
}
