import 'package:dartz/dartz.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../domain/entities/flashcard.dart';
import '../../domain/repositories/flashcard_repository.dart';
import '../../domain/services/spaced_repetition_service.dart';
import '../../../../core/error/failures.dart';
import 'dart:convert';

/// Firestore implementation of FlashcardRepository
class RealFlashcardRepository implements FlashcardRepository {
  final FirebaseFirestore _firestore;
  final CollectionReference<Map<String, dynamic>> _flashcardsCollection;
  final CollectionReference<Map<String, dynamic>> _reviewsCollection;
  final SpacedRepetitionService _spacedRepetitionService;

  RealFlashcardRepository({
    FirebaseFirestore? firestore,
    SpacedRepetitionService? spacedRepetitionService,
  }) : _firestore = firestore ?? FirebaseFirestore.instance,
       _flashcardsCollection = (firestore ?? FirebaseFirestore.instance)
           .collection('flashcards'),
       _reviewsCollection = (firestore ?? FirebaseFirestore.instance)
           .collection('flashcard_reviews'),
       _spacedRepetitionService =
           spacedRepetitionService ?? SpacedRepetitionService();

  @override
  Future<Either<Failure, void>> saveFlashcard(Flashcard flashcard) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return Left(
          CacheFailure('User must be authenticated to save flashcards'),
        );
      }

      final flashcardData = flashcard.toJson();
      flashcardData['userId'] = currentUser.uid; // Ensure userId is set

      await _flashcardsCollection.doc(flashcard.id).set(flashcardData);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to save flashcard: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> saveFlashcards(
    List<Flashcard> flashcards,
  ) async {
    final batch = _firestore.batch();
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return Left(
          CacheFailure('User must be authenticated to save flashcards'),
        );
      }

      for (final flashcard in flashcards) {
        final docRef = _flashcardsCollection.doc(flashcard.id);
        final flashcardData = flashcard.toJson();
        flashcardData['userId'] = currentUser.uid; // Ensure userId is set
        batch.set(docRef, flashcardData);
      }
      await batch.commit();
      return const Right(null);
    } catch (e) {
      return Left(
        CacheFailure('Failed to save flashcards batch: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, Flashcard?>> getFlashcard(String id) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return Left(
          DataFailure('User must be authenticated to access flashcards'),
        );
      }

      final doc = await _flashcardsCollection.doc(id).get();
      if (!doc.exists) {
        return const Right(null);
      }
      final data = doc.data()!;

      // Check if user owns this flashcard
      if (data['userId'] != currentUser.uid) {
        return const Right(
          null,
        ); // Return null if user doesn't own the flashcard
      }

      final flashcard = Flashcard.fromJson(data);
      return Right(flashcard);
    } catch (e) {
      return Left(DataFailure('Failed to get flashcard: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<Flashcard>>> getFlashcardsByTopic({
    required String topic,
    String? subject,
    DifficultyLevel? difficulty,
  }) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return Left(
          DataFailure('User must be authenticated to access flashcards'),
        );
      }

      Query<Map<String, dynamic>> query = _flashcardsCollection
          .where('topic', isEqualTo: topic)
          .where('userId', isEqualTo: currentUser.uid);

      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }
      if (difficulty != null) {
        query = query.where('difficulty', isEqualTo: difficulty.toString());
      }
      final querySnapshot = await query.get();
      final flashcards = querySnapshot.docs
          .map((doc) => Flashcard.fromJson(doc.data()))
          .toList();
      return Right(flashcards);
    } catch (e) {
      return Left(
        DataFailure('Failed to get flashcards by topic: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, List<Flashcard>>> getFlashcardsBySubject(
    String subject,
  ) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return Left(
          DataFailure('User must be authenticated to access flashcards'),
        );
      }

      final querySnapshot = await _flashcardsCollection
          .where('subject', isEqualTo: subject)
          .where('userId', isEqualTo: currentUser.uid)
          .get();
      final flashcards = querySnapshot.docs
          .map((doc) => Flashcard.fromJson(doc.data()))
          .toList();
      return Right(flashcards);
    } catch (e) {
      return Left(
        DataFailure('Failed to get flashcards by subject: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, List<Flashcard>>> getDueFlashcards({
    String? userId,
    String? subject,
    String? topic,
  }) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return Left(
          DataFailure('User must be authenticated to access flashcards'),
        );
      }

      Query<Map<String, dynamic>> query = _flashcardsCollection
          .where('nextReview', isLessThanOrEqualTo: DateTime.now())
          .where('userId', isEqualTo: currentUser.uid);

      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }
      if (topic != null) {
        query = query.where('topic', isEqualTo: topic);
      }
      final querySnapshot = await query.get();
      final flashcards = querySnapshot.docs
          .map((doc) => Flashcard.fromJson(doc.data()))
          .toList();
      return Right(flashcards);
    } catch (e) {
      return Left(DataFailure('Failed to get due flashcards: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Flashcard>> updateFlashcardAfterReview({
    required String flashcardId,
    required FlashcardResponse response,
    Duration reviewTime = const Duration(seconds: 30),
  }) async {
    try {
      final doc = await _flashcardsCollection.doc(flashcardId).get();
      if (!doc.exists) {
        return const Left(DataFailure('Flashcard not found'));
      }
      final flashcard = Flashcard.fromJson(doc.data()!);

      // Use enhanced spaced repetition algorithm
      final schedule = _spacedRepetitionService.calculateNextReview(
        response: response,
        currentInterval: flashcard.interval,
        easeFactor: flashcard.easeFactor,
        reviewCount: flashcard.reviewCount,
      );

      final now = DateTime.now();
      final updatedFlashcard = flashcard.copyWith(
        lastReviewed: now,
        nextReview: schedule.nextReview,
        reviewCount: schedule.reviewCount,
        easeFactor: schedule.easeFactor,
        interval: schedule.interval,
      );

      await _flashcardsCollection
          .doc(flashcardId)
          .update(updatedFlashcard.toJson());

      // Record the review in the reviews collection
      final review = {
        'flashcardId': flashcardId,
        'reviewDate': now.toIso8601String(),
        'response': response.toString(),
        'intervalBefore': flashcard.interval,
        'intervalAfter': schedule.interval,
        'easeFactorBefore': flashcard.easeFactor,
        'easeFactorAfter': schedule.easeFactor,
        'reviewTime': reviewTime.inSeconds,
        'algorithm': 'enhanced_sm2', // Track which algorithm was used
      };
      await _reviewsCollection.add(review);

      return Right(updatedFlashcard);
    } catch (e) {
      return Left(
        DataFailure(
          'Failed to update flashcard after review: \\${e.toString()}',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, void>> deleteFlashcard(String id) async {
    try {
      await _flashcardsCollection.doc(id).delete();
      // Optionally delete related reviews
      final reviewsQuery = await _reviewsCollection
          .where('flashcardId', isEqualTo: id)
          .get();
      for (final doc in reviewsQuery.docs) {
        await _reviewsCollection.doc(doc.id).delete();
      }
      return const Right(null);
    } catch (e) {
      return Left(DataFailure('Failed to delete flashcard: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteFlashcards(List<String> ids) async {
    final batch = _firestore.batch();
    try {
      for (final id in ids) {
        final docRef = _flashcardsCollection.doc(id);
        batch.delete(docRef);
        final reviewsQuery = await _reviewsCollection
            .where('flashcardId', isEqualTo: id)
            .get();
        for (final doc in reviewsQuery.docs) {
          batch.delete(_reviewsCollection.doc(doc.id));
        }
      }
      await batch.commit();
      return const Right(null);
    } catch (e) {
      return Left(
        DataFailure('Failed to delete flashcards batch: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> toggleFlashcardFavorite(
    String flashcardId,
  ) async {
    try {
      final docRef = _flashcardsCollection.doc(flashcardId);
      final doc = await docRef.get();
      if (!doc.exists) {
        return const Left(DataFailure('Flashcard not found'));
      }
      final data = doc.data()!;
      final flashcard = Flashcard.fromJson(data);
      final tags = List<String>.from(flashcard.tags);
      if (tags.contains('favorite')) {
        tags.remove('favorite');
      } else {
        tags.add('favorite');
      }
      await docRef.update({'tags': tags});
      return const Right(null);
    } catch (e) {
      return Left(DataFailure('Failed to toggle favorite: \\${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<Flashcard>>> searchFlashcards({
    required String query,
    String? subject,
    String? topic,
    DifficultyLevel? difficulty,
  }) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return Left(
          DataFailure('User must be authenticated to access flashcards'),
        );
      }

      Query<Map<String, dynamic>> firestoreQuery = _flashcardsCollection.where(
        'userId',
        isEqualTo: currentUser.uid,
      );

      if (subject != null) {
        firestoreQuery = firestoreQuery.where('subject', isEqualTo: subject);
      }
      if (topic != null) {
        firestoreQuery = firestoreQuery.where('topic', isEqualTo: topic);
      }
      if (difficulty != null) {
        firestoreQuery = firestoreQuery.where(
          'difficulty',
          isEqualTo: difficulty.toString(),
        );
      }
      final querySnapshot = await firestoreQuery.get();
      final lowerQuery = query.toLowerCase();
      final results = querySnapshot.docs
          .map((doc) => Flashcard.fromJson(doc.data()))
          .where(
            (card) =>
                card.front.toLowerCase().contains(lowerQuery) ||
                card.back.toLowerCase().contains(lowerQuery) ||
                card.tags.any((tag) => tag.toLowerCase().contains(lowerQuery)),
          )
          .toList();
      return Right(results);
    } catch (e) {
      return Left(DataFailure('Failed to search flashcards: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, FlashcardStats>> getFlashcardStats({
    String? userId,
    String? subject,
    String? topic,
  }) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return Left(
          DataFailure('User must be authenticated to access flashcards'),
        );
      }

      // Build query based on filters
      Query<Map<String, dynamic>> query = _flashcardsCollection.where(
        'userId',
        isEqualTo: currentUser.uid,
      );

      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }

      if (topic != null) {
        query = query.where('topic', isEqualTo: topic);
      }

      final querySnapshot = await query.get();
      final cardList = querySnapshot.docs
          .map((doc) => Flashcard.fromJson(doc.data()))
          .toList();
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      // Enhanced database-computed statistics with performance metrics
      final stats = FlashcardStats(
        totalFlashcards: cardList.length,
        dueFlashcards: cardList.where((card) => card.isDue).length,
        newFlashcards: cardList.where((card) => card.isNew).length,
        reviewedToday: cardList.where((card) {
          final lastReview = DateTime(
            card.lastReviewed.year,
            card.lastReviewed.month,
            card.lastReviewed.day,
          );
          return lastReview.isAtSameMomentAs(today);
        }).length,
        masteredFlashcards: cardList
            .where((card) => card.easeFactor > 2.5 && card.reviewCount > 5)
            .length,
        averageEaseFactor: cardList.isEmpty
            ? 2.5
            : cardList.map((card) => card.easeFactor).reduce((a, b) => a + b) /
                  cardList.length,
        difficultyDistribution: _getDifficultyDistribution(cardList),
        subjectDistribution: _getSubjectDistribution(cardList),
      );

      return Right(stats);
    } catch (e) {
      return Left(
        DataFailure('Failed to get flashcard stats: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, String>> exportFlashcards({
    required List<String> flashcardIds,
    required ExportFormat format,
  }) async {
    try {
      final List<Flashcard> cards = [];
      for (final id in flashcardIds) {
        final doc = await _flashcardsCollection.doc(id).get();
        if (doc.exists) {
          cards.add(Flashcard.fromJson(doc.data()!));
        }
      }
      switch (format) {
        case ExportFormat.json:
          final jsonList = cards.map((c) => c.toJson()).toList();
          return Right(jsonEncode({'flashcards': jsonList}));
        case ExportFormat.csv:
          final csv = StringBuffer('front,back,subject,topic\n');
          for (final c in cards) {
            csv.writeln('${c.front},${c.back},${c.subject},${c.topic}');
          }
          return Right(csv.toString());
        case ExportFormat.anki:
          // Anki-compatible TSV format (tab-separated values)
          final tsv = StringBuffer();
          for (final c in cards) {
            // Anki format: Front\tBack\tTags
            final tags = c.tags.join(' ');
            tsv.writeln('${c.front}\t${c.back}\t$tags');
          }
          return Right(tsv.toString());
        case ExportFormat.quizlet:
          // Quizlet-compatible format (term\tdefinition)
          final quizletFormat = StringBuffer();
          for (final c in cards) {
            quizletFormat.writeln('${c.front}\t${c.back}');
          }
          return Right(quizletFormat.toString());
      }
    } catch (e) {
      return Left(
        DataFailure('Failed to export flashcards: \\${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, List<Flashcard>>> importFlashcards({
    required String data,
    required ExportFormat format,
  }) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return Left(
          DataFailure('User must be authenticated to import flashcards'),
        );
      }

      List<Flashcard> imported = [];
      final now = DateTime.now();
      if (format == ExportFormat.json) {
        final decoded = jsonDecode(data);
        if (decoded is Map && decoded['flashcards'] is List) {
          for (final item in decoded['flashcards']) {
            final card = Flashcard(
              id: item['id'] ?? 'imported_${now.millisecondsSinceEpoch}',
              front: item['front'] ?? '',
              back: item['back'] ?? '',
              subject: item['subject'] ?? 'Imported Subject',
              topic: item['topic'] ?? 'Imported Topic',
              tags: List<String>.from(item['tags'] ?? ['imported']),
              difficulty: DifficultyLevel.values.firstWhere(
                (e) => e.toString() == item['difficulty'],
                orElse: () => DifficultyLevel.medium,
              ),
              createdAt: now,
              lastReviewed: now,
              nextReview: now.add(const Duration(days: 1)),
              reviewCount: 0,
              easeFactor: 2.5,
              interval: 1,
            );
            final flashcardData = card.toJson();
            flashcardData['userId'] = currentUser.uid;
            await _flashcardsCollection.doc(card.id).set(flashcardData);
            imported.add(card);
          }
        }
      } else if (format == ExportFormat.csv) {
        final lines = data.split('\n');
        for (var i = 1; i < lines.length; i++) {
          final cols = lines[i].split(',');
          if (cols.length >= 4) {
            final card = Flashcard(
              id: 'imported_${now.millisecondsSinceEpoch}_$i',
              front: cols[0],
              back: cols[1],
              subject: cols[2],
              topic: cols[3],
              tags: ['imported'],
              difficulty: DifficultyLevel.medium,
              createdAt: now,
              lastReviewed: now,
              nextReview: now.add(const Duration(days: 1)),
              reviewCount: 0,
              easeFactor: 2.5,
              interval: 1,
            );
            final flashcardData = card.toJson();
            flashcardData['userId'] = currentUser.uid;
            await _flashcardsCollection.doc(card.id).set(flashcardData);
            imported.add(card);
          }
        }
      } else if (format == ExportFormat.anki) {
        // Anki TSV format: Front\tBack\tTags
        final lines = data.split('\n');
        for (var i = 0; i < lines.length; i++) {
          final line = lines[i].trim();
          if (line.isEmpty) continue;

          final parts = line.split('\t');
          if (parts.length >= 2) {
            final tags = parts.length > 2 ? parts[2].split(' ') : ['imported'];
            final card = Flashcard(
              id: 'anki_imported_${now.millisecondsSinceEpoch}_$i',
              front: parts[0],
              back: parts[1],
              subject: 'Anki Import',
              topic: 'Imported from Anki',
              tags: ['imported', 'anki', ...tags],
              difficulty: DifficultyLevel.medium,
              createdAt: now,
              lastReviewed: now,
              nextReview: now.add(const Duration(days: 1)),
              reviewCount: 0,
              easeFactor: 2.5,
              interval: 1,
            );
            final flashcardData = card.toJson();
            flashcardData['userId'] = currentUser.uid;
            await _flashcardsCollection.doc(card.id).set(flashcardData);
            imported.add(card);
          }
        }
      } else if (format == ExportFormat.quizlet) {
        // Quizlet format: Term\tDefinition
        final lines = data.split('\n');
        for (var i = 0; i < lines.length; i++) {
          final line = lines[i].trim();
          if (line.isEmpty) continue;

          final parts = line.split('\t');
          if (parts.length >= 2) {
            final card = Flashcard(
              id: 'quizlet_imported_${now.millisecondsSinceEpoch}_$i',
              front: parts[0],
              back: parts[1],
              subject: 'Quizlet Import',
              topic: 'Imported from Quizlet',
              tags: ['imported', 'quizlet'],
              difficulty: DifficultyLevel.medium,
              createdAt: now,
              lastReviewed: now,
              nextReview: now.add(const Duration(days: 1)),
              reviewCount: 0,
              easeFactor: 2.5,
              interval: 1,
            );
            final flashcardData = card.toJson();
            flashcardData['userId'] = currentUser.uid;
            await _flashcardsCollection.doc(card.id).set(flashcardData);
            imported.add(card);
          }
        }
      }
      return Right(imported);
    } catch (e) {
      return Left(
        DataFailure('Failed to import flashcards: \\${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, Flashcard?>> getNextReviewFlashcard({
    String? userId,
    String? subject,
    String? topic,
  }) async {
    final dueResult = await getDueFlashcards(
      userId: userId,
      subject: subject,
      topic: topic,
    );

    return dueResult.fold((failure) => Left(failure), (dueCards) {
      if (dueCards.isEmpty) return const Right(null);

      // Advanced scheduling algorithm based on spaced repetition research
      // Priority factors: overdue time, ease factor, review count, and difficulty
      dueCards.sort((a, b) {
        final now = DateTime.now();
        final aDaysOverdue = now.difference(a.nextReview).inDays;
        final bDaysOverdue = now.difference(b.nextReview).inDays;

        // 1. Prioritize overdue cards (more overdue = higher priority)
        if (aDaysOverdue != bDaysOverdue) {
          return bDaysOverdue.compareTo(aDaysOverdue);
        }

        // 2. Prioritize cards with lower ease factor (harder cards)
        if ((a.easeFactor - b.easeFactor).abs() > 0.1) {
          return a.easeFactor.compareTo(b.easeFactor);
        }

        // 3. Prioritize cards with fewer reviews (newer cards)
        if (a.reviewCount != b.reviewCount) {
          return a.reviewCount.compareTo(b.reviewCount);
        }

        // 4. Prioritize harder difficulty cards
        return b.difficulty.index.compareTo(a.difficulty.index);
      });

      return Right(dueCards.first);
    });
  }

  @override
  Future<Either<Failure, List<Flashcard>>> getFavoriteFlashcards({
    String? userId,
    String? subject,
  }) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return Left(
          DataFailure('User must be authenticated to access flashcards'),
        );
      }

      Query<Map<String, dynamic>> query = _flashcardsCollection
          .where('tags', arrayContains: 'favorite')
          .where('userId', isEqualTo: currentUser.uid);

      if (subject != null) {
        query = query.where('subject', isEqualTo: subject);
      }
      final querySnapshot = await query.get();
      final favorites = querySnapshot.docs
          .map((doc) => Flashcard.fromJson(doc.data()))
          .toList();
      return Right(favorites);
    } catch (e) {
      return Left(
        DataFailure('Failed to get favorite flashcards: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, List<FlashcardReview>>> getFlashcardReviewHistory({
    required String flashcardId,
    int? limit,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _reviewsCollection
          .where('flashcardId', isEqualTo: flashcardId)
          .orderBy('reviewDate', descending: true);
      if (limit != null && limit > 0) {
        query = query.limit(limit);
      }
      final querySnapshot = await query.get();
      final reviews = querySnapshot.docs.map((doc) {
        final data = doc.data();
        return FlashcardReview(
          id: doc.id,
          flashcardId: data['flashcardId'] as String,
          reviewDate: DateTime.parse(data['reviewDate'] as String),
          response: FlashcardResponse.values.firstWhere(
            (e) => e.toString() == data['response'],
            orElse: () => FlashcardResponse.good,
          ),
          intervalBefore: data['intervalBefore'] as int? ?? 0,
          intervalAfter: data['intervalAfter'] as int? ?? 0,
          easeFactorBefore:
              (data['easeFactorBefore'] as num?)?.toDouble() ?? 2.5,
          easeFactorAfter: (data['easeFactorAfter'] as num?)?.toDouble() ?? 2.5,
          reviewTime: Duration(seconds: data['reviewTime'] as int? ?? 0),
        );
      }).toList();
      return Right(reviews);
    } catch (e) {
      return Left(
        DataFailure(
          'Failed to get flashcard review history: \\${e.toString()}',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, void>> resetFlashcardProgress(
    String flashcardId,
  ) async {
    try {
      final doc = await _flashcardsCollection.doc(flashcardId).get();
      if (!doc.exists) {
        return const Left(DataFailure('Flashcard not found'));
      }
      final now = DateTime.now();
      await _flashcardsCollection.doc(flashcardId).update({
        'lastReviewed': now.toIso8601String(),
        'nextReview': now.add(const Duration(days: 1)).toIso8601String(),
        'reviewCount': 0,
        'easeFactor': 2.5,
        'interval': 1,
      });
      // Optionally delete review history
      final reviewsQuery = await _reviewsCollection
          .where('flashcardId', isEqualTo: flashcardId)
          .get();
      for (final doc in reviewsQuery.docs) {
        await _reviewsCollection.doc(doc.id).delete();
      }
      return const Right(null);
    } catch (e) {
      return Left(
        DataFailure('Failed to reset flashcard progress: \\${e.toString()}'),
      );
    }
  }

  /// Helper method to get difficulty distribution
  Map<DifficultyLevel, int> _getDifficultyDistribution(List<Flashcard> cards) {
    final distribution = <DifficultyLevel, int>{};

    for (final level in DifficultyLevel.values) {
      distribution[level] = cards
          .where((card) => card.difficulty == level)
          .length;
    }

    return distribution;
  }

  /// Helper method to get subject distribution
  Map<String, int> _getSubjectDistribution(List<Flashcard> cards) {
    final distribution = <String, int>{};

    for (final card in cards) {
      distribution[card.subject] = (distribution[card.subject] ?? 0) + 1;
    }

    return distribution;
  }
}
