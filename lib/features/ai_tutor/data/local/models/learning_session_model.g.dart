// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'learning_session_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LearningSessionModelAdapter extends TypeAdapter<LearningSessionModel> {
  @override
  final int typeId = 1;

  @override
  LearningSessionModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LearningSessionModel(
      id: fields[0] as String,
      userId: fields[1] as String,
      subject: fields[2] as String,
      topic: fields[3] as String,
      startTime: fields[4] as DateTime,
      endTime: fields[5] as DateTime?,
      status: fields[6] as String,
      conceptsCovered: (fields[7] as List).cast<String>(),
      comprehensionScore: fields[8] as double,
      metadata: (fields[9] as Map).cast<String, dynamic>(),
      durationMinutes: fields[10] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, LearningSessionModel obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.subject)
      ..writeByte(3)
      ..write(obj.topic)
      ..writeByte(4)
      ..write(obj.startTime)
      ..writeByte(5)
      ..write(obj.endTime)
      ..writeByte(6)
      ..write(obj.status)
      ..writeByte(7)
      ..write(obj.conceptsCovered)
      ..writeByte(8)
      ..write(obj.comprehensionScore)
      ..writeByte(9)
      ..write(obj.metadata)
      ..writeByte(10)
      ..write(obj.durationMinutes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LearningSessionModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
