// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'flashcard_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FlashcardModelAdapter extends TypeAdapter<FlashcardModel> {
  @override
  final int typeId = 0;

  @override
  FlashcardModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FlashcardModel(
      id: fields[0] as String,
      front: fields[1] as String,
      back: fields[2] as String,
      subject: fields[3] as String,
      topic: fields[4] as String,
      tags: (fields[5] as List).cast<String>(),
      difficulty: fields[6] as String,
      createdAt: fields[7] as DateTime,
      lastReviewed: fields[8] as DateTime,
      nextReview: fields[9] as DateTime,
      reviewCount: fields[10] as int,
      easeFactor: fields[11] as double,
      interval: fields[12] as int,
    );
  }

  @override
  void write(BinaryWriter writer, FlashcardModel obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.front)
      ..writeByte(2)
      ..write(obj.back)
      ..writeByte(3)
      ..write(obj.subject)
      ..writeByte(4)
      ..write(obj.topic)
      ..writeByte(5)
      ..write(obj.tags)
      ..writeByte(6)
      ..write(obj.difficulty)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.lastReviewed)
      ..writeByte(9)
      ..write(obj.nextReview)
      ..writeByte(10)
      ..write(obj.reviewCount)
      ..writeByte(11)
      ..write(obj.easeFactor)
      ..writeByte(12)
      ..write(obj.interval);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FlashcardModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
