// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuizModelAdapter extends TypeAdapter<QuizModel> {
  @override
  final int typeId = 2;

  @override
  QuizModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuizModel(
      id: fields[0] as String,
      title: fields[1] as String,
      subject: fields[2] as String,
      topic: fields[3] as String,
      questions: (fields[4] as List).cast<QuizQuestionModel>(),
      difficulty: fields[5] as String,
      createdAt: fields[6] as DateTime,
      timeLimit: fields[7] as int,
      isAdaptive: fields[8] as bool,
      metadata: (fields[9] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, QuizModel obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.subject)
      ..writeByte(3)
      ..write(obj.topic)
      ..writeByte(4)
      ..write(obj.questions)
      ..writeByte(5)
      ..write(obj.difficulty)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.timeLimit)
      ..writeByte(8)
      ..write(obj.isAdaptive)
      ..writeByte(9)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuizModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class QuizQuestionModelAdapter extends TypeAdapter<QuizQuestionModel> {
  @override
  final int typeId = 3;

  @override
  QuizQuestionModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuizQuestionModel(
      id: fields[0] as String,
      question: fields[1] as String,
      type: fields[2] as String,
      options: (fields[3] as List).cast<String>(),
      correctAnswers: (fields[4] as List).cast<String>(),
      explanation: fields[5] as String,
      concept: fields[6] as String,
      points: fields[7] as int,
      difficulty: fields[8] as String,
    );
  }

  @override
  void write(BinaryWriter writer, QuizQuestionModel obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.question)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.options)
      ..writeByte(4)
      ..write(obj.correctAnswers)
      ..writeByte(5)
      ..write(obj.explanation)
      ..writeByte(6)
      ..write(obj.concept)
      ..writeByte(7)
      ..write(obj.points)
      ..writeByte(8)
      ..write(obj.difficulty);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuizQuestionModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
