import 'package:hive/hive.dart';
import '../../../domain/entities/learning_session.dart';

part 'learning_session_model.g.dart';

/// Hive model for local storage of learning sessions
/// Implements JSON serialization for offline functionality
@HiveType(typeId: 1)
class LearningSessionModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String userId;

  @HiveField(2)
  final String subject;

  @HiveField(3)
  final String topic;

  @HiveField(4)
  final DateTime startTime;

  @HiveField(5)
  final DateTime? endTime;

  @HiveField(6)
  final String status; // Store as string for Hive compatibility

  @HiveField(7)
  final List<String> conceptsCovered;

  @HiveField(8)
  final double comprehensionScore;

  @HiveField(9)
  final Map<String, dynamic> metadata;

  @HiveField(10)
  final int? durationMinutes; // Store duration as minutes

  LearningSessionModel({
    required this.id,
    required this.userId,
    required this.subject,
    required this.topic,
    required this.startTime,
    this.endTime,
    required this.status,
    required this.conceptsCovered,
    required this.comprehensionScore,
    required this.metadata,
    this.durationMinutes,
  });

  /// Converts domain entity to data model
  factory LearningSessionModel.fromEntity(LearningSession session) {
    return LearningSessionModel(
      id: session.id,
      userId: session.userId,
      subject: session.subject,
      topic: session.topic,
      startTime: session.startTime,
      endTime: session.endTime,
      status: session.status.toString(),
      conceptsCovered: session.conceptsCovered,
      comprehensionScore: session.comprehensionScore,
      metadata: session.metadata,
      durationMinutes: session.duration?.inMinutes,
    );
  }

  /// Converts data model to domain entity
  LearningSession toEntity() {
    return LearningSession(
      id: id,
      userId: userId,
      subject: subject,
      topic: topic,
      startTime: startTime,
      endTime: endTime,
      status: _parseSessionStatus(status),
      conceptsCovered: conceptsCovered,
      comprehensionScore: comprehensionScore,
      metadata: metadata,
    );
  }

  /// Converts to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'subject': subject,
      'topic': topic,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'status': status,
      'conceptsCovered': conceptsCovered,
      'comprehensionScore': comprehensionScore,
      'metadata': metadata,
      'durationMinutes': durationMinutes,
    };
  }

  /// Creates model from JSON
  factory LearningSessionModel.fromJson(Map<String, dynamic> json) {
    return LearningSessionModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      subject: json['subject'] as String,
      topic: json['topic'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] != null
          ? DateTime.parse(json['endTime'] as String)
          : null,
      status: json['status'] as String,
      conceptsCovered: List<String>.from(json['conceptsCovered'] ?? []),
      comprehensionScore: (json['comprehensionScore'] as num).toDouble(),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      durationMinutes: json['durationMinutes'] as int?,
    );
  }

  /// Helper method to parse session status
  LearningSessionStatus _parseSessionStatus(String status) {
    switch (status) {
      case 'LearningSessionStatus.active':
        return LearningSessionStatus.active;
      case 'LearningSessionStatus.completed':
        return LearningSessionStatus.completed;
      case 'LearningSessionStatus.paused':
        return LearningSessionStatus.paused;
      case 'LearningSessionStatus.cancelled':
        return LearningSessionStatus.cancelled;
      default:
        return LearningSessionStatus.active;
    }
  }

  @override
  String toString() {
    return 'LearningSessionModel(id: $id, subject: $subject, topic: $topic, status: $status)';
  }
}
