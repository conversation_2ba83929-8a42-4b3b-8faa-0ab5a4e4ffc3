// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'progress_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LearningProgressModelAdapter extends TypeAdapter<LearningProgressModel> {
  @override
  final int typeId = 4;

  @override
  LearningProgressModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LearningProgressModel(
      id: fields[0] as String,
      userId: fields[1] as String,
      subject: fields[2] as String,
      topic: fields[3] as String,
      overallProgress: fields[4] as double,
      conceptProgress: Map<String, double>.from(fields[5] as Map),
      masteredConcepts: List<String>.from(fields[6] as List),
      strugglingConcepts: List<String>.from(fields[7] as List),
      stats: fields[8] as LearningStatsModel,
      lastUpdated: fields[9] as DateTime,
      metadata: Map<String, dynamic>.from(fields[10] as Map),
    );
  }

  @override
  void write(BinaryWriter writer, LearningProgressModel obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.subject)
      ..writeByte(3)
      ..write(obj.masteredConcepts)
      ..writeByte(4)
      ..write(obj.strugglingConcepts)
      ..writeByte(5)
      ..write(obj.stats)
      ..writeByte(6)
      ..write(obj.lastUpdated)
      ..writeByte(7)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LearningProgressModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LearningStatsModelAdapter extends TypeAdapter<LearningStatsModel> {
  @override
  final int typeId = 5;

  @override
  LearningStatsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LearningStatsModel(
      totalStudyTime: fields[0] as int,
      sessionsCompleted: fields[1] as int,
      quizzesCompleted: fields[2] as int,
      averageQuizScore: fields[3] as double,
      flashcardsReviewed: fields[4] as int,
      streakDays: fields[5] as int,
      lastStudyDate: fields[6] as DateTime,
      weeklyActivity: Map<String, int>.from(fields[7] as Map),
    );
  }

  @override
  void write(BinaryWriter writer, LearningStatsModel obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.totalStudyTime)
      ..writeByte(1)
      ..write(obj.sessionsCompleted)
      ..writeByte(2)
      ..write(obj.quizzesCompleted)
      ..writeByte(3)
      ..write(obj.averageQuizScore)
      ..writeByte(4)
      ..write(obj.flashcardsReviewed)
      ..writeByte(5)
      ..write(obj.streakDays)
      ..writeByte(6)
      ..write(obj.lastStudyDate);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LearningStatsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
