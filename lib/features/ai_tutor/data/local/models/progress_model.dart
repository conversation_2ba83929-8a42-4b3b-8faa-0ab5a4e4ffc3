import 'package:hive/hive.dart';
import '../../../domain/entities/learning_progress.dart';

part 'progress_model.g.dart';

/// Hive model for local storage of learning progress
/// Implements JSON serialization for offline functionality
@HiveType(typeId: 4)
class LearningProgressModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String userId;

  @HiveField(2)
  final String subject;

  @HiveField(3)
  final String topic;

  @HiveField(4)
  final double overallProgress;

  @HiveField(5)
  final Map<String, double> conceptProgress;

  @HiveField(6)
  final List<String> masteredConcepts;

  @HiveField(7)
  final List<String> strugglingConcepts;

  @HiveField(8)
  final LearningStatsModel stats;

  @HiveField(9)
  final DateTime lastUpdated;

  @HiveField(10)
  final Map<String, dynamic> metadata;

  LearningProgressModel({
    required this.id,
    required this.userId,
    required this.subject,
    required this.topic,
    required this.overallProgress,
    required this.conceptProgress,
    required this.masteredConcepts,
    required this.strugglingConcepts,
    required this.stats,
    required this.lastUpdated,
    this.metadata = const {},
  });

  /// Converts domain entity to data model
  factory LearningProgressModel.fromEntity(LearningProgress progress) {
    return LearningProgressModel(
      id: progress.id,
      userId: progress.userId,
      subject: progress.subject,
      topic: progress.topic,
      overallProgress: progress.overallProgress,
      conceptProgress: progress.conceptProgress,
      masteredConcepts: progress.masteredConcepts,
      strugglingConcepts: progress.strugglingConcepts,
      stats: LearningStatsModel.fromEntity(progress.stats),
      lastUpdated: progress.lastUpdated,
      metadata: progress.metadata,
    );
  }

  /// Converts data model to domain entity
  LearningProgress toEntity() {
    return LearningProgress(
      id: id,
      userId: userId,
      subject: subject,
      topic: topic,
      overallProgress: overallProgress,
      conceptProgress: conceptProgress,
      masteredConcepts: masteredConcepts,
      strugglingConcepts: strugglingConcepts,
      stats: stats.toEntity(),
      lastUpdated: lastUpdated,
      metadata: metadata,
    );
  }

  /// Converts to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'subject': subject,
      'topic': topic,
      'overallProgress': overallProgress,
      'conceptProgress': conceptProgress,
      'masteredConcepts': masteredConcepts,
      'strugglingConcepts': strugglingConcepts,
      'stats': stats.toJson(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Creates model from JSON
  factory LearningProgressModel.fromJson(Map<String, dynamic> json) {
    return LearningProgressModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      subject: json['subject'] as String,
      topic: json['topic'] as String? ?? 'general',
      overallProgress: (json['overallProgress'] as num?)?.toDouble() ?? 0.0,
      conceptProgress: Map<String, double>.from(json['conceptProgress'] ?? {}),
      masteredConcepts: List<String>.from(json['masteredConcepts'] ?? []),
      strugglingConcepts: List<String>.from(json['strugglingConcepts'] ?? []),
      stats: LearningStatsModel.fromJson(json['stats'] as Map<String, dynamic>),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'LearningProgressModel(id: $id, userId: $userId, subject: $subject)';
  }
}

/// Hive model for learning statistics
@HiveType(typeId: 5)
class LearningStatsModel extends HiveObject {
  @HiveField(0)
  final int totalStudyTime; // in minutes

  @HiveField(1)
  final int sessionsCompleted;

  @HiveField(2)
  final int quizzesCompleted;

  @HiveField(3)
  final double averageQuizScore;

  @HiveField(4)
  final int flashcardsReviewed;

  @HiveField(5)
  final int streakDays;

  @HiveField(6)
  final DateTime lastStudyDate;

  @HiveField(7)
  final Map<String, int> weeklyActivity;

  LearningStatsModel({
    required this.totalStudyTime,
    required this.sessionsCompleted,
    required this.quizzesCompleted,
    required this.averageQuizScore,
    required this.flashcardsReviewed,
    required this.streakDays,
    required this.lastStudyDate,
    required this.weeklyActivity,
  });

  /// Converts domain entity to data model
  factory LearningStatsModel.fromEntity(LearningStats stats) {
    return LearningStatsModel(
      totalStudyTime: stats.totalStudyTime,
      sessionsCompleted: stats.sessionsCompleted,
      quizzesCompleted: stats.quizzesCompleted,
      averageQuizScore: stats.averageQuizScore,
      flashcardsReviewed: stats.flashcardsReviewed,
      streakDays: stats.streakDays,
      lastStudyDate: stats.lastStudyDate,
      weeklyActivity: stats.weeklyActivity,
    );
  }

  /// Converts data model to domain entity
  LearningStats toEntity() {
    return LearningStats(
      totalStudyTime: totalStudyTime,
      sessionsCompleted: sessionsCompleted,
      quizzesCompleted: quizzesCompleted,
      averageQuizScore: averageQuizScore,
      flashcardsReviewed: flashcardsReviewed,
      streakDays: streakDays,
      lastStudyDate: lastStudyDate,
      weeklyActivity: weeklyActivity,
    );
  }

  /// Converts to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'totalStudyTime': totalStudyTime,
      'sessionsCompleted': sessionsCompleted,
      'quizzesCompleted': quizzesCompleted,
      'averageQuizScore': averageQuizScore,
      'flashcardsReviewed': flashcardsReviewed,
      'streakDays': streakDays,
      'lastStudyDate': lastStudyDate.toIso8601String(),
      'weeklyActivity': weeklyActivity,
    };
  }

  /// Creates model from JSON
  factory LearningStatsModel.fromJson(Map<String, dynamic> json) {
    return LearningStatsModel(
      totalStudyTime: json['totalStudyTime'] as int,
      sessionsCompleted: json['sessionsCompleted'] as int,
      quizzesCompleted: json['quizzesCompleted'] as int,
      averageQuizScore: (json['averageQuizScore'] as num).toDouble(),
      flashcardsReviewed: json['flashcardsReviewed'] as int,
      streakDays: json['streakDays'] as int,
      lastStudyDate: DateTime.parse(json['lastStudyDate'] as String),
      weeklyActivity: Map<String, int>.from(json['weeklyActivity'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'LearningStatsModel(totalStudyTime: $totalStudyTime, sessionsCompleted: $sessionsCompleted)';
  }
}
