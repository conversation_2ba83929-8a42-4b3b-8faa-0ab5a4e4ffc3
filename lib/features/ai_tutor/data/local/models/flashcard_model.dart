import 'package:hive/hive.dart';
import '../../../domain/entities/flashcard.dart';

part 'flashcard_model.g.dart';

/// Hive model for local storage of flashcards
/// Implements JSON serialization for offline functionality
@HiveType(typeId: 0)
class FlashcardModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String front;

  @HiveField(2)
  final String back;

  @HiveField(3)
  final String subject;

  @HiveField(4)
  final String topic;

  @HiveField(5)
  final List<String> tags;

  @HiveField(6)
  final String difficulty; // Store as string for Hive compatibility

  @HiveField(7)
  final DateTime createdAt;

  @HiveField(8)
  final DateTime lastReviewed;

  @HiveField(9)
  final DateTime nextReview;

  @HiveField(10)
  final int reviewCount;

  @HiveField(11)
  final double easeFactor;

  @HiveField(12)
  final int interval;

  FlashcardModel({
    required this.id,
    required this.front,
    required this.back,
    required this.subject,
    required this.topic,
    required this.tags,
    required this.difficulty,
    required this.createdAt,
    required this.lastReviewed,
    required this.nextReview,
    required this.reviewCount,
    required this.easeFactor,
    required this.interval,
  });

  /// Converts domain entity to data model
  factory FlashcardModel.fromEntity(Flashcard flashcard) {
    return FlashcardModel(
      id: flashcard.id,
      front: flashcard.front,
      back: flashcard.back,
      subject: flashcard.subject,
      topic: flashcard.topic,
      tags: flashcard.tags,
      difficulty: flashcard.difficulty.toString(),
      createdAt: flashcard.createdAt,
      lastReviewed: flashcard.lastReviewed,
      nextReview: flashcard.nextReview,
      reviewCount: flashcard.reviewCount,
      easeFactor: flashcard.easeFactor,
      interval: flashcard.interval,
    );
  }

  /// Converts data model to domain entity
  Flashcard toEntity() {
    return Flashcard(
      id: id,
      front: front,
      back: back,
      subject: subject,
      topic: topic,
      tags: tags,
      difficulty: _parseDifficultyLevel(difficulty),
      createdAt: createdAt,
      lastReviewed: lastReviewed,
      nextReview: nextReview,
      reviewCount: reviewCount,
      easeFactor: easeFactor,
      interval: interval,
    );
  }

  /// Converts to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'front': front,
      'back': back,
      'subject': subject,
      'topic': topic,
      'tags': tags,
      'difficulty': difficulty,
      'createdAt': createdAt.toIso8601String(),
      'lastReviewed': lastReviewed.toIso8601String(),
      'nextReview': nextReview.toIso8601String(),
      'reviewCount': reviewCount,
      'easeFactor': easeFactor,
      'interval': interval,
    };
  }

  /// Creates model from JSON
  factory FlashcardModel.fromJson(Map<String, dynamic> json) {
    return FlashcardModel(
      id: json['id'] as String,
      front: json['front'] as String,
      back: json['back'] as String,
      subject: json['subject'] as String,
      topic: json['topic'] as String,
      tags: List<String>.from(json['tags'] ?? []),
      difficulty: json['difficulty'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastReviewed: DateTime.parse(json['lastReviewed'] as String),
      nextReview: DateTime.parse(json['nextReview'] as String),
      reviewCount: json['reviewCount'] as int,
      easeFactor: (json['easeFactor'] as num).toDouble(),
      interval: json['interval'] as int,
    );
  }

  /// Helper method to parse difficulty level
  DifficultyLevel _parseDifficultyLevel(String difficulty) {
    switch (difficulty) {
      case 'DifficultyLevel.easy':
        return DifficultyLevel.easy;
      case 'DifficultyLevel.medium':
        return DifficultyLevel.medium;
      case 'DifficultyLevel.hard':
        return DifficultyLevel.hard;
      case 'DifficultyLevel.expert':
        return DifficultyLevel.expert;
      default:
        return DifficultyLevel.medium;
    }
  }

  @override
  String toString() {
    return 'FlashcardModel(id: $id, subject: $subject, topic: $topic, difficulty: $difficulty)';
  }
}
