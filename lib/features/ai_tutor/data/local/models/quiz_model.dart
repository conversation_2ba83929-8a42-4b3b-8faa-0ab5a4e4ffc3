import 'package:diogeneschatbot/features/ai_tutor/domain/entities/flashcard.dart';
import 'package:hive/hive.dart';
import '../../../domain/entities/quiz.dart';

part 'quiz_model.g.dart';

/// Hive model for local storage of quizzes
/// Implements JSON serialization for offline functionality
@HiveType(typeId: 2)
class QuizModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String subject;

  @HiveField(3)
  final String topic;

  @HiveField(4)
  final List<QuizQuestionModel> questions;

  @HiveField(5)
  final String difficulty; // Store as string for Hive compatibility

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  final int timeLimit;

  @HiveField(8)
  final bool isAdaptive;

  @HiveField(9)
  final Map<String, dynamic> metadata;

  QuizModel({
    required this.id,
    required this.title,
    required this.subject,
    required this.topic,
    required this.questions,
    required this.difficulty,
    required this.createdAt,
    required this.timeLimit,
    required this.isAdaptive,
    required this.metadata,
  });

  /// Converts domain entity to data model
  factory QuizModel.fromEntity(Quiz quiz) {
    return QuizModel(
      id: quiz.id,
      title: quiz.title,
      subject: quiz.subject,
      topic: quiz.topic,
      questions: quiz.questions.map((q) => QuizQuestionModel.fromEntity(q)).toList(),
      difficulty: quiz.difficulty.toString(),
      createdAt: quiz.createdAt,
      timeLimit: quiz.timeLimit,
      isAdaptive: quiz.isAdaptive,
      metadata: quiz.metadata,
    );
  }

  /// Converts data model to domain entity
  Quiz toEntity() {
    return Quiz(
      id: id,
      title: title,
      subject: subject,
      topic: topic,
      questions: questions.map((q) => q.toEntity()).toList(),
      difficulty: _parseDifficultyLevel(difficulty),
      createdAt: createdAt,
      timeLimit: timeLimit,
      isAdaptive: isAdaptive,
      metadata: metadata,
    );
  }

  /// Converts to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'subject': subject,
      'topic': topic,
      'questions': questions.map((q) => q.toJson()).toList(),
      'difficulty': difficulty,
      'createdAt': createdAt.toIso8601String(),
      'timeLimit': timeLimit,
      'isAdaptive': isAdaptive,
      'metadata': metadata,
    };
  }

  /// Creates model from JSON
  factory QuizModel.fromJson(Map<String, dynamic> json) {
    return QuizModel(
      id: json['id'] as String,
      title: json['title'] as String,
      subject: json['subject'] as String,
      topic: json['topic'] as String,
      questions: (json['questions'] as List<dynamic>)
          .map((q) => QuizQuestionModel.fromJson(q as Map<String, dynamic>))
          .toList(),
      difficulty: json['difficulty'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      timeLimit: json['timeLimit'] as int,
      isAdaptive: json['isAdaptive'] as bool,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// Helper method to parse difficulty level
  DifficultyLevel _parseDifficultyLevel(String difficulty) {
    switch (difficulty) {
      case 'DifficultyLevel.easy':
        return DifficultyLevel.easy;
      case 'DifficultyLevel.medium':
        return DifficultyLevel.medium;
      case 'DifficultyLevel.hard':
        return DifficultyLevel.hard;
      case 'DifficultyLevel.expert':
        return DifficultyLevel.expert;
      default:
        return DifficultyLevel.medium;
    }
  }

  @override
  String toString() {
    return 'QuizModel(id: $id, title: $title, subject: $subject, difficulty: $difficulty)';
  }
}

/// Hive model for quiz questions
@HiveType(typeId: 3)
class QuizQuestionModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String question;

  @HiveField(2)
  final String type; // Store as string for Hive compatibility

  @HiveField(3)
  final List<String> options;

  @HiveField(4)
  final List<String> correctAnswers;

  @HiveField(5)
  final String explanation;

  @HiveField(6)
  final String concept;

  @HiveField(7)
  final int points;

  @HiveField(8)
  final String difficulty; // Store as string for Hive compatibility

  QuizQuestionModel({
    required this.id,
    required this.question,
    required this.type,
    required this.options,
    required this.correctAnswers,
    required this.explanation,
    required this.concept,
    required this.points,
    required this.difficulty,
  });

  /// Converts domain entity to data model
  factory QuizQuestionModel.fromEntity(QuizQuestion question) {
    return QuizQuestionModel(
      id: question.id,
      question: question.question,
      type: question.type.toString(),
      options: question.options,
      correctAnswers: question.correctAnswers,
      explanation: question.explanation,
      concept: question.concept,
      points: question.points,
      difficulty: question.difficulty.toString(),
    );
  }

  /// Converts data model to domain entity
  QuizQuestion toEntity() {
    return QuizQuestion(
      id: id,
      question: question,
      type: _parseQuestionType(type),
      options: options,
      correctAnswers: correctAnswers,
      explanation: explanation,
      concept: concept,
      points: points,
      difficulty: _parseDifficultyLevel(difficulty),
    );
  }

  /// Converts to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'type': type,
      'options': options,
      'correctAnswers': correctAnswers,
      'explanation': explanation,
      'concept': concept,
      'points': points,
      'difficulty': difficulty,
    };
  }

  /// Creates model from JSON
  factory QuizQuestionModel.fromJson(Map<String, dynamic> json) {
    return QuizQuestionModel(
      id: json['id'] as String,
      question: json['question'] as String,
      type: json['type'] as String,
      options: List<String>.from(json['options'] ?? []),
      correctAnswers: List<String>.from(json['correctAnswers'] ?? []),
      explanation: json['explanation'] as String,
      concept: json['concept'] as String,
      points: json['points'] as int,
      difficulty: json['difficulty'] as String,
    );
  }

  /// Helper method to parse question type
  QuestionType _parseQuestionType(String type) {
    switch (type) {
      case 'QuestionType.multipleChoice':
        return QuestionType.multipleChoice;
      case 'QuestionType.trueFalse':
        return QuestionType.trueFalse;
      case 'QuestionType.fillInBlank':
        return QuestionType.fillInBlank;
      case 'QuestionType.shortAnswer':
        return QuestionType.shortAnswer;
      case 'QuestionType.essay':
        return QuestionType.essay;
      default:
        return QuestionType.multipleChoice;
    }
  }

  /// Helper method to parse difficulty level
  DifficultyLevel _parseDifficultyLevel(String difficulty) {
    switch (difficulty) {
      case 'DifficultyLevel.easy':
        return DifficultyLevel.easy;
      case 'DifficultyLevel.medium':
        return DifficultyLevel.medium;
      case 'DifficultyLevel.hard':
        return DifficultyLevel.hard;
      case 'DifficultyLevel.expert':
        return DifficultyLevel.expert;
      default:
        return DifficultyLevel.medium;
    }
  }

  @override
  String toString() {
    return 'QuizQuestionModel(id: $id, question: $question, type: $type)';
  }
}
