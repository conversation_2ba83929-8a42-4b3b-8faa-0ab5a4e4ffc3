import 'package:dartz/dartz.dart';
import 'package:hive/hive.dart';
import '../../../domain/entities/learning_progress.dart';
import '../../../domain/entities/learning_session.dart';
import '../../../domain/entities/learning_goal.dart';
import '../../../domain/entities/learning_streak.dart';
import '../../../../../core/error/failures.dart';

/// Local data source for learning progress using Hive for offline storage
class LearningProgressLocalDataSource {
  static const String _progressBoxName = 'learning_progress';
  static const String _sessionsBoxName = 'learning_sessions';
  static const String _goalsBoxName = 'learning_goals';
  static const String _streaksBoxName = 'learning_streaks';

  late Box<Map<dynamic, dynamic>> _progressBox;
  late Box<Map<dynamic, dynamic>> _sessionsBox;
  late Box<Map<dynamic, dynamic>> _goalsBox;
  late Box<Map<dynamic, dynamic>> _streaksBox;

  /// Initialize Hive boxes for local storage
  Future<void> init() async {
    _progressBox = await Hive.openBox<Map<dynamic, dynamic>>(_progressBoxName);
    _sessionsBox = await Hive.openBox<Map<dynamic, dynamic>>(_sessionsBoxName);
    _goalsBox = await Hive.openBox<Map<dynamic, dynamic>>(_goalsBoxName);
    _streaksBox = await Hive.openBox<Map<dynamic, dynamic>>(_streaksBoxName);
  }

  /// Save learning progress locally
  Future<Either<Failure, void>> saveLearningProgress(
    LearningProgress progress,
  ) async {
    try {
      final key = '${progress.userId}_${progress.subject}_${progress.topic}';
      await _progressBox.put(key, progress.toJson());
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to save progress locally: $e'));
    }
  }

  /// Get learning progress from local storage
  Future<Either<Failure, LearningProgress?>> getLearningProgress({
    required String userId,
    required String subject,
    String? topic,
  }) async {
    try {
      final key = '${userId}_${subject}_${topic ?? 'general'}';
      final data = _progressBox.get(key);
      
      if (data == null) {
        return const Right(null);
      }
      
      final progress = LearningProgress.fromJson(
        Map<String, dynamic>.from(data),
      );
      return Right(progress);
    } catch (e) {
      return Left(CacheFailure('Failed to get progress locally: $e'));
    }
  }

  /// Get all learning progress for a user from local storage
  Future<Either<Failure, List<LearningProgress>>> getUserLearningProgress(
    String userId,
  ) async {
    try {
      final progressList = <LearningProgress>[];
      
      for (final key in _progressBox.keys) {
        if (key.toString().startsWith(userId)) {
          final data = _progressBox.get(key);
          if (data != null) {
            final progress = LearningProgress.fromJson(
              Map<String, dynamic>.from(data),
            );
            progressList.add(progress);
          }
        }
      }
      
      // Sort by last updated (most recent first)
      progressList.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));
      
      return Right(progressList);
    } catch (e) {
      return Left(CacheFailure('Failed to get user progress locally: $e'));
    }
  }

  /// Save learning session locally
  Future<Either<Failure, void>> saveLearningSession(
    LearningSession session,
  ) async {
    try {
      await _sessionsBox.put(session.id, session.toJson());
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to save session locally: $e'));
    }
  }

  /// Get learning sessions for a user from local storage
  Future<Either<Failure, List<LearningSession>>> getLearningSessionsForUser({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final sessions = <LearningSession>[];
      
      for (final data in _sessionsBox.values) {
        if (data != null) {
          final session = LearningSession.fromJson(
            Map<String, dynamic>.from(data),
          );
          
          // Filter by user ID
          if (session.userId != userId) continue;
          
          // Filter by subject if specified
          if (subject != null && session.subject != subject) continue;
          
          // Filter by date range if specified
          if (startDate != null && session.startTime.isBefore(startDate)) continue;
          if (endDate != null && session.startTime.isAfter(endDate)) continue;
          
          sessions.add(session);
        }
      }
      
      // Sort by start time (most recent first)
      sessions.sort((a, b) => b.startTime.compareTo(a.startTime));
      
      return Right(sessions);
    } catch (e) {
      return Left(CacheFailure('Failed to get sessions locally: $e'));
    }
  }

  /// Save learning goal locally
  Future<Either<Failure, void>> saveLearningGoal(LearningGoal goal) async {
    try {
      await _goalsBox.put(goal.id, goal.toJson());
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to save goal locally: $e'));
    }
  }

  /// Get learning goals for a user from local storage
  Future<Either<Failure, List<LearningGoal>>> getLearningGoals(
    String userId,
  ) async {
    try {
      final goals = <LearningGoal>[];
      
      for (final data in _goalsBox.values) {
        if (data != null) {
          final goal = LearningGoal.fromJson(
            Map<String, dynamic>.from(data),
          );
          
          if (goal.userId == userId) {
            goals.add(goal);
          }
        }
      }
      
      // Sort by target date (nearest first)
      goals.sort((a, b) => a.targetDate.compareTo(b.targetDate));
      
      return Right(goals);
    } catch (e) {
      return Left(CacheFailure('Failed to get goals locally: $e'));
    }
  }

  /// Save learning streak locally
  Future<Either<Failure, void>> saveLearningStreak(
    LearningStreak streak,
  ) async {
    try {
      await _streaksBox.put(streak.userId, streak.toJson());
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to save streak locally: $e'));
    }
  }

  /// Get learning streak for a user from local storage
  Future<Either<Failure, LearningStreak?>> getLearningStreak(
    String userId,
  ) async {
    try {
      final data = _streaksBox.get(userId);
      
      if (data == null) {
        return const Right(null);
      }
      
      final streak = LearningStreak.fromJson(
        Map<String, dynamic>.from(data),
      );
      return Right(streak);
    } catch (e) {
      return Left(CacheFailure('Failed to get streak locally: $e'));
    }
  }

  /// Clear all local data (for logout or data reset)
  Future<Either<Failure, void>> clearAllData() async {
    try {
      await _progressBox.clear();
      await _sessionsBox.clear();
      await _goalsBox.clear();
      await _streaksBox.clear();
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to clear local data: $e'));
    }
  }

  /// Get cache size information
  Map<String, int> getCacheInfo() {
    return {
      'progress_count': _progressBox.length,
      'sessions_count': _sessionsBox.length,
      'goals_count': _goalsBox.length,
      'streaks_count': _streaksBox.length,
    };
  }
}
