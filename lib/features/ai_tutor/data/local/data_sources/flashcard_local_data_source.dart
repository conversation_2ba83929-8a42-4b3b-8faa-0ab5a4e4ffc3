import 'package:dartz/dartz.dart';
import 'package:hive/hive.dart';
import '../../../domain/entities/flashcard.dart';
import '../../../../../core/error/failures.dart';

/// Local data source for flashcards using Hive for offline storage
class FlashcardLocalDataSource {
  static const String _flashcardsBoxName = 'flashcards';
  static const String _reviewsBoxName = 'flashcard_reviews';

  late Box<Map<dynamic, dynamic>> _flashcardsBox;
  late Box<Map<dynamic, dynamic>> _reviewsBox;

  /// Initialize Hive boxes for local storage
  Future<void> init() async {
    _flashcardsBox = await Hive.openBox<Map<dynamic, dynamic>>(_flashcardsBoxName);
    _reviewsBox = await Hive.openBox<Map<dynamic, dynamic>>(_reviewsBoxName);
  }

  /// Save flashcard locally
  Future<Either<Failure, void>> saveFlashcard(Flashcard flashcard) async {
    try {
      await _flashcardsBox.put(flashcard.id, flashcard.toJson());
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to save flashcard locally: $e'));
    }
  }

  /// Save multiple flashcards locally
  Future<Either<Failure, void>> saveFlashcards(List<Flashcard> flashcards) async {
    try {
      final Map<String, Map<String, dynamic>> flashcardMap = {};
      for (final flashcard in flashcards) {
        flashcardMap[flashcard.id] = flashcard.toJson();
      }
      await _flashcardsBox.putAll(flashcardMap);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to save flashcards locally: $e'));
    }
  }

  /// Get flashcard by ID from local storage
  Future<Either<Failure, Flashcard?>> getFlashcard(String id) async {
    try {
      final data = _flashcardsBox.get(id);
      
      if (data == null) {
        return const Right(null);
      }
      
      final flashcard = Flashcard.fromJson(Map<String, dynamic>.from(data));
      return Right(flashcard);
    } catch (e) {
      return Left(CacheFailure('Failed to get flashcard locally: $e'));
    }
  }

  /// Get flashcards by subject from local storage
  Future<Either<Failure, List<Flashcard>>> getFlashcardsBySubject(
    String subject,
  ) async {
    try {
      final flashcards = <Flashcard>[];
      
      for (final data in _flashcardsBox.values) {
        if (data != null) {
          final flashcard = Flashcard.fromJson(Map<String, dynamic>.from(data));
          if (flashcard.subject == subject) {
            flashcards.add(flashcard);
          }
        }
      }
      
      // Sort by creation date (newest first)
      flashcards.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      return Right(flashcards);
    } catch (e) {
      return Left(CacheFailure('Failed to get flashcards by subject locally: $e'));
    }
  }

  /// Get flashcards by topic from local storage
  Future<Either<Failure, List<Flashcard>>> getFlashcardsByTopic({
    required String topic,
    String? subject,
    DifficultyLevel? difficulty,
  }) async {
    try {
      final flashcards = <Flashcard>[];
      
      for (final data in _flashcardsBox.values) {
        if (data != null) {
          final flashcard = Flashcard.fromJson(Map<String, dynamic>.from(data));
          
          // Filter by topic
          if (flashcard.topic != topic) continue;
          
          // Filter by subject if specified
          if (subject != null && flashcard.subject != subject) continue;
          
          // Filter by difficulty if specified
          if (difficulty != null && flashcard.difficulty != difficulty) continue;
          
          flashcards.add(flashcard);
        }
      }
      
      // Sort by creation date (newest first)
      flashcards.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      return Right(flashcards);
    } catch (e) {
      return Left(CacheFailure('Failed to get flashcards by topic locally: $e'));
    }
  }

  /// Get due flashcards from local storage
  Future<Either<Failure, List<Flashcard>>> getDueFlashcards({
    String? userId,
    String? subject,
    String? topic,
  }) async {
    try {
      final flashcards = <Flashcard>[];
      final now = DateTime.now();
      
      for (final data in _flashcardsBox.values) {
        if (data != null) {
          final flashcard = Flashcard.fromJson(Map<String, dynamic>.from(data));
          
          // Check if flashcard is due
          if (!flashcard.nextReview.isBefore(now) && !flashcard.nextReview.isAtSameMomentAs(now)) {
            continue;
          }
          
          // Filter by subject if specified
          if (subject != null && flashcard.subject != subject) continue;
          
          // Filter by topic if specified
          if (topic != null && flashcard.topic != topic) continue;
          
          flashcards.add(flashcard);
        }
      }
      
      // Sort by next review date (most overdue first)
      flashcards.sort((a, b) => a.nextReview.compareTo(b.nextReview));
      
      return Right(flashcards);
    } catch (e) {
      return Left(CacheFailure('Failed to get due flashcards locally: $e'));
    }
  }

  /// Search flashcards locally
  Future<Either<Failure, List<Flashcard>>> searchFlashcards({
    required String query,
    String? subject,
    String? topic,
    DifficultyLevel? difficulty,
  }) async {
    try {
      final flashcards = <Flashcard>[];
      final lowercaseQuery = query.toLowerCase();
      
      for (final data in _flashcardsBox.values) {
        if (data != null) {
          final flashcard = Flashcard.fromJson(Map<String, dynamic>.from(data));
          
          // Search in front, back, and tags
          final searchableText = '${flashcard.front} ${flashcard.back} ${flashcard.tags.join(' ')}'.toLowerCase();
          
          if (!searchableText.contains(lowercaseQuery)) continue;
          
          // Apply filters
          if (subject != null && flashcard.subject != subject) continue;
          if (topic != null && flashcard.topic != topic) continue;
          if (difficulty != null && flashcard.difficulty != difficulty) continue;
          
          flashcards.add(flashcard);
        }
      }
      
      // Sort by relevance (exact matches first, then partial matches)
      flashcards.sort((a, b) {
        final aFrontMatch = a.front.toLowerCase().contains(lowercaseQuery);
        final bFrontMatch = b.front.toLowerCase().contains(lowercaseQuery);
        
        if (aFrontMatch && !bFrontMatch) return -1;
        if (!aFrontMatch && bFrontMatch) return 1;
        
        return b.createdAt.compareTo(a.createdAt);
      });
      
      return Right(flashcards);
    } catch (e) {
      return Left(CacheFailure('Failed to search flashcards locally: $e'));
    }
  }

  /// Update flashcard after review
  Future<Either<Failure, void>> updateFlashcard(Flashcard flashcard) async {
    try {
      await _flashcardsBox.put(flashcard.id, flashcard.toJson());
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to update flashcard locally: $e'));
    }
  }

  /// Delete flashcard from local storage
  Future<Either<Failure, void>> deleteFlashcard(String id) async {
    try {
      await _flashcardsBox.delete(id);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to delete flashcard locally: $e'));
    }
  }

  /// Save flashcard review record
  Future<Either<Failure, void>> saveFlashcardReview({
    required String flashcardId,
    required FlashcardResponse response,
    required Duration reviewTime,
  }) async {
    try {
      final reviewId = '${flashcardId}_${DateTime.now().millisecondsSinceEpoch}';
      final reviewData = {
        'id': reviewId,
        'flashcardId': flashcardId,
        'response': response.name,
        'reviewTime': reviewTime.inMilliseconds,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      await _reviewsBox.put(reviewId, reviewData);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to save review locally: $e'));
    }
  }

  /// Get flashcard statistics
  Map<String, dynamic> getFlashcardStats() {
    final totalCards = _flashcardsBox.length;
    final now = DateTime.now();
    int dueCards = 0;
    int newCards = 0;
    int reviewedCards = 0;
    
    for (final data in _flashcardsBox.values) {
      if (data != null) {
        final flashcard = Flashcard.fromJson(Map<String, dynamic>.from(data));
        
        if (flashcard.reviewCount == 0) {
          newCards++;
        } else {
          reviewedCards++;
        }
        
        if (flashcard.nextReview.isBefore(now) || flashcard.nextReview.isAtSameMomentAs(now)) {
          dueCards++;
        }
      }
    }
    
    return {
      'total': totalCards,
      'due': dueCards,
      'new': newCards,
      'reviewed': reviewedCards,
      'reviews_count': _reviewsBox.length,
    };
  }

  /// Clear all flashcard data
  Future<Either<Failure, void>> clearAllData() async {
    try {
      await _flashcardsBox.clear();
      await _reviewsBox.clear();
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to clear flashcard data locally: $e'));
    }
  }
}
