import 'package:dartz/dartz.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/entities/flashcard.dart';
import 'package:hive/hive.dart';
import '../../../domain/entities/quiz.dart';
import '../../../../../core/error/failures.dart';

/// Local data source for quizzes using Hive for offline storage
class QuizLocalDataSource {
  static const String _quizzesBoxName = 'quizzes';
  static const String _quizResultsBoxName = 'quiz_results';

  late Box<Map<dynamic, dynamic>> _quizzesBox;
  late Box<Map<dynamic, dynamic>> _quizResultsBox;

  /// Initialize Hive boxes for local storage
  Future<void> init() async {
    _quizzesBox = await Hive.openBox<Map<dynamic, dynamic>>(_quizzesBoxName);
    _quizResultsBox = await Hive.openBox<Map<dynamic, dynamic>>(_quizResultsBoxName);
  }

  /// Save quiz locally
  Future<Either<Failure, void>> saveQuiz(Quiz quiz) async {
    try {
      await _quizzesBox.put(quiz.id, quiz.toJson());
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to save quiz locally: $e'));
    }
  }

  /// Get quiz by ID from local storage
  Future<Either<Failure, Quiz?>> getQuiz(String id) async {
    try {
      final data = _quizzesBox.get(id);
      
      if (data == null) {
        return const Right(null);
      }
      
      final quiz = Quiz.fromJson(Map<String, dynamic>.from(data));
      return Right(quiz);
    } catch (e) {
      return Left(CacheFailure('Failed to get quiz locally: $e'));
    }
  }

  /// Get quizzes by subject from local storage
  Future<Either<Failure, List<Quiz>>> getQuizzesBySubject(String subject) async {
    try {
      final quizzes = <Quiz>[];
      
      for (final data in _quizzesBox.values) {
        if (data != null) {
          final quiz = Quiz.fromJson(Map<String, dynamic>.from(data));
          if (quiz.subject == subject) {
            quizzes.add(quiz);
          }
        }
      }
      
      // Sort by creation date (newest first)
      quizzes.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      return Right(quizzes);
    } catch (e) {
      return Left(CacheFailure('Failed to get quizzes by subject locally: $e'));
    }
  }

  /// Get quizzes by topic from local storage
  Future<Either<Failure, List<Quiz>>> getQuizzesByTopic({
    required String topic,
    String? subject,
    DifficultyLevel? difficulty,
  }) async {
    try {
      final quizzes = <Quiz>[];
      
      for (final data in _quizzesBox.values) {
        if (data != null) {
          final quiz = Quiz.fromJson(Map<String, dynamic>.from(data));
          
          // Filter by topic
          if (quiz.topic != topic) continue;
          
          // Filter by subject if specified
          if (subject != null && quiz.subject != subject) continue;
          
          // Filter by difficulty if specified
          if (difficulty != null && quiz.difficulty != difficulty) continue;
          
          quizzes.add(quiz);
        }
      }
      
      // Sort by creation date (newest first)
      quizzes.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      return Right(quizzes);
    } catch (e) {
      return Left(CacheFailure('Failed to get quizzes by topic locally: $e'));
    }
  }

  /// Search quizzes locally
  Future<Either<Failure, List<Quiz>>> searchQuizzes({
    required String query,
    String? subject,
    String? topic,
    DifficultyLevel? difficulty,
  }) async {
    try {
      final quizzes = <Quiz>[];
      final lowercaseQuery = query.toLowerCase();
      
      for (final data in _quizzesBox.values) {
        if (data != null) {
          final quiz = Quiz.fromJson(Map<String, dynamic>.from(data));
          
          // Search in title and questions
          final searchableText = '${quiz.title} ${quiz.questions.map((q) => q.question).join(' ')}'.toLowerCase();
          
          if (!searchableText.contains(lowercaseQuery)) continue;
          
          // Apply filters
          if (subject != null && quiz.subject != subject) continue;
          if (topic != null && quiz.topic != topic) continue;
          if (difficulty != null && quiz.difficulty != difficulty) continue;
          
          quizzes.add(quiz);
        }
      }
      
      // Sort by relevance (title matches first, then question matches)
      quizzes.sort((a, b) {
        final aTitleMatch = a.title.toLowerCase().contains(lowercaseQuery);
        final bTitleMatch = b.title.toLowerCase().contains(lowercaseQuery);
        
        if (aTitleMatch && !bTitleMatch) return -1;
        if (!aTitleMatch && bTitleMatch) return 1;
        
        return b.createdAt.compareTo(a.createdAt);
      });
      
      return Right(quizzes);
    } catch (e) {
      return Left(CacheFailure('Failed to search quizzes locally: $e'));
    }
  }

  /// Save quiz result locally
  Future<Either<Failure, void>> saveQuizResult(QuizResult result) async {
    try {
      await _quizResultsBox.put(result.id, result.toJson());
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to save quiz result locally: $e'));
    }
  }

  /// Get quiz results for a user from local storage
  Future<Either<Failure, List<QuizResult>>> getQuizResultsForUser({
    required String userId,
    String? subject,
    String? topic,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final results = <QuizResult>[];
      
      for (final data in _quizResultsBox.values) {
        if (data != null) {
          final result = QuizResult.fromJson(Map<String, dynamic>.from(data));
          
          // Filter by user ID
          if (result.userId != userId) continue;
          
          // Filter by subject if specified
          if (subject != null && result.subject != subject) continue;
          
          // Filter by topic if specified
          if (topic != null && result.topic != topic) continue;
          
          // Filter by date range if specified
          if (startDate != null && result.completedAt.isBefore(startDate)) continue;
          if (endDate != null && result.completedAt.isAfter(endDate)) continue;
          
          results.add(result);
        }
      }
      
      // Sort by completion date (most recent first)
      results.sort((a, b) => b.completedAt.compareTo(a.completedAt));
      
      return Right(results);
    } catch (e) {
      return Left(CacheFailure('Failed to get quiz results locally: $e'));
    }
  }

  /// Get quiz statistics for a user
  Future<Either<Failure, Map<String, dynamic>>> getQuizStats(String userId) async {
    try {
      final results = await getQuizResultsForUser(userId: userId);
      
      return results.fold(
        (failure) => Left(failure),
        (quizResults) {
          if (quizResults.isEmpty) {
            return const Right({
              'totalQuizzes': 0,
              'averageScore': 0.0,
              'totalTimeSpent': 0,
              'bestScore': 0.0,
              'recentActivity': <Map<String, dynamic>>[],
            });
          }
          
          final totalQuizzes = quizResults.length;
          final averageScore = quizResults
              .map((r) => r.score)
              .reduce((a, b) => a + b) / totalQuizzes;
          final totalTimeSpent = quizResults
              .map((r) => r.timeSpent?.inMinutes ?? 0)
              .reduce((a, b) => a + b);
          final bestScore = quizResults
              .map((r) => r.score)
              .reduce((a, b) => a > b ? a : b);
          
          // Get recent activity (last 10 quizzes)
          final recentActivity = quizResults
              .take(10)
              .map((r) => {
                'subject': r.subject,
                'topic': r.topic,
                'score': r.score,
                'completedAt': r.completedAt.toIso8601String(),
              })
              .toList();
          
          return Right({
            'totalQuizzes': totalQuizzes,
            'averageScore': averageScore,
            'totalTimeSpent': totalTimeSpent,
            'bestScore': bestScore,
            'recentActivity': recentActivity,
          });
        },
      );
    } catch (e) {
      return Left(CacheFailure('Failed to get quiz stats locally: $e'));
    }
  }

  /// Delete quiz from local storage
  Future<Either<Failure, void>> deleteQuiz(String id) async {
    try {
      await _quizzesBox.delete(id);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to delete quiz locally: $e'));
    }
  }

  /// Get cache information
  Map<String, int> getCacheInfo() {
    return {
      'quizzes_count': _quizzesBox.length,
      'quiz_results_count': _quizResultsBox.length,
    };
  }

  /// Clear all quiz data
  Future<Either<Failure, void>> clearAllData() async {
    try {
      await _quizzesBox.clear();
      await _quizResultsBox.clear();
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to clear quiz data locally: $e'));
    }
  }
}
