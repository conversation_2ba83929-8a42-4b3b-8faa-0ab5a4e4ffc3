import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:developer' as developer;

import 'models/flashcard_model.dart';
import 'models/learning_session_model.dart';
import 'models/quiz_model.dart';
import 'models/progress_model.dart';
import '../../domain/entities/flashcard.dart';
import '../../domain/entities/learning_session.dart';
import '../../domain/entities/quiz.dart';
import '../../domain/entities/learning_progress.dart';

/// Local database service for AI Tutor using Hive
/// Provides offline storage and caching for learning data
class AITutorLocalDatabase {
  static const String _flashcardsBoxName = 'flashcards';
  static const String _learningSessionsBoxName = 'learning_sessions';
  static const String _quizzesBoxName = 'quizzes';
  static const String _progressBoxName = 'learning_progress';

  static AITutorLocalDatabase? _instance;
  static AITutorLocalDatabase get instance => _instance ??= AITutorLocalDatabase._();

  AITutorLocalDatabase._();

  bool _isInitialized = false;

  /// Initialize the local database
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Get application documents directory
      final appDocumentDir = await getApplicationDocumentsDirectory();
      Hive.init(appDocumentDir.path);

      // Register adapters for Hive models
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(FlashcardModelAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(LearningSessionModelAdapter());
      }
      if (!Hive.isAdapterRegistered(2)) {
        Hive.registerAdapter(QuizModelAdapter());
      }
      if (!Hive.isAdapterRegistered(3)) {
        Hive.registerAdapter(QuizQuestionModelAdapter());
      }
      if (!Hive.isAdapterRegistered(4)) {
        Hive.registerAdapter(LearningProgressModelAdapter());
      }
      if (!Hive.isAdapterRegistered(5)) {
        Hive.registerAdapter(LearningStatsModelAdapter());
      }

      // Open boxes
      await Hive.openBox<FlashcardModel>(_flashcardsBoxName);
      await Hive.openBox<LearningSessionModel>(_learningSessionsBoxName);
      await Hive.openBox<QuizModel>(_quizzesBoxName);
      await Hive.openBox<LearningProgressModel>(_progressBoxName);

      _isInitialized = true;
      developer.log('AI Tutor local database initialized successfully');
    } catch (e) {
      developer.log('Failed to initialize AI Tutor local database: $e');
      rethrow;
    }
  }

  /// Get flashcards box
  Box<FlashcardModel> get _flashcardsBox => Hive.box<FlashcardModel>(_flashcardsBoxName);

  /// Get learning sessions box
  Box<LearningSessionModel> get _learningSessionsBox => Hive.box<LearningSessionModel>(_learningSessionsBoxName);

  /// Get quizzes box
  Box<QuizModel> get _quizzesBox => Hive.box<QuizModel>(_quizzesBoxName);

  /// Get progress box
  Box<LearningProgressModel> get _progressBox => Hive.box<LearningProgressModel>(_progressBoxName);

  // Flashcard operations

  /// Save a flashcard to local storage
  Future<void> saveFlashcard(Flashcard flashcard) async {
    await _ensureInitialized();
    final model = FlashcardModel.fromEntity(flashcard);
    await _flashcardsBox.put(flashcard.id, model);
    developer.log('Saved flashcard: ${flashcard.id}');
  }

  /// Save multiple flashcards to local storage
  Future<void> saveFlashcards(List<Flashcard> flashcards) async {
    await _ensureInitialized();
    final Map<String, FlashcardModel> flashcardMap = {};
    for (final flashcard in flashcards) {
      flashcardMap[flashcard.id] = FlashcardModel.fromEntity(flashcard);
    }
    await _flashcardsBox.putAll(flashcardMap);
    developer.log('Saved ${flashcards.length} flashcards');
  }

  /// Get a flashcard by ID
  Future<Flashcard?> getFlashcard(String id) async {
    await _ensureInitialized();
    final model = _flashcardsBox.get(id);
    return model?.toEntity();
  }

  /// Get flashcards by subject
  Future<List<Flashcard>> getFlashcardsBySubject(String subject) async {
    await _ensureInitialized();
    final models = _flashcardsBox.values.where((model) => model.subject == subject);
    return models.map((model) => model.toEntity()).toList();
  }

  /// Get flashcards by topic
  Future<List<Flashcard>> getFlashcardsByTopic(String topic) async {
    await _ensureInitialized();
    final models = _flashcardsBox.values.where((model) => model.topic == topic);
    return models.map((model) => model.toEntity()).toList();
  }

  /// Get due flashcards for review
  Future<List<Flashcard>> getDueFlashcards() async {
    await _ensureInitialized();
    final now = DateTime.now();
    final models = _flashcardsBox.values.where((model) => 
        model.nextReview.isBefore(now) || model.nextReview.isAtSameMomentAs(now));
    return models.map((model) => model.toEntity()).toList();
  }

  /// Delete a flashcard
  Future<void> deleteFlashcard(String id) async {
    await _ensureInitialized();
    await _flashcardsBox.delete(id);
    developer.log('Deleted flashcard: $id');
  }

  // Learning session operations

  /// Save a learning session
  Future<void> saveLearningSession(LearningSession session) async {
    await _ensureInitialized();
    final model = LearningSessionModel.fromEntity(session);
    await _learningSessionsBox.put(session.id, model);
    developer.log('Saved learning session: ${session.id}');
  }

  /// Get learning sessions by user ID
  Future<List<LearningSession>> getLearningSessionsByUser(String userId) async {
    await _ensureInitialized();
    final models = _learningSessionsBox.values.where((model) => model.userId == userId);
    return models.map((model) => model.toEntity()).toList();
  }

  /// Get recent learning sessions
  Future<List<LearningSession>> getRecentLearningSessions({int limit = 10}) async {
    await _ensureInitialized();
    final models = _learningSessionsBox.values.toList()
      ..sort((a, b) => b.startTime.compareTo(a.startTime));
    final limitedModels = models.take(limit);
    return limitedModels.map((model) => model.toEntity()).toList();
  }

  // Quiz operations

  /// Save a quiz
  Future<void> saveQuiz(Quiz quiz) async {
    await _ensureInitialized();
    final model = QuizModel.fromEntity(quiz);
    await _quizzesBox.put(quiz.id, model);
    developer.log('Saved quiz: ${quiz.id}');
  }

  /// Get a quiz by ID
  Future<Quiz?> getQuiz(String id) async {
    await _ensureInitialized();
    final model = _quizzesBox.get(id);
    return model?.toEntity();
  }

  /// Get quizzes by subject
  Future<List<Quiz>> getQuizzesBySubject(String subject) async {
    await _ensureInitialized();
    final models = _quizzesBox.values.where((model) => model.subject == subject);
    return models.map((model) => model.toEntity()).toList();
  }

  /// Search quizzes by title or topic
  Future<List<Quiz>> searchQuizzes(String query) async {
    await _ensureInitialized();
    final lowerQuery = query.toLowerCase();
    final models = _quizzesBox.values.where((model) => 
        model.title.toLowerCase().contains(lowerQuery) ||
        model.topic.toLowerCase().contains(lowerQuery) ||
        model.subject.toLowerCase().contains(lowerQuery));
    return models.map((model) => model.toEntity()).toList();
  }

  // Progress operations

  /// Save learning progress
  Future<void> saveLearningProgress(LearningProgress progress) async {
    await _ensureInitialized();
    final model = LearningProgressModel.fromEntity(progress);
    await _progressBox.put(progress.id, model);
    developer.log('Saved learning progress: ${progress.id}');
  }

  /// Get learning progress by user and subject
  Future<LearningProgress?> getLearningProgress(String userId, String subject) async {
    await _ensureInitialized();
    final model = _progressBox.values.firstWhere(
      (model) => model.userId == userId && model.subject == subject,
      orElse: () => throw StateError('No progress found'),
    );
    return model.toEntity();
  }

  /// Get all learning progress for a user
  Future<List<LearningProgress>> getAllLearningProgress(String userId) async {
    await _ensureInitialized();
    final models = _progressBox.values.where((model) => model.userId == userId);
    return models.map((model) => model.toEntity()).toList();
  }

  // Utility methods

  /// Ensure database is initialized
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Clear all data (for testing or reset)
  Future<void> clearAllData() async {
    await _ensureInitialized();
    await _flashcardsBox.clear();
    await _learningSessionsBox.clear();
    await _quizzesBox.clear();
    await _progressBox.clear();
    developer.log('Cleared all local database data');
  }

  /// Get database statistics
  Future<Map<String, int>> getDatabaseStats() async {
    await _ensureInitialized();
    return {
      'flashcards': _flashcardsBox.length,
      'learning_sessions': _learningSessionsBox.length,
      'quizzes': _quizzesBox.length,
      'progress_records': _progressBox.length,
    };
  }

  /// Close the database
  Future<void> close() async {
    if (_isInitialized) {
      await Hive.close();
      _isInitialized = false;
      developer.log('AI Tutor local database closed');
    }
  }
}
