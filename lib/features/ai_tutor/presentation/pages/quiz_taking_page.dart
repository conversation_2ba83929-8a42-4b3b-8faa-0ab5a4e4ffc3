import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/ai_tutor_bloc.dart';
import '../../domain/entities/quiz.dart';

/// Page for taking quizzes with interactive questions
class QuizTakingPage extends StatefulWidget {
  final String quizId;
  final Quiz? quiz; // Optional pre-loaded quiz

  const QuizTakingPage({
    super.key,
    required this.quizId,
    this.quiz,
  });

  @override
  State<QuizTakingPage> createState() => _QuizTakingPageState();
}

class _QuizTakingPageState extends State<QuizTakingPage> {
  Quiz? _quiz;
  int _currentQuestionIndex = 0;
  List<QuizAnswer> _answers = [];
  List<String> _currentAnswers = [];
  bool _showFeedback = false;
  bool _isAnswerSubmitted = false;

  @override
  void initState() {
    super.initState();
    if (widget.quiz != null) {
      _quiz = widget.quiz;
    } else {
      _startQuiz();
    }
  }

  void _startQuiz() {
    context.read<AITutorBloc>().add(StartQuizEvent(quizId: widget.quizId));
  }

  void _submitAnswer() {
    if (_quiz == null || _currentAnswers.isEmpty) return;

    final currentQuestion = _quiz!.questions[_currentQuestionIndex];
    
    setState(() {
      _isAnswerSubmitted = true;
      _showFeedback = true;
    });

    context.read<AITutorBloc>().add(SubmitQuizAnswerEvent(
      questionId: currentQuestion.id,
      answers: _currentAnswers,
    ));
  }

  void _nextQuestion() {
    if (_quiz == null) return;

    if (_currentQuestionIndex < _quiz!.questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
        _currentAnswers.clear();
        _showFeedback = false;
        _isAnswerSubmitted = false;
      });
    } else {
      _completeQuiz();
    }
  }

  void _completeQuiz() {
    if (_quiz == null) return;

    context.read<AITutorBloc>().add(CompleteQuizEvent(
      quizId: widget.quizId,
      answers: _answers,
    ));
  }

  void _selectAnswer(String answer) {
    if (_isAnswerSubmitted) return;

    final currentQuestion = _quiz!.questions[_currentQuestionIndex];
    
    setState(() {
      if (currentQuestion.type == QuestionType.multipleChoice && 
          !currentQuestion.options.contains('Select all that apply')) {
        // Single selection
        _currentAnswers = [answer];
      } else {
        // Multiple selection
        if (_currentAnswers.contains(answer)) {
          _currentAnswers.remove(answer);
        } else {
          _currentAnswers.add(answer);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_quiz?.title ?? 'Quiz'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        actions: [
          if (_quiz != null)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: Text(
                  '${_currentQuestionIndex + 1} / ${_quiz!.questions.length}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
            ),
        ],
      ),
      body: BlocListener<AITutorBloc, AITutorState>(
        listener: (context, state) {
          if (state is QuizStarted) {
            setState(() {
              _quiz = state.quiz;
              _currentQuestionIndex = state.currentQuestionIndex;
              _answers = List.from(state.answers);
            });
          } else if (state is QuizAnswerSubmitted) {
            setState(() {
              _answers = List.from(state.answers);
            });
          } else if (state is QuizCompleted) {
            _showResultsDialog(state.result);
          } else if (state is AITutorError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: BlocBuilder<AITutorBloc, AITutorState>(
          builder: (context, state) {
            if (state is AITutorLoading) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Loading quiz...'),
                  ],
                ),
              );
            }

            if (_quiz == null || _quiz!.questions.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.quiz, size: 64, color: Colors.grey),
                    const SizedBox(height: 16),
                    const Text('No quiz questions available'),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Go Back'),
                    ),
                  ],
                ),
              );
            }

            final currentQuestion = _quiz!.questions[_currentQuestionIndex];

            return Column(
              children: [
                // Progress indicator
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      LinearProgressIndicator(
                        value: (_currentQuestionIndex + 1) / _quiz!.questions.length,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).primaryColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Question ${_currentQuestionIndex + 1} of ${_quiz!.questions.length}'),
                          Text('${currentQuestion.points} points'),
                        ],
                      ),
                    ],
                  ),
                ),

                // Question content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Question
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      Icons.help_outline,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        currentQuestion.question,
                                        style: Theme.of(context).textTheme.titleLarge,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Type: ${currentQuestion.type.displayName}',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Answer options
                        if (currentQuestion.type == QuestionType.multipleChoice ||
                            currentQuestion.type == QuestionType.trueFalse)
                          ...currentQuestion.options.map((option) => Card(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: ListTile(
                              leading: _currentAnswers.contains(option)
                                  ? Icon(Icons.check_circle, color: Theme.of(context).primaryColor)
                                  : const Icon(Icons.radio_button_unchecked),
                              title: Text(option),
                              onTap: () => _selectAnswer(option),
                              enabled: !_isAnswerSubmitted,
                            ),
                          )),

                        // Text input for other question types
                        if (currentQuestion.type == QuestionType.fillInBlank ||
                            currentQuestion.type == QuestionType.shortAnswer)
                          TextField(
                            decoration: const InputDecoration(
                              labelText: 'Your answer',
                              border: OutlineInputBorder(),
                            ),
                            enabled: !_isAnswerSubmitted,
                            onChanged: (value) {
                              setState(() {
                                _currentAnswers = [value];
                              });
                            },
                          ),

                        // Feedback section
                        if (_showFeedback) ...[
                          const SizedBox(height: 16),
                          Card(
                            color: currentQuestion.isCorrect(_currentAnswers)
                                ? Colors.green[50]
                                : Colors.red[50],
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        currentQuestion.isCorrect(_currentAnswers)
                                            ? Icons.check_circle
                                            : Icons.cancel,
                                        color: currentQuestion.isCorrect(_currentAnswers)
                                            ? Colors.green
                                            : Colors.red,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        currentQuestion.isCorrect(_currentAnswers)
                                            ? 'Correct!'
                                            : 'Incorrect',
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          color: currentQuestion.isCorrect(_currentAnswers)
                                              ? Colors.green
                                              : Colors.red,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  if (currentQuestion.explanation.isNotEmpty) ...[
                                    const SizedBox(height: 8),
                                    Text(
                                      'Explanation:',
                                      style: Theme.of(context).textTheme.titleSmall,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(currentQuestion.explanation),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                // Action buttons
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      if (!_isAnswerSubmitted)
                        ElevatedButton(
                          onPressed: _currentAnswers.isNotEmpty ? _submitAnswer : null,
                          child: const Text('Submit Answer'),
                        ),
                      if (_isAnswerSubmitted)
                        ElevatedButton(
                          onPressed: _nextQuestion,
                          child: Text(
                            _currentQuestionIndex < _quiz!.questions.length - 1
                                ? 'Next Question'
                                : 'Complete Quiz',
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _showResultsDialog(QuizResult result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Quiz Complete!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              result.percentage >= 70 ? Icons.celebration : Icons.sentiment_satisfied,
              size: 64,
              color: result.percentage >= 70 ? Colors.green : Colors.orange,
            ),
            const SizedBox(height: 16),
            Text('Score: ${result.percentage.round()}%'),
            Text('${result.correctAnswers}/${result.totalQuestions} correct'),
            const SizedBox(height: 16),
            Text(
              _getResultMessage(result.percentage),
              style: const TextStyle(fontStyle: FontStyle.italic),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to previous page
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  String _getResultMessage(double percentage) {
    if (percentage >= 90) return 'Outstanding! You\'ve mastered this topic!';
    if (percentage >= 80) return 'Excellent work! Keep it up!';
    if (percentage >= 70) return 'Good job! You\'re on the right track!';
    if (percentage >= 60) return 'Not bad! A little more study and you\'ll improve!';
    return 'Keep practicing! Every attempt makes you better!';
  }
}
