import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/ai_tutor_bloc.dart';
import '../widgets/flashcard_widget.dart';
import '../../domain/entities/flashcard.dart';

/// Page for reviewing flashcards with spaced repetition
class FlashcardReviewPage extends StatefulWidget {
  final String? subject;
  final String? topic;
  final bool dueOnly;

  const FlashcardReviewPage({
    super.key,
    this.subject,
    this.topic,
    this.dueOnly = true,
  });

  @override
  State<FlashcardReviewPage> createState() => _FlashcardReviewPageState();
}

class _FlashcardReviewPageState extends State<FlashcardReviewPage> {
  int _currentIndex = 0;
  List<Flashcard> _flashcards = [];
  int _reviewedCount = 0;
  int _correctCount = 0;

  @override
  void initState() {
    super.initState();
    _loadFlashcards();
  }

  void _loadFlashcards() {
    context.read<AITutorBloc>().add(LoadFlashcardsEvent(
      subject: widget.subject,
      topic: widget.topic,
      dueOnly: widget.dueOnly,
    ));
  }

  void _onFlashcardResponse(FlashcardResponse response) {
    if (_currentIndex < _flashcards.length) {
      final currentFlashcard = _flashcards[_currentIndex];
      
      // Track statistics
      setState(() {
        _reviewedCount++;
        if (response == FlashcardResponse.easy || response == FlashcardResponse.good) {
          _correctCount++;
        }
      });

      // Submit review to BLoC
      context.read<AITutorBloc>().add(ReviewFlashcardEvent(
        flashcardId: currentFlashcard.id,
        response: response,
      ));

      // Move to next flashcard after a short delay
      Future.delayed(const Duration(milliseconds: 500), () {
        _nextFlashcard();
      });
    }
  }

  void _nextFlashcard() {
    if (_currentIndex < _flashcards.length - 1) {
      setState(() {
        _currentIndex++;
      });
    } else {
      _showCompletionDialog();
    }
  }

  void _previousFlashcard() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
      });
    }
  }

  void _showCompletionDialog() {
    final accuracy = _reviewedCount > 0 ? (_correctCount / _reviewedCount * 100).round() : 0;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Review Complete!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.celebration, size: 64, color: Colors.green),
            const SizedBox(height: 16),
            Text('You reviewed $_reviewedCount flashcards'),
            Text('Accuracy: $accuracy%'),
            const SizedBox(height: 16),
            Text(
              _getEncouragementMessage(accuracy),
              style: const TextStyle(fontStyle: FontStyle.italic),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to previous page
            },
            child: const Text('Done'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              _resetReview();
            },
            child: const Text('Review Again'),
          ),
        ],
      ),
    );
  }

  String _getEncouragementMessage(int accuracy) {
    if (accuracy >= 90) return 'Excellent work! You\'ve mastered these concepts!';
    if (accuracy >= 75) return 'Great job! Keep up the good work!';
    if (accuracy >= 60) return 'Good progress! A few more reviews and you\'ll have it!';
    return 'Keep practicing! Every review makes you stronger!';
  }

  void _resetReview() {
    setState(() {
      _currentIndex = 0;
      _reviewedCount = 0;
      _correctCount = 0;
    });
    _loadFlashcards();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.dueOnly ? 'Review Due Cards' : 'Study Flashcards'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        actions: [
          if (_flashcards.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: Text(
                  '${_currentIndex + 1} / ${_flashcards.length}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
            ),
        ],
      ),
      body: BlocListener<AITutorBloc, AITutorState>(
        listener: (context, state) {
          if (state is FlashcardsLoaded) {
            setState(() {
              _flashcards = state.flashcards;
              _currentIndex = 0;
            });
          } else if (state is AITutorError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: BlocBuilder<AITutorBloc, AITutorState>(
          builder: (context, state) {
            if (state is AITutorLoading) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Loading flashcards...'),
                  ],
                ),
              );
            }

            if (_flashcards.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      size: 64,
                      color: Colors.green[300],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      widget.dueOnly 
                        ? 'No cards due for review!'
                        : 'No flashcards found!',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.dueOnly
                        ? 'Great job! Check back later for more reviews.'
                        : 'Create some flashcards to start studying.',
                      style: Theme.of(context).textTheme.bodyLarge,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Go Back'),
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: [
                // Progress indicator
                if (_flashcards.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        LinearProgressIndicator(
                          value: (_currentIndex + 1) / _flashcards.length,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).primaryColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('Progress: ${_currentIndex + 1}/${_flashcards.length}'),
                            if (_reviewedCount > 0)
                              Text('Accuracy: ${(_correctCount / _reviewedCount * 100).round()}%'),
                          ],
                        ),
                      ],
                    ),
                  ),

                // Flashcard
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: _currentIndex < _flashcards.length
                        ? FlashcardWidget(
                            flashcard: _flashcards[_currentIndex],
                            onResponse: _onFlashcardResponse,
                          )
                        : const SizedBox.shrink(),
                  ),
                ),

                // Navigation buttons
                if (_flashcards.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton.icon(
                          onPressed: _currentIndex > 0 ? _previousFlashcard : null,
                          icon: const Icon(Icons.arrow_back),
                          label: const Text('Previous'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[300],
                            foregroundColor: Colors.black87,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _currentIndex < _flashcards.length - 1 ? _nextFlashcard : null,
                          icon: const Icon(Icons.arrow_forward),
                          label: const Text('Next'),
                        ),
                      ],
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }
}
