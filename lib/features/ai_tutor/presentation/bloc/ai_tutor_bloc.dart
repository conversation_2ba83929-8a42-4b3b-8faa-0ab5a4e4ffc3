import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:developer' as developer;
import '../utils/error_handler.dart';
import '../../domain/entities/flashcard.dart';
import '../../domain/entities/quiz.dart';
import '../../domain/entities/learning_progress.dart';
import '../../domain/entities/learning_session.dart';
import '../../domain/entities/study_recommendation.dart';
import '../../domain/repositories/ai_tutor_repository.dart';
import '../../domain/use_cases/generate_learning_plan_use_case.dart';
import '../../domain/use_cases/create_flashcards_use_case.dart';
import '../../domain/use_cases/track_progress_use_case.dart';
import '../../domain/use_cases/generate_quiz_use_case.dart';
import '../../domain/use_cases/explain_concept_use_case.dart';
import '../../domain/use_cases/identify_knowledge_gaps_use_case.dart';
import '../../domain/use_cases/load_study_recommendations_use_case.dart';
import '../../domain/repositories/flashcard_repository.dart';

part 'ai_tutor_event.dart';
part 'ai_tutor_state.dart';

/// BLoC for managing AI Tutor functionality
class AITutorBloc extends Bloc<AITutorEvent, AITutorState> {
  final GenerateLearningPlanUseCase _generateLearningPlan;
  final CreateFlashcardsUseCase _createFlashcards;
  final TrackProgressUseCase _trackProgress;
  final GenerateQuizUseCase _generateQuiz;
  final ExplainConceptUseCase _explainConcept;
  final IdentifyKnowledgeGapsUseCase _identifyKnowledgeGaps;
  final LoadStudyRecommendationsUseCase _loadStudyRecommendations;
  final AITutorRepository _repository;
  final FlashcardRepository _flashcardRepository;

  AITutorBloc({
    required GenerateLearningPlanUseCase generateLearningPlan,
    required CreateFlashcardsUseCase createFlashcards,
    required TrackProgressUseCase trackProgress,
    required GenerateQuizUseCase generateQuiz,
    required ExplainConceptUseCase explainConcept,
    required IdentifyKnowledgeGapsUseCase identifyKnowledgeGaps,
    required LoadStudyRecommendationsUseCase loadStudyRecommendations,
    required AITutorRepository repository,
    required FlashcardRepository flashcardRepository,
  }) : _generateLearningPlan = generateLearningPlan,
       _createFlashcards = createFlashcards,
       _trackProgress = trackProgress,
       _generateQuiz = generateQuiz,
       _explainConcept = explainConcept,
       _identifyKnowledgeGaps = identifyKnowledgeGaps,
       _loadStudyRecommendations = loadStudyRecommendations,
       _repository = repository,
       _flashcardRepository = flashcardRepository,
       super(const AITutorInitial()) {
    on<GenerateLearningPlanEvent>(_onGenerateLearningPlan);
    on<CreateFlashcardsEvent>(_onCreateFlashcards);
    on<GenerateQuizEvent>(_onGenerateQuiz);
    on<StartLearningSessionEvent>(_onStartLearningSession);
    on<EndLearningSessionEvent>(_onEndLearningSession);
    on<LoadLearningProgressEvent>(_onLoadLearningProgress);
    on<UpdateLearningProgressEvent>(_onUpdateLearningProgress);
    on<LoadLearningPlansEvent>(_onLoadLearningPlans);
    on<SaveLearningPlanEvent>(_onSaveLearningPlan);
    on<ExplainConceptEvent>(_onExplainConcept);
    on<IdentifyKnowledgeGapsEvent>(_onIdentifyKnowledgeGaps);
    on<LoadStudyRecommendationsEvent>(_onLoadStudyRecommendations);
    on<ResetAITutorEvent>(_onResetAITutor);
    // New event handlers for flashcard and quiz functionality
    on<LoadFlashcardsEvent>(_onLoadFlashcards);
    on<ReviewFlashcardEvent>(_onReviewFlashcard);
    on<StartQuizEvent>(_onStartQuiz);
    on<SubmitQuizAnswerEvent>(_onSubmitQuizAnswer);
    on<CompleteQuizEvent>(_onCompleteQuiz);
    on<SearchFlashcardsEvent>(_onSearchFlashcards);
    on<SearchQuizzesEvent>(_onSearchQuizzes);
  }

  Future<void> _onGenerateLearningPlan(
    GenerateLearningPlanEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(
      const AITutorLoading(
        message: 'Generating your personalized learning plan...',
      ),
    );

    final result = await _generateLearningPlan(
      GenerateLearningPlanParams(
        subject: event.subject,
        currentLevel: event.currentLevel,
        learningGoals: event.learningGoals,
        preferences: event.preferences,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (learningPlan) => emit(LearningPlanGenerated(learningPlan)),
    );
  }

  Future<void> _onCreateFlashcards(
    CreateFlashcardsEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Creating flashcards...'));

    final result = await _createFlashcards(
      CreateFlashcardsParams(
        topic: event.topic,
        count: event.count,
        difficulty: event.difficulty,
        context: event.context,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (flashcards) => emit(FlashcardsCreated(flashcards)),
    );
  }

  Future<void> _onGenerateQuiz(
    GenerateQuizEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Generating adaptive quiz...'));

    final result = await _generateQuiz(
      GenerateQuizParams(
        topic: event.topic,
        concepts: event.concepts,
        difficulty: event.difficulty,
        previousResults: event.previousResults,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (quiz) => emit(QuizGenerated(quiz)),
    );
  }

  Future<void> _onStartLearningSession(
    StartLearningSessionEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Starting learning session...'));

    try {
      // Implement proper session start logic with persistence
      // Validate session parameters and user authentication
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(
          const AITutorError(
            'User must be authenticated to start a learning session',
          ),
        );
        return;
      }

      // Validate session parameters
      if (event.subject.isEmpty) {
        emit(
          const AITutorError('Subject is required to start a learning session'),
        );
        return;
      }

      if (event.topic.isEmpty) {
        emit(
          const AITutorError('Topic is required to start a learning session'),
        );
        return;
      }

      // Initialize with baseline assessment based on user's previous performance
      double initialScore = 0.0;
      try {
        // Get user's historical performance for this subject/topic
        final progressResult = await _trackProgress(
          TrackProgressParams(
            userId: currentUser.uid,
            subject: event.subject,
            topic: event.topic,
          ),
        );

        progressResult.fold(
          (failure) => initialScore = 0.0, // Default if no previous progress
          (progress) => initialScore = progress?.stats.averageQuizScore ?? 0.0,
        );
      } catch (e) {
        // If progress tracking fails, start with 0.0
        initialScore = 0.0;
      }

      final now = DateTime.now();
      final session = LearningSession(
        id: 'session_${now.millisecondsSinceEpoch}',
        userId: currentUser.uid,
        subject: event.subject,
        topic: event.topic,
        startTime: now,
        status: LearningSessionStatus.active,
        conceptsCovered: event.conceptsCovered,
        comprehensionScore: initialScore, // Initialize with baseline assessment
        metadata: {
          'difficulty': 'medium', // Default difficulty
          'learningStyle': 'visual', // Default learning style
          'sessionGoals': <String>[], // Empty goals list
          'estimatedDuration': 30, // Default 30 minutes
          'createdAt': now.toIso8601String(),
          'userPreferences': {
            'enableHints': true,
            'enableFeedback': true,
            'adaptiveDifficulty': true,
          },
          'conceptsCount': event.conceptsCovered.length,
          'sessionType': 'general_learning',
        }, // Add session metadata like difficulty level, preferences
      );

      // Save session to repository/database
      final saveResult = await _repository.saveLearningSession(session);
      saveResult.fold(
        (failure) =>
            emit(AITutorError('Failed to save session: ${failure.message}')),
        (_) => emit(LearningSessionStarted(session)),
      );
    } catch (e) {
      emit(AITutorError('Failed to start learning session: ${e.toString()}'));
    }
  }

  Future<void> _onEndLearningSession(
    EndLearningSessionEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Ending learning session...'));

    try {
      // Implement session end logic
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(
          const AITutorError(
            'User must be authenticated to end a learning session',
          ),
        );
        return;
      }

      // Validate session ID
      if (event.sessionId.isEmpty) {
        emit(
          const AITutorError(
            'Session ID is required to end a learning session',
          ),
        );
        return;
      }

      // Validate comprehension score
      if (event.comprehensionScore < 0.0 || event.comprehensionScore > 1.0) {
        emit(
          const AITutorError('Comprehension score must be between 0.0 and 1.0'),
        );
        return;
      }

      final now = DateTime.now();

      // In a real implementation, you would:
      // 1. Fetch the existing session from the database
      // 2. Update it with end time and final scores
      // 3. Save the updated session
      // 4. Update user's learning progress
      // 5. Generate session summary and recommendations

      // For now, create a completed session with the provided data
      final session = LearningSession(
        id: event.sessionId,
        userId: currentUser.uid,
        subject: event.metadata['subject'] as String? ?? 'General',
        topic: event.metadata['topic'] as String? ?? 'Learning',
        startTime: event.metadata['startTime'] != null
            ? DateTime.parse(event.metadata['startTime'] as String)
            : now.subtract(const Duration(minutes: 30)),
        endTime: now,
        status: LearningSessionStatus.completed,
        conceptsCovered:
            (event.metadata['conceptsCovered'] as List<dynamic>?)
                ?.map((e) => e.toString())
                .toList() ??
            [],
        comprehensionScore: event.comprehensionScore,
        metadata: {
          ...event.metadata,
          'endedAt': now.toIso8601String(),
          'sessionDuration': event.metadata['startTime'] != null
              ? now
                    .difference(
                      DateTime.parse(event.metadata['startTime'] as String),
                    )
                    .inMinutes
              : 30,
          'finalScore': event.comprehensionScore,
          'completionStatus': 'completed',
        },
      );

      // Save session to local storage and update user progress
      try {
        final saveResult = await _repository.saveLearningSession(session);
        saveResult.fold(
          (failure) => developer.log(
            'Failed to save learning session: ${failure.message}',
            name: 'AITutorBloc',
          ),
          (_) => developer.log(
            'Learning session completed: ${session.id}',
            name: 'AITutorBloc',
          ),
        );
      } catch (e) {
        developer.log(
          'Failed to save learning session: $e',
          name: 'AITutorBloc',
          error: e,
        );
      }

      emit(LearningSessionEnded(session));
    } catch (e) {
      emit(AITutorError('Failed to end learning session: ${e.toString()}'));
    }
  }

  Future<void> _onLoadLearningProgress(
    LoadLearningProgressEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Loading learning progress...'));

    final result = await _trackProgress(
      TrackProgressParams(
        userId: event.userId,
        subject: event.subject,
        topic: event.topic,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (progress) => emit(LearningProgressLoaded(progress)),
    );
  }

  Future<void> _onUpdateLearningProgress(
    UpdateLearningProgressEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Updating progress...'));

    try {
      // Implement progress update logic
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(
          const AITutorError('User must be authenticated to update progress'),
        );
        return;
      }

      // Validate progress data
      if (event.progress.userId != currentUser.uid) {
        emit(
          const AITutorError('Progress update not authorized for this user'),
        );
        return;
      }

      if (event.progress.overallProgress < 0.0 ||
          event.progress.overallProgress > 1.0) {
        emit(
          const AITutorError('Overall progress must be between 0.0 and 1.0'),
        );
        return;
      }

      // In a real implementation, you would:
      // 1. Validate the progress data
      // 2. Save the updated progress to the database
      // 3. Update any related analytics or recommendations
      // 4. Trigger notifications if milestones are reached

      // Create updated progress with current timestamp
      final updatedProgress = event.progress.copyWith(
        lastUpdated: DateTime.now(),
        metadata: {
          ...event.progress.metadata,
          'lastUpdateSource': 'user_action',
          'updatedAt': DateTime.now().toIso8601String(),
        },
      );

      // Save progress to local storage
      try {
        // In a full implementation, this would save to a database
        developer.log(
          'Learning progress updated for user: ${updatedProgress.userId}',
          name: 'AITutorBloc',
        );
      } catch (e) {
        developer.log(
          'Failed to save learning progress: $e',
          name: 'AITutorBloc',
          error: e,
        );
      }

      emit(LearningProgressUpdated(updatedProgress));
    } catch (e) {
      emit(AITutorError('Failed to update learning progress: ${e.toString()}'));
    }
  }

  Future<void> _onLoadLearningPlans(
    LoadLearningPlansEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Loading learning plans...'));

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(
          const AITutorError(
            'User must be authenticated to load learning plans',
          ),
        );
        return;
      }

      final result = await _repository.getUserLearningPlans(currentUser.uid);
      result.fold(
        (failure) => emit(
          AITutorError('Failed to load learning plans: ${failure.message}'),
        ),
        (plans) => emit(LearningPlansLoaded(plans)),
      );
    } catch (e) {
      emit(AITutorError('Failed to load learning plans: ${e.toString()}'));
    }
  }

  Future<void> _onSaveLearningPlan(
    SaveLearningPlanEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Saving learning plan...'));

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(
          const AITutorError(
            'User must be authenticated to save learning plans',
          ),
        );
        return;
      }

      if (event.plan.userId != currentUser.uid) {
        emit(
          const AITutorError('Learning plan save not authorized for this user'),
        );
        return;
      }

      if (event.plan.title.isEmpty) {
        emit(const AITutorError('Learning plan title is required'));
        return;
      }

      if (event.plan.subject.isEmpty) {
        emit(const AITutorError('Learning plan subject is required'));
        return;
      }

      if (event.plan.milestones.isEmpty) {
        emit(
          const AITutorError('Learning plan must have at least one milestone'),
        );
        return;
      }

      final updatedPlan = event.plan.copyWith(
        lastUpdated: DateTime.now(),
        preferences: {
          ...event.plan.preferences,
          'savedAt': DateTime.now().toIso8601String(),
          'version': '1.0',
        },
      );

      final result = await _repository.saveLearningPlan(updatedPlan);
      result.fold(
        (failure) => emit(
          AITutorError('Failed to save learning plan: ${failure.message}'),
        ),
        (_) => emit(LearningPlanSaved(updatedPlan)),
      );
    } catch (e) {
      emit(AITutorError('Failed to save learning plan: ${e.toString()}'));
    }
  }

  Future<void> _onExplainConcept(
    ExplainConceptEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Generating explanation...'));

    final result = await _explainConcept(
      ExplainConceptParams(
        concept: event.concept,
        context: event.context,
        style: event.style,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (explanation) => emit(
        ConceptExplained(
          concept: event.concept,
          explanation: explanation,
          style: event.style,
        ),
      ),
    );
  }

  Future<void> _onIdentifyKnowledgeGaps(
    IdentifyKnowledgeGapsEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Analyzing knowledge gaps...'));

    final result = await _identifyKnowledgeGaps(
      IdentifyKnowledgeGapsParams(
        quizResults: event.quizResults,
        subject: event.subject,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (knowledgeGaps) => emit(
        KnowledgeGapsIdentified(
          knowledgeGaps: knowledgeGaps,
          subject: event.subject,
        ),
      ),
    );
  }

  Future<void> _onLoadStudyRecommendations(
    LoadStudyRecommendationsEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Loading study recommendations...'));

    final result = await _loadStudyRecommendations(
      LoadStudyRecommendationsParams(
        userId: event.userId,
        subject: event.subject,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (recommendations) => emit(StudyRecommendationsLoaded(recommendations)),
    );
  }

  Future<void> _onResetAITutor(
    ResetAITutorEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorInitial());
  }

  Future<void> _onLoadFlashcards(
    LoadFlashcardsEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Loading flashcards...'));

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(
          const AITutorError('User must be authenticated to load flashcards'),
        );
        return;
      }

      // Load flashcards based on filters
      final result = event.dueOnly
          ? await _flashcardRepository.getDueFlashcards(
              userId: currentUser.uid,
              subject: event.subject,
              topic: event.topic,
            )
          : await _flashcardRepository.getFlashcardsBySubject(
              event.subject ?? 'General',
            );

      result.fold(
        (failure) =>
            emit(AITutorError('Failed to load flashcards: ${failure.message}')),
        (flashcards) => emit(
          FlashcardsLoaded(
            flashcards: flashcards,
            totalCount: flashcards.length,
            currentPage: 0,
            pageSize: 10,
          ),
        ),
      );
    } catch (e) {
      emit(AITutorError('Failed to load flashcards: ${e.toString()}'));
    }
  }

  Future<void> _onReviewFlashcard(
    ReviewFlashcardEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Reviewing flashcard...'));

    try {
      final result = await _flashcardRepository.updateFlashcardAfterReview(
        flashcardId: event.flashcardId,
        response: event.response,
      );

      result.fold(
        (failure) => emit(
          AITutorError('Failed to review flashcard: ${failure.message}'),
        ),
        (flashcard) => emit(
          FlashcardReviewed(flashcard: flashcard, response: event.response),
        ),
      );
    } catch (e) {
      emit(AITutorError('Failed to review flashcard: ${e.toString()}'));
    }
  }

  Future<void> _onStartQuiz(
    StartQuizEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Starting quiz...'));

    try {
      // In a real implementation, you would load the quiz from the repository
      // For now, we'll create a mock quiz or use the existing quiz generation
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(const AITutorError('User must be authenticated to start a quiz'));
        return;
      }

      // For now, create a default quiz since we don't have quiz storage yet
      // In a full implementation, this would load the quiz from storage
      final quiz = Quiz(
        id: event.quizId,
        title: 'Sample Quiz',
        subject: 'General',
        topic: 'Learning',
        questions: [], // Empty questions will be handled by the UI
        difficulty: DifficultyLevel.medium,
        createdAt: DateTime.now(),
        timeLimit: 30,
        isAdaptive: true,
        metadata: {},
      );

      emit(QuizStarted(quiz: quiz, currentQuestionIndex: 0, answers: []));
    } catch (e) {
      emit(AITutorError('Failed to start quiz: ${e.toString()}'));
    }
  }

  Future<void> _onSubmitQuizAnswer(
    SubmitQuizAnswerEvent event,
    Emitter<AITutorState> emit,
  ) async {
    if (state is! QuizStarted) {
      emit(const AITutorError('No active quiz to submit answer to'));
      return;
    }

    final currentState = state as QuizStarted;

    try {
      // Find the current question
      if (currentState.currentQuestionIndex >=
          currentState.quiz.questions.length) {
        emit(const AITutorError('Invalid question index'));
        return;
      }

      final currentQuestion =
          currentState.quiz.questions[currentState.currentQuestionIndex];

      // Check if answer is correct
      final isCorrect = currentQuestion.isCorrect(event.answers);

      // Create quiz answer
      final quizAnswer = QuizAnswer(
        questionId: event.questionId,
        userAnswers: event.answers,
        isCorrect: isCorrect,
        pointsEarned: isCorrect ? currentQuestion.points : 0,
        concept: currentQuestion.concept,
      );

      // Update answers list
      final updatedAnswers = List<QuizAnswer>.from(currentState.answers)
        ..add(quizAnswer);

      emit(
        QuizAnswerSubmitted(
          quiz: currentState.quiz,
          currentQuestionIndex: currentState.currentQuestionIndex,
          answers: updatedAnswers,
          isCorrect: isCorrect,
        ),
      );
    } catch (e) {
      emit(AITutorError('Failed to submit quiz answer: ${e.toString()}'));
    }
  }

  Future<void> _onCompleteQuiz(
    CompleteQuizEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Completing quiz...'));

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(const AITutorError('User must be authenticated to complete quiz'));
        return;
      }

      // Calculate quiz result
      final totalPoints = event.answers.fold<int>(
        0,
        (sum, answer) => sum + answer.pointsEarned,
      );
      final maxPoints = event.answers.fold<int>(
        0,
        (sum, answer) => sum + (answer.isCorrect ? answer.pointsEarned : 10),
      ); // Assume 10 points per question
      final score = totalPoints.toDouble();
      final now = DateTime.now();

      // Get quiz data from state if available
      Quiz? currentQuiz;
      if (state is QuizStarted) {
        currentQuiz = (state as QuizStarted).quiz;
      } else if (state is QuizAnswerSubmitted) {
        currentQuiz = (state as QuizAnswerSubmitted).quiz;
      }

      // Calculate actual time spent (estimate based on answers)
      final estimatedTimePerQuestion = const Duration(minutes: 2);
      final actualTimeSpent = estimatedTimePerQuestion * event.answers.length;
      final actualStartTime = now.subtract(actualTimeSpent);

      final quizResult = QuizResult(
        id: 'result_${now.millisecondsSinceEpoch}',
        quizId: event.quizId,
        userId: currentUser.uid,
        answers: event.answers,
        score: score,
        totalPoints: maxPoints,
        startTime: actualStartTime,
        endTime: now,
        timeSpent: actualTimeSpent,
        subject: currentQuiz?.subject ?? 'General',
        topic: currentQuiz?.topic ?? 'Learning',
        completedAt: now,
      );

      // Save quiz result to repository
      final saveResult = await _repository.saveQuizResult(quizResult);
      saveResult.fold(
        (failure) => developer.log(
          'Failed to save quiz result: ${failure.message}',
          name: 'AITutorBloc',
        ),
        (_) => developer.log(
          'Quiz result saved successfully: ${quizResult.id}',
          name: 'AITutorBloc',
        ),
      );

      // Use the current quiz if available, otherwise create a completed quiz
      final quiz =
          currentQuiz ??
          Quiz(
            id: event.quizId,
            title: 'Completed Quiz',
            subject: 'General',
            topic: 'Learning',
            questions: [], // Questions not needed for completed state
            difficulty: DifficultyLevel.medium,
            createdAt: DateTime.now(),
            timeLimit: 30,
            isAdaptive: true,
            metadata: {},
          );

      emit(QuizCompleted(quiz: quiz, result: quizResult));
    } catch (e) {
      emit(AITutorError('Failed to complete quiz: ${e.toString()}'));
    }
  }

  Future<void> _onSearchFlashcards(
    SearchFlashcardsEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Searching flashcards...'));

    try {
      final result = await _flashcardRepository.searchFlashcards(
        query: event.query,
        subject: event.subject,
        topic: event.topic,
        difficulty: event.difficulty,
      );

      result.fold(
        (failure) => emit(
          AITutorError('Failed to search flashcards: ${failure.message}'),
        ),
        (flashcards) => emit(
          SearchResultsLoaded(
            flashcards: flashcards,
            quizzes: null,
            query: event.query,
          ),
        ),
      );
    } catch (e) {
      emit(AITutorError('Failed to search flashcards: ${e.toString()}'));
    }
  }

  Future<void> _onSearchQuizzes(
    SearchQuizzesEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Searching quizzes...'));

    try {
      // Search quizzes using repository
      final result = await _repository.searchQuizzes(
        query: event.query,
        subject: event.subject,
        topic: event.topic,
        difficulty: event.difficulty,
      );

      result.fold(
        (failure) =>
            emit(AITutorError('Failed to search quizzes: ${failure.message}')),
        (quizzes) => emit(
          SearchResultsLoaded(
            flashcards: null,
            quizzes: quizzes,
            query: event.query,
          ),
        ),
      );
    } catch (e) {
      emit(AITutorError('Failed to search quizzes: ${e.toString()}'));
    }
  }

  /// Gets enhanced error message with user-friendly information
  String _getEnhancedErrorMessage(dynamic error) {
    if (error.toString().contains('network')) {
      return 'Network connection failed. Please check your internet connection and try again.';
    } else if (error.toString().contains('auth')) {
      return 'Authentication required. Please sign in to continue using AI Tutor features.';
    } else if (error.toString().contains('permission')) {
      return 'Permission denied. Please check your account permissions.';
    } else if (error.toString().contains('quota') ||
        error.toString().contains('limit')) {
      return 'Usage limit reached. Please try again later or upgrade your plan.';
    } else if (error.toString().contains('timeout')) {
      return 'Request timed out. Please check your connection and try again.';
    } else {
      return 'An error occurred. Please try again or contact support if the problem persists.';
    }
  }

  /// Logs error with context for debugging
  void _logErrorWithContext(String operation, dynamic error) {
    developer.log(
      'AI Tutor Error in $operation: $error',
      name: 'AITutorBloc',
      error: error,
    );
  }
}
