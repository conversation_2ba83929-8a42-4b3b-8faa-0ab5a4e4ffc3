part of 'ai_tutor_bloc.dart';

/// Base class for all AI Tutor events
abstract class AITutorEvent extends Equatable {
  const AITutorEvent();

  @override
  List<Object?> get props => [];
}

/// Event to generate a learning plan
class GenerateLearningPlanEvent extends AITutorEvent {
  final String subject;
  final String currentLevel;
  final List<String> learningGoals;
  final Map<String, dynamic> preferences;

  const GenerateLearningPlanEvent({
    required this.subject,
    required this.currentLevel,
    required this.learningGoals,
    required this.preferences,
  });

  @override
  List<Object> get props => [subject, currentLevel, learningGoals, preferences];

  @override
  String toString() {
    return 'GenerateLearningPlanEvent(subject: $subject, level: $currentLevel, goals: $learningGoals)';
  }
}

/// Event to create flashcards for a topic
class CreateFlashcardsEvent extends AITutorEvent {
  final String topic;
  final int count;
  final DifficultyLevel difficulty;
  final String? context;

  const CreateFlashcardsEvent({
    required this.topic,
    required this.count,
    required this.difficulty,
    this.context,
  });

  @override
  List<Object?> get props => [topic, count, difficulty, context];

  @override
  String toString() {
    return 'CreateFlashcardsEvent(topic: $topic, count: $count, difficulty: $difficulty)';
  }
}

/// Event to generate an adaptive quiz
class GenerateQuizEvent extends AITutorEvent {
  final String topic;
  final List<String> concepts;
  final DifficultyLevel difficulty;
  final List<QuizResult>? previousResults;

  const GenerateQuizEvent({
    required this.topic,
    required this.concepts,
    required this.difficulty,
    this.previousResults,
  });

  @override
  List<Object?> get props => [topic, concepts, difficulty, previousResults];

  @override
  String toString() {
    return 'GenerateQuizEvent(topic: $topic, concepts: $concepts, difficulty: $difficulty)';
  }
}

/// Event to start a learning session
class StartLearningSessionEvent extends AITutorEvent {
  final String subject;
  final String topic;
  final List<String> conceptsCovered;

  const StartLearningSessionEvent({
    required this.subject,
    required this.topic,
    required this.conceptsCovered,
  });

  @override
  List<Object> get props => [subject, topic, conceptsCovered];

  @override
  String toString() {
    return 'StartLearningSessionEvent(subject: $subject, topic: $topic)';
  }
}

/// Event to end a learning session
class EndLearningSessionEvent extends AITutorEvent {
  final String sessionId;
  final double comprehensionScore;
  final Map<String, dynamic> metadata;

  const EndLearningSessionEvent({
    required this.sessionId,
    required this.comprehensionScore,
    required this.metadata,
  });

  @override
  List<Object> get props => [sessionId, comprehensionScore, metadata];

  @override
  String toString() {
    return 'EndLearningSessionEvent(sessionId: $sessionId, score: $comprehensionScore)';
  }
}

/// Event to load learning progress
class LoadLearningProgressEvent extends AITutorEvent {
  final String userId;
  final String? subject;
  final String? topic;

  const LoadLearningProgressEvent({
    required this.userId,
    this.subject,
    this.topic,
  });

  @override
  List<Object?> get props => [userId, subject, topic];

  @override
  String toString() {
    return 'LoadLearningProgressEvent(userId: $userId, subject: $subject, topic: $topic)';
  }
}

/// Event to update learning progress
class UpdateLearningProgressEvent extends AITutorEvent {
  final LearningProgress progress;

  const UpdateLearningProgressEvent({required this.progress});

  @override
  List<Object> get props => [progress];

  @override
  String toString() {
    return 'UpdateLearningProgressEvent(progress: $progress)';
  }
}

/// Event to load user's learning plans
class LoadLearningPlansEvent extends AITutorEvent {
  final String userId;

  const LoadLearningPlansEvent({required this.userId});

  @override
  List<Object> get props => [userId];

  @override
  String toString() {
    return 'LoadLearningPlansEvent(userId: $userId)';
  }
}

/// Event to save a learning plan
class SaveLearningPlanEvent extends AITutorEvent {
  final LearningPlan plan;

  const SaveLearningPlanEvent({required this.plan});

  @override
  List<Object> get props => [plan];

  @override
  String toString() {
    return 'SaveLearningPlanEvent(plan: $plan)';
  }
}

/// Event to explain a concept
class ExplainConceptEvent extends AITutorEvent {
  final String concept;
  final String context;
  final ExplanationStyle style;

  const ExplainConceptEvent({
    required this.concept,
    required this.context,
    required this.style,
  });

  @override
  List<Object> get props => [concept, context, style];

  @override
  String toString() {
    return 'ExplainConceptEvent(concept: $concept, style: $style)';
  }
}

/// Event to identify knowledge gaps
class IdentifyKnowledgeGapsEvent extends AITutorEvent {
  final List<QuizResult> quizResults;
  final String subject;

  const IdentifyKnowledgeGapsEvent({
    required this.quizResults,
    required this.subject,
  });

  @override
  List<Object> get props => [quizResults, subject];

  @override
  String toString() {
    return 'IdentifyKnowledgeGapsEvent(subject: $subject, results: ${quizResults.length})';
  }
}

/// Event to load study recommendations
class LoadStudyRecommendationsEvent extends AITutorEvent {
  final String userId;
  final String subject;

  const LoadStudyRecommendationsEvent({
    required this.userId,
    required this.subject,
  });

  @override
  List<Object> get props => [userId, subject];

  @override
  String toString() {
    return 'LoadStudyRecommendationsEvent(userId: $userId, subject: $subject)';
  }
}

/// Event to reset the AI Tutor state
class ResetAITutorEvent extends AITutorEvent {
  const ResetAITutorEvent();

  @override
  String toString() {
    return 'ResetAITutorEvent()';
  }
}

/// Event to load flashcards for review
class LoadFlashcardsEvent extends AITutorEvent {
  final String? subject;
  final String? topic;
  final bool dueOnly;

  const LoadFlashcardsEvent({this.subject, this.topic, this.dueOnly = false});

  @override
  List<Object?> get props => [subject, topic, dueOnly];

  @override
  String toString() {
    return 'LoadFlashcardsEvent(subject: $subject, topic: $topic, dueOnly: $dueOnly)';
  }
}

/// Event to review a flashcard
class ReviewFlashcardEvent extends AITutorEvent {
  final String flashcardId;
  final FlashcardResponse response;

  const ReviewFlashcardEvent({
    required this.flashcardId,
    required this.response,
  });

  @override
  List<Object> get props => [flashcardId, response];

  @override
  String toString() {
    return 'ReviewFlashcardEvent(flashcardId: $flashcardId, response: $response)';
  }
}

/// Event to start a quiz
class StartQuizEvent extends AITutorEvent {
  final String quizId;

  const StartQuizEvent({required this.quizId});

  @override
  List<Object> get props => [quizId];

  @override
  String toString() {
    return 'StartQuizEvent(quizId: $quizId)';
  }
}

/// Event to submit a quiz answer
class SubmitQuizAnswerEvent extends AITutorEvent {
  final String questionId;
  final List<String> answers;

  const SubmitQuizAnswerEvent({
    required this.questionId,
    required this.answers,
  });

  @override
  List<Object> get props => [questionId, answers];

  @override
  String toString() {
    return 'SubmitQuizAnswerEvent(questionId: $questionId, answers: $answers)';
  }
}

/// Event to complete a quiz
class CompleteQuizEvent extends AITutorEvent {
  final String quizId;
  final List<QuizAnswer> answers;

  const CompleteQuizEvent({required this.quizId, required this.answers});

  @override
  List<Object> get props => [quizId, answers];

  @override
  String toString() {
    return 'CompleteQuizEvent(quizId: $quizId, answers: ${answers.length})';
  }
}

/// Event to search flashcards
class SearchFlashcardsEvent extends AITutorEvent {
  final String query;
  final String? subject;
  final String? topic;
  final DifficultyLevel? difficulty;

  const SearchFlashcardsEvent({
    required this.query,
    this.subject,
    this.topic,
    this.difficulty,
  });

  @override
  List<Object?> get props => [query, subject, topic, difficulty];

  @override
  String toString() {
    return 'SearchFlashcardsEvent(query: $query, subject: $subject, topic: $topic)';
  }
}

/// Event to search quizzes
class SearchQuizzesEvent extends AITutorEvent {
  final String query;
  final String? subject;
  final String? topic;
  final DifficultyLevel? difficulty;

  const SearchQuizzesEvent({
    required this.query,
    this.subject,
    this.topic,
    this.difficulty,
  });

  @override
  List<Object?> get props => [query, subject, topic, difficulty];

  @override
  String toString() {
    return 'SearchQuizzesEvent(query: $query, subject: $subject, topic: $topic)';
  }
}
