import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:io';
import 'dart:async';
import '../../../../core/error/failures.dart';
import 'ui_helpers.dart';

/// Comprehensive error handler for AI Tutor features
/// Provides user-friendly error messages and recovery options
class AITutorErrorHandler {
  /// Handles errors and shows appropriate user feedback
  static void handleError(
    BuildContext context,
    dynamic error, {
    String? customMessage,
    VoidCallback? onRetry,
    bool showSnackBar = true,
  }) {
    final errorInfo = _categorizeError(error);
    final message = customMessage ?? errorInfo.userMessage;
    
    if (showSnackBar) {
      _showErrorSnackBar(context, message, onRetry);
    }
    
    // Log error for debugging
    _logError(error, errorInfo);
  }

  /// Shows error dialog with detailed information and recovery options
  static void showErrorDialog(
    BuildContext context,
    dynamic error, {
    String? title,
    String? customMessage,
    VoidCallback? onRetry,
    VoidCallback? onContactSupport,
  }) {
    final errorInfo = _categorizeError(error);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(width: 8),
            Text(title ?? 'Error'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              customMessage ?? errorInfo.userMessage,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            if (errorInfo.showTechnicalDetails) ...[
              const SizedBox(height: 16),
              ExpansionTile(
                title: const Text('Technical Details'),
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Error Type: ${errorInfo.type}\n'
                      'Details: ${errorInfo.technicalMessage}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                ],
              ),
            ],
            if (errorInfo.suggestions.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'Suggestions:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              ...errorInfo.suggestions.map((suggestion) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• '),
                    Expanded(child: Text(suggestion)),
                  ],
                ),
              )),
            ],
          ],
        ),
        actions: [
          if (onContactSupport != null)
            TextButton.icon(
              onPressed: onContactSupport,
              icon: const Icon(Icons.support_agent),
              label: const Text('Contact Support'),
            ),
          if (onRetry != null)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Shows network error with specific recovery options
  static void showNetworkError(
    BuildContext context, {
    VoidCallback? onRetry,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.wifi_off,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(width: 8),
            const Text('Connection Error'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Unable to connect to the server. Please check your internet connection and try again.',
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue.shade700),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'AI Tutor features require an internet connection to work properly.',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          if (onRetry != null)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
        ],
      ),
    );
  }

  /// Shows authentication error with sign-in option
  static void showAuthError(
    BuildContext context, {
    VoidCallback? onSignIn,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.account_circle_outlined,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(width: 8),
            const Text('Authentication Required'),
          ],
        ),
        content: const Text(
          'You need to be signed in to use AI Tutor features. Please sign in to continue.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          if (onSignIn != null)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                onSignIn();
              },
              icon: const Icon(Icons.login),
              label: const Text('Sign In'),
            ),
        ],
      ),
    );
  }

  /// Shows API limit exceeded error with upgrade options
  static void showApiLimitError(
    BuildContext context, {
    VoidCallback? onUpgrade,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.speed,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(width: 8),
            const Text('Usage Limit Reached'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'You have reached your daily AI usage limit. You can still use basic features or upgrade for unlimited access.',
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.star, color: Colors.amber.shade700),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Upgrade to Premium for unlimited AI features and priority support.',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue with Basic'),
          ),
          if (onUpgrade != null)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                onUpgrade();
              },
              icon: const Icon(Icons.upgrade),
              label: const Text('Upgrade'),
            ),
        ],
      ),
    );
  }

  /// Categorizes error and provides user-friendly information
  static ErrorInfo _categorizeError(dynamic error) {
    if (error is FirebaseAuthException) {
      return _handleFirebaseAuthError(error);
    } else if (error is FirebaseException) {
      return _handleFirebaseError(error);
    } else if (error is SocketException) {
      return ErrorInfo(
        type: 'Network Error',
        userMessage: 'Unable to connect to the server. Please check your internet connection.',
        technicalMessage: error.toString(),
        suggestions: [
          'Check your internet connection',
          'Try again in a few moments',
          'Switch to a different network if available',
        ],
      );
    } else if (error is TimeoutException) {
      return ErrorInfo(
        type: 'Timeout Error',
        userMessage: 'The request took too long to complete. Please try again.',
        technicalMessage: error.toString(),
        suggestions: [
          'Check your internet connection speed',
          'Try again with a better connection',
          'Contact support if the problem persists',
        ],
      );
    } else if (error is Failure) {
      return _handleFailureError(error);
    } else {
      return ErrorInfo(
        type: 'Unknown Error',
        userMessage: 'An unexpected error occurred. Please try again.',
        technicalMessage: error.toString(),
        suggestions: [
          'Try the action again',
          'Restart the app if the problem persists',
          'Contact support for assistance',
        ],
      );
    }
  }

  static ErrorInfo _handleFirebaseAuthError(FirebaseAuthException error) {
    switch (error.code) {
      case 'user-not-found':
        return ErrorInfo(
          type: 'Authentication Error',
          userMessage: 'No account found. Please sign up or check your credentials.',
          technicalMessage: error.toString(),
          suggestions: ['Create a new account', 'Check your email address'],
        );
      case 'wrong-password':
        return ErrorInfo(
          type: 'Authentication Error',
          userMessage: 'Incorrect password. Please try again.',
          technicalMessage: error.toString(),
          suggestions: ['Check your password', 'Use password reset if needed'],
        );
      case 'network-request-failed':
        return ErrorInfo(
          type: 'Network Error',
          userMessage: 'Network error during authentication. Please check your connection.',
          technicalMessage: error.toString(),
          suggestions: ['Check internet connection', 'Try again'],
        );
      default:
        return ErrorInfo(
          type: 'Authentication Error',
          userMessage: 'Authentication failed. Please try signing in again.',
          technicalMessage: error.toString(),
          suggestions: ['Try signing in again', 'Contact support if needed'],
        );
    }
  }

  static ErrorInfo _handleFirebaseError(FirebaseException error) {
    switch (error.code) {
      case 'permission-denied':
        return ErrorInfo(
          type: 'Permission Error',
          userMessage: 'You don\'t have permission to access this data.',
          technicalMessage: error.toString(),
          suggestions: ['Sign in to your account', 'Contact support for access'],
        );
      case 'not-found':
        return ErrorInfo(
          type: 'Data Not Found',
          userMessage: 'The requested data was not found.',
          technicalMessage: error.toString(),
          suggestions: ['Check if the data exists', 'Try refreshing'],
        );
      case 'quota-exceeded':
        return ErrorInfo(
          type: 'Quota Exceeded',
          userMessage: 'Service usage limit exceeded. Please try again later.',
          technicalMessage: error.toString(),
          suggestions: ['Wait and try again later', 'Contact support for limits'],
        );
      default:
        return ErrorInfo(
          type: 'Server Error',
          userMessage: 'A server error occurred. Please try again.',
          technicalMessage: error.toString(),
          suggestions: ['Try again', 'Contact support if persistent'],
        );
    }
  }

  static ErrorInfo _handleFailureError(Failure failure) {
    if (failure is NetworkFailure) {
      return ErrorInfo(
        type: 'Network Error',
        userMessage: 'Network connection failed. Please check your internet.',
        technicalMessage: failure.message,
        suggestions: ['Check internet connection', 'Try again'],
      );
    } else if (failure is AuthFailure) {
      return ErrorInfo(
        type: 'Authentication Error',
        userMessage: 'Authentication required. Please sign in.',
        technicalMessage: failure.message,
        suggestions: ['Sign in to your account'],
      );
    } else if (failure is ServerFailure) {
      return ErrorInfo(
        type: 'Server Error',
        userMessage: 'Server error occurred. Please try again.',
        technicalMessage: failure.message,
        suggestions: ['Try again', 'Contact support if persistent'],
      );
    } else {
      return ErrorInfo(
        type: 'Error',
        userMessage: 'An error occurred. Please try again.',
        technicalMessage: failure.message,
        suggestions: ['Try again'],
      );
    }
  }

  static void _showErrorSnackBar(
    BuildContext context,
    String message,
    VoidCallback? onRetry,
  ) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.onError,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
        action: onRetry != null
            ? SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }

  static void _logError(dynamic error, ErrorInfo errorInfo) {
    // Log error for debugging and analytics
    print('AI Tutor Error: ${errorInfo.type}');
    print('User Message: ${errorInfo.userMessage}');
    print('Technical: ${errorInfo.technicalMessage}');
    print('Original Error: $error');
  }
}

/// Contains categorized error information
class ErrorInfo {
  final String type;
  final String userMessage;
  final String technicalMessage;
  final List<String> suggestions;
  final bool showTechnicalDetails;

  ErrorInfo({
    required this.type,
    required this.userMessage,
    required this.technicalMessage,
    this.suggestions = const [],
    this.showTechnicalDetails = false,
  });
}
