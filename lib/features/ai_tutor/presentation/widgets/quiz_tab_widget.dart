import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../presentation/bloc/ai_tutor_bloc.dart';
import '../../domain/entities/quiz.dart';
import '../pages/quiz_taking_page.dart';

/// Widget for the quiz tab
/// Extracted from the merged AI tutor page for better code organization
class QuizTabWidget extends StatefulWidget {
  final VoidCallback onCreateQuiz;
  final Map<String, dynamic> stats;

  const QuizTabWidget({
    super.key,
    required this.onCreateQuiz,
    required this.stats,
  });

  @override
  State<QuizTabWidget> createState() => _QuizTabWidgetState();
}

class _QuizTabWidgetState extends State<QuizTabWidget> {
  int _currentPage = 0;
  static const int _pageSize = 10;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Quizzes',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              ElevatedButton.icon(
                onPressed: widget.onCreateQuiz,
                icon: const Icon(Icons.add),
                label: const Text('Create Quiz'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quiz stats
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Completed',
                  '${widget.stats['completedQuizzes']}',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Average Score',
                  '${widget.stats['averageScore']}%',
                  Icons.trending_up,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Streak',
                  '${widget.stats['currentStreak']}',
                  Icons.local_fire_department,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Quiz list
          Expanded(child: _buildQuizList(context)),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuizList(BuildContext context) {
    // Add search/filter and pagination for quizzes
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: TextField(
            decoration: const InputDecoration(
              labelText: 'Search quizzes',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (query) {
              if (query.isNotEmpty) {
                context.read<AITutorBloc>().add(
                  SearchQuizzesEvent(query: query),
                );
              } else {
                // Load all quizzes when search is cleared
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Showing all quizzes')),
                );
              }
            },
          ),
        ),
        Expanded(
          child: BlocBuilder<AITutorBloc, AITutorState>(
            builder: (context, state) {
              if (state is AITutorLoading) {
                return const Center(child: CircularProgressIndicator());
              } else if (state is AITutorError) {
                return Center(child: Text('Error: ${state.message}'));
              } else if (state is QuizGenerated) {
                final quiz = state.quiz;
                if (quiz.questions.isEmpty) {
                  return Center(
                    child: Text(
                      'No quizzes found.\nCreate your first quiz to get started!',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  );
                }
                // Pagination
                int pageCount = (quiz.questions.length / _pageSize).ceil();
                final pageItems = quiz.questions
                    .skip(_currentPage * _pageSize)
                    .take(_pageSize)
                    .toList();
                return Column(
                  children: [
                    Expanded(
                      child: ListView.separated(
                        itemCount: pageItems.length,
                        separatorBuilder: (_, __) => const Divider(height: 1),
                        itemBuilder: (context, index) {
                          final question = pageItems[index];
                          return ListTile(
                            leading: const Icon(
                              Icons.quiz,
                              color: Colors.green,
                            ),
                            title: Text('Q${index + 1}: ${question.question}'),
                            subtitle: Text(
                              'Type: ${question.type.displayName}',
                            ),
                            trailing: IconButton(
                              icon: const Icon(Icons.arrow_forward_ios),
                              tooltip: 'Take Quiz',
                              onPressed: () {
                                // Get the bloc instance before navigation
                                final aiTutorBloc = context.read<AITutorBloc>();

                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => BlocProvider.value(
                                      value: aiTutorBloc,
                                      child: QuizTakingPage(
                                        quizId: quiz.id,
                                        quiz: quiz,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          );
                        },
                      ),
                    ),
                    if (pageCount > 1)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text('Page ${_currentPage + 1} of $pageCount'),
                          const SizedBox(width: 16),
                          if (_currentPage > 0)
                            IconButton(
                              icon: const Icon(Icons.arrow_back),
                              onPressed: () {
                                setState(() {
                                  _currentPage--;
                                });
                              },
                            ),
                          if (_currentPage < pageCount - 1)
                            IconButton(
                              icon: const Icon(Icons.arrow_forward),
                              onPressed: () {
                                setState(() {
                                  _currentPage++;
                                });
                              },
                            ),
                        ],
                      ),
                  ],
                );
              }
              return const Center(child: Text('No quiz data available.'));
            },
          ),
        ),
      ],
    );
  }

  /// Shows tips for effective quiz-based learning
  void _showQuizTips(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🧠 Quiz Learning Tips'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('• Take quizzes regularly to reinforce learning'),
              SizedBox(height: 8),
              Text('• Review incorrect answers to understand mistakes'),
              SizedBox(height: 8),
              Text('• Use adaptive difficulty to challenge yourself'),
              SizedBox(height: 8),
              Text('• Focus on understanding, not just memorization'),
              SizedBox(height: 8),
              Text('• Track your progress over time'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it!'),
          ),
        ],
      ),
    );
  }
}

/// Dialog for creating quizzes
class CreateQuizDialog extends StatefulWidget {
  final Function(String topic, int questionCount, String difficulty)
  onCreateQuiz;

  const CreateQuizDialog({super.key, required this.onCreateQuiz});

  @override
  State<CreateQuizDialog> createState() => _CreateQuizDialogState();
}

class _CreateQuizDialogState extends State<CreateQuizDialog> {
  final TextEditingController _topicController = TextEditingController();
  final TextEditingController _questionCountController = TextEditingController(
    text: '5',
  );
  String _selectedDifficulty = 'medium';

  @override
  void dispose() {
    _topicController.dispose();
    _questionCountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create Quiz'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _topicController,
            decoration: const InputDecoration(
              labelText: 'Topic',
              hintText: 'e.g., World History',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _questionCountController,
            decoration: const InputDecoration(
              labelText: 'Number of questions',
              hintText: '5',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedDifficulty,
            decoration: const InputDecoration(
              labelText: 'Difficulty',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 'easy', child: Text('Easy')),
              DropdownMenuItem(value: 'medium', child: Text('Medium')),
              DropdownMenuItem(value: 'hard', child: Text('Hard')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedDifficulty = value ?? 'medium';
              });
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final topic = _topicController.text.trim();
            final questionCount =
                int.tryParse(_questionCountController.text) ?? 5;

            if (topic.isNotEmpty) {
              widget.onCreateQuiz(topic, questionCount, _selectedDifficulty);
              Navigator.pop(context);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Please enter a topic')),
              );
            }
          },
          child: const Text('Create'),
        ),
      ],
    );
  }
}
