import 'package:flutter/material.dart';

/// Dialog for creating a custom learning plan subject, level, and goals.
class CustomSubjectDialog extends StatefulWidget {
  final void Function(String subject, String level, List<String> goals)
  onCreate;

  const CustomSubjectDialog({super.key, required this.onCreate});

  @override
  State<CustomSubjectDialog> createState() => _CustomSubjectDialogState();
}

class _CustomSubjectDialogState extends State<CustomSubjectDialog> {
  final TextEditingController subjectController = TextEditingController();
  final TextEditingController levelController = TextEditingController();
  final TextEditingController goalsController = TextEditingController();

  @override
  void dispose() {
    subjectController.dispose();
    levelController.dispose();
    goalsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Custom Learning Plan'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: subjectController,
              decoration: const InputDecoration(
                labelText: 'Subject',
                hintText: 'e.g., Philosophy',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: levelController,
              decoration: const InputDecoration(
                labelText: 'Level',
                hintText: 'e.g., Beginner',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: goalsController,
              decoration: const InputDecoration(
                labelText: 'Learning Goals',
                hintText: 'Comma separated, e.g., Logic, Ethics',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final subject = subjectController.text.trim();
            final level = levelController.text.trim();
            final goals = goalsController.text
                .split(',')
                .map((g) => g.trim())
                .where((g) => g.isNotEmpty)
                .toList();
            if (subject.isEmpty || level.isEmpty || goals.isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Please fill all fields.')),
              );
              return;
            }
            Navigator.of(context).pop();
            widget.onCreate(subject, level, goals);
            // Enhanced validation and UI for custom plan dialog
            _validateAndCreatePlan(context, subject, level, goals);
          },
          child: const Text('Create'),
        ),
      ],
    );
  }

  /// Enhanced validation and plan creation with better user feedback
  void _validateAndCreatePlan(
    BuildContext context,
    String subject,
    String level,
    List<String> goals,
  ) {
    // Enhanced validation
    final errors = <String>[];

    if (subject.length < 2) {
      errors.add('Subject must be at least 2 characters long');
    }

    if (level.isEmpty) {
      errors.add('Please specify your current level');
    }

    if (goals.isEmpty) {
      errors.add('Please add at least one learning goal');
    } else if (goals.length > 10) {
      errors.add('Please limit to 10 learning goals maximum');
    }

    // Check for duplicate goals
    final uniqueGoals = goals.toSet();
    if (uniqueGoals.length != goals.length) {
      errors.add('Please remove duplicate goals');
    }

    if (errors.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errors.join('\n')),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
      return;
    }

    // Show success feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Creating personalized plan for $subject...'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
