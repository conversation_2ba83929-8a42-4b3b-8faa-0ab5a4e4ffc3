import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../presentation/bloc/ai_tutor_bloc.dart';
import '../../domain/entities/flashcard.dart';
import '../pages/flashcard_review_page.dart';

/// Enhanced flashcards tab widget with real pagination and search
class EnhancedFlashcardsTabWidget extends StatefulWidget {
  final VoidCallback onCreateFlashcards;

  const EnhancedFlashcardsTabWidget({
    super.key,
    required this.onCreateFlashcards,
  });

  @override
  State<EnhancedFlashcardsTabWidget> createState() =>
      _EnhancedFlashcardsTabWidgetState();
}

class _EnhancedFlashcardsTabWidgetState
    extends State<EnhancedFlashcardsTabWidget> {
  int _currentPage = 0;
  final int _pageSize = 10;
  String _searchQuery = '';
  List<Flashcard> _allFlashcards = [];
  List<Flashcard> _filteredFlashcards = [];

  @override
  void initState() {
    super.initState();
    _loadFlashcards();
  }

  void _loadFlashcards() {
    context.read<AITutorBloc>().add(const LoadFlashcardsEvent());
  }

  void _filterFlashcards() {
    setState(() {
      if (_searchQuery.isEmpty) {
        _filteredFlashcards = _allFlashcards;
      } else {
        _filteredFlashcards = _allFlashcards.where((flashcard) {
          return flashcard.front.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              flashcard.back.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              flashcard.topic.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              flashcard.subject.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              );
        }).toList();
      }
      _currentPage = 0; // Reset to first page when filtering
    });
  }

  List<Flashcard> _getCurrentPageItems() {
    final startIndex = _currentPage * _pageSize;
    final endIndex = (startIndex + _pageSize).clamp(
      0,
      _filteredFlashcards.length,
    );
    return _filteredFlashcards.sublist(startIndex, endIndex);
  }

  int get _totalPages => (_filteredFlashcards.length / _pageSize).ceil();

  void _goToPage(int page) {
    setState(() {
      _currentPage = page.clamp(0, _totalPages - 1);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Flashcards',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      // Get the bloc instance before navigation
                      final aiTutorBloc = context.read<AITutorBloc>();

                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => BlocProvider.value(
                            value: aiTutorBloc,
                            child: const FlashcardReviewPage(
                              dueOnly: true, // Review only due cards
                            ),
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.schedule),
                    label: const Text('Review Due'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: widget.onCreateFlashcards,
                    icon: const Icon(Icons.add),
                    label: const Text('Create Set'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quick stats
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Total Cards',
                  '${_allFlashcards.length}',
                  Icons.quiz,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Due Today',
                  '${_allFlashcards.where((card) => card.isDue).length}',
                  Icons.schedule,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Mastered',
                  '${_allFlashcards.where((card) => card.easeFactor > 2.5 && card.reviewCount > 5).length}',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Search bar
          TextField(
            decoration: const InputDecoration(
              labelText: 'Search flashcards',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (query) {
              setState(() {
                _searchQuery = query;
              });
              _filterFlashcards();
            },
          ),
          const SizedBox(height: 16),

          // Flashcard list
          Expanded(
            child: BlocListener<AITutorBloc, AITutorState>(
              listener: (context, state) {
                if (state is FlashcardsLoaded) {
                  setState(() {
                    _allFlashcards = state.flashcards;
                    _filterFlashcards();
                  });
                } else if (state is FlashcardsCreated) {
                  setState(() {
                    _allFlashcards = state.flashcards;
                    _filterFlashcards();
                  });
                }
              },
              child: BlocBuilder<AITutorBloc, AITutorState>(
                builder: (context, state) {
                  if (state is AITutorLoading) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (state is AITutorError) {
                    return Center(child: Text('Error: ${state.message}'));
                  }

                  final currentPageItems = _getCurrentPageItems();
                  if (currentPageItems.isEmpty) {
                    return Center(
                      child: Text(
                        _searchQuery.isNotEmpty
                            ? 'No flashcards found matching "$_searchQuery"'
                            : 'No flashcard sets found.\nCreate your first set to get started!',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    );
                  }

                  return Column(
                    children: [
                      Expanded(
                        child: ListView.separated(
                          itemCount: currentPageItems.length,
                          separatorBuilder: (_, __) => const Divider(height: 1),
                          itemBuilder: (context, index) {
                            final flashcard = currentPageItems[index];
                            return ListTile(
                              leading: const Icon(
                                Icons.style,
                                color: Colors.blue,
                              ),
                              title: Text(flashcard.topic),
                              subtitle: Text(
                                '${flashcard.subject} • ${flashcard.difficulty.displayName}',
                              ),
                              trailing: IconButton(
                                icon: const Icon(Icons.arrow_forward_ios),
                                tooltip: 'Review Flashcards',
                                onPressed: () {
                                  // Get the bloc instance before navigation
                                  final aiTutorBloc = context
                                      .read<AITutorBloc>();

                                  Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder: (context) => BlocProvider.value(
                                        value: aiTutorBloc,
                                        child: FlashcardReviewPage(
                                          subject: flashcard.subject,
                                          topic: flashcard.topic,
                                          dueOnly:
                                              false, // Review all cards in this set
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        ),
                      ),

                      // Pagination controls
                      if (_totalPages > 1)
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              IconButton(
                                onPressed: _currentPage > 0
                                    ? () => _goToPage(_currentPage - 1)
                                    : null,
                                icon: const Icon(Icons.chevron_left),
                              ),
                              Text('Page ${_currentPage + 1} of $_totalPages'),
                              IconButton(
                                onPressed: _currentPage < _totalPages - 1
                                    ? () => _goToPage(_currentPage + 1)
                                    : null,
                                icon: const Icon(Icons.chevron_right),
                              ),
                            ],
                          ),
                        ),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
