import 'package:flutter/material.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import '../../domain/entities/flashcard.dart';

/// Enhanced dialog for creating quizzes with AI integration
class CreateQuizDialog extends StatefulWidget {
  final Function(
    String topic,
    int questionCount,
    DifficultyLevel difficulty,
    String? context,
  )
  onCreateQuiz;

  const CreateQuizDialog({super.key, required this.onCreateQuiz});

  @override
  State<CreateQuizDialog> createState() => _CreateQuizDialogState();
}

class _CreateQuizDialogState extends State<CreateQuizDialog> {
  final TextEditingController _topicController = TextEditingController();
  final TextEditingController _questionCountController = TextEditingController(
    text: '5',
  );
  final TextEditingController _contextController = TextEditingController();

  DifficultyLevel _selectedDifficulty = DifficultyLevel.medium;
  bool _includeContext = false;
  String _quizType = 'adaptive'; // adaptive, practice, assessment

  @override
  void dispose() {
    _topicController.dispose();
    _questionCountController.dispose();
    _contextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.quiz, color: AppTheme.primaryGreen),
          SizedBox(width: 8),
          Text('Create Quiz'),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Topic input
            TextField(
              controller: _topicController,
              decoration: const InputDecoration(
                labelText: 'Topic *',
                hintText: 'e.g., World War II, Algebra, Biology',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.topic),
              ),
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),

            // Number of questions
            TextField(
              controller: _questionCountController,
              decoration: const InputDecoration(
                labelText: 'Number of questions',
                hintText: '5',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.numbers),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),

            // Quiz type selection
            Text(
              'Quiz Type',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Column(
              children: [
                RadioListTile<String>(
                  title: const Text('Adaptive Quiz'),
                  subtitle: const Text(
                    'Adjusts difficulty based on your performance',
                  ),
                  value: 'adaptive',
                  groupValue: _quizType,
                  onChanged: (value) {
                    setState(() {
                      _quizType = value!;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                RadioListTile<String>(
                  title: const Text('Practice Quiz'),
                  subtitle: const Text('Fixed difficulty for practice'),
                  value: 'practice',
                  groupValue: _quizType,
                  onChanged: (value) {
                    setState(() {
                      _quizType = value!;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                RadioListTile<String>(
                  title: const Text('Assessment'),
                  subtitle: const Text('Comprehensive evaluation'),
                  value: 'assessment',
                  groupValue: _quizType,
                  onChanged: (value) {
                    setState(() {
                      _quizType = value!;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Difficulty selection (only for practice and assessment)
            if (_quizType != 'adaptive') ...[
              Text(
                'Difficulty Level',
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: DifficultyLevel.values.map((difficulty) {
                  return FilterChip(
                    label: Text(difficulty.displayName),
                    selected: _selectedDifficulty == difficulty,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _selectedDifficulty = difficulty;
                        });
                      }
                    },
                    selectedColor: Color(
                      difficulty.colorValue,
                    ).withOpacity(0.2),
                    checkmarkColor: Color(difficulty.colorValue),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
            ],

            // Context toggle
            CheckboxListTile(
              title: const Text('Add learning context'),
              subtitle: const Text(
                'Provide additional context for better questions',
              ),
              value: _includeContext,
              onChanged: (value) {
                setState(() {
                  _includeContext = value ?? false;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
            ),

            // Context input (conditional)
            if (_includeContext) ...[
              const SizedBox(height: 8),
              TextField(
                controller: _contextController,
                decoration: const InputDecoration(
                  labelText: 'Learning Context',
                  hintText:
                      'e.g., Focus on key dates, Include practical applications',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.info_outline),
                ),
                maxLines: 3,
                textCapitalization: TextCapitalization.sentences,
              ),
            ],

            const SizedBox(height: 16),

            // Tips section
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.quiz_outlined,
                        color: Colors.orange[700],
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Quiz Tips:',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Colors.orange[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• Adaptive quizzes adjust to your level\n'
                    '• Practice quizzes help reinforce learning\n'
                    '• Assessments provide comprehensive evaluation\n'
                    '• Start with 5-10 questions for new topics',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.orange[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton.icon(
          onPressed: _createQuiz,
          icon: const Icon(Icons.auto_awesome),
          label: const Text('Generate Quiz'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryGreen,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  void _createQuiz() {
    final topic = _topicController.text.trim();
    final questionCount = int.tryParse(_questionCountController.text) ?? 5;
    final learningContext = _includeContext
        ? _contextController.text.trim()
        : null;

    if (topic.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a topic'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (questionCount < 1 || questionCount > 30) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Number of questions must be between 1 and 30'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // For adaptive quizzes, use medium difficulty as starting point
    final difficulty = _quizType == 'adaptive'
        ? DifficultyLevel.medium
        : _selectedDifficulty;

    widget.onCreateQuiz(topic, questionCount, difficulty, learningContext);
    Navigator.pop(context);
  }
}
