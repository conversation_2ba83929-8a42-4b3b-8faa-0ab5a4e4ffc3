import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../presentation/bloc/ai_tutor_bloc.dart';
import '../../domain/entities/flashcard.dart';
import '../pages/flashcard_review_page.dart';

/// Widget for the flashcards tab
/// Extracted from the merged AI tutor page for better code organization
class FlashcardsTabWidget extends StatefulWidget {
  final VoidCallback onCreateFlashcards;
  final Map<String, dynamic> stats;

  const FlashcardsTabWidget({
    super.key,
    required this.onCreateFlashcards,
    required this.stats,
  });

  @override
  State<FlashcardsTabWidget> createState() => _FlashcardsTabWidgetState();
}

class _FlashcardsTabWidgetState extends State<FlashcardsTabWidget> {
  int _currentPage = 0;
  final int _pageSize = 10;
  String _searchQuery = '';
  List<Flashcard> _allFlashcards = [];
  List<Flashcard> _filteredFlashcards = [];

  @override
  void initState() {
    super.initState();
    _loadFlashcards();
  }

  void _loadFlashcards() {
    context.read<AITutorBloc>().add(const LoadFlashcardsEvent());
  }

  void _filterFlashcards() {
    setState(() {
      if (_searchQuery.isEmpty) {
        _filteredFlashcards = _allFlashcards;
      } else {
        _filteredFlashcards = _allFlashcards.where((flashcard) {
          return flashcard.front.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              flashcard.back.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              flashcard.topic.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              flashcard.subject.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              );
        }).toList();
      }
      _currentPage = 0; // Reset to first page when filtering
    });
  }

  List<Flashcard> _getCurrentPageItems() {
    final startIndex = _currentPage * _pageSize;
    final endIndex = (startIndex + _pageSize).clamp(
      0,
      _filteredFlashcards.length,
    );
    return _filteredFlashcards.sublist(startIndex, endIndex);
  }

  int get _totalPages => (_filteredFlashcards.length / _pageSize).ceil();

  void _goToPage(int page) {
    setState(() {
      _currentPage = page.clamp(0, _totalPages - 1);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Flashcards',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      final aiTutorBloc = context.read<AITutorBloc>();
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => BlocProvider.value(
                            value: aiTutorBloc,
                            child: const FlashcardReviewPage(
                              dueOnly: true, // Review only due cards
                            ),
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.schedule),
                    label: const Text('Review Due'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: widget.onCreateFlashcards,
                    icon: const Icon(Icons.add),
                    label: const Text('Create Set'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quick stats
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Total Cards',
                  '${_allFlashcards.length}',
                  Icons.quiz,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Due Today',
                  '${_allFlashcards.where((card) => card.isDue).length}',
                  Icons.schedule,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Mastered',
                  '${_allFlashcards.where((card) => card.easeFactor > 2.5 && card.reviewCount > 5).length}',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Flashcard sets list
          Expanded(child: _buildFlashcardSetsList(context)),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFlashcardSetsList(BuildContext context) {
    // Add search/filter and pagination for flashcard sets
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: TextField(
            decoration: const InputDecoration(
              labelText: 'Search flashcard sets',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (query) {
              setState(() {
                _searchQuery = query;
              });
              _filterFlashcards();
            },
          ),
        ),
        Expanded(
          child: BlocBuilder<AITutorBloc, AITutorState>(
            builder: (context, state) {
              if (state is AITutorLoading) {
                return const Center(child: CircularProgressIndicator());
              } else if (state is AITutorError) {
                return Center(child: Text('Error: {state.message}'));
              } else if (state is FlashcardsCreated) {
                final sets = state.flashcards;
                if (sets.isEmpty) {
                  return Center(
                    child: Text(
                      'No flashcard sets found.\nCreate your first set to get started!',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  );
                }
                // Pagination with state management
                _allFlashcards = sets;
                _filterFlashcards();
                final pageItems = _getCurrentPageItems();
                final pageCount = _totalPages;
                return Column(
                  children: [
                    Expanded(
                      child: ListView.separated(
                        itemCount: pageItems.length,
                        separatorBuilder: (_, __) => const Divider(height: 1),
                        itemBuilder: (context, index) {
                          final set = pageItems[index];
                          return ListTile(
                            leading: const Icon(
                              Icons.style,
                              color: Colors.blue,
                            ),
                            title: Text(set.topic),
                            subtitle: Text('${sets.length} cards available'),
                            trailing: IconButton(
                              icon: const Icon(Icons.arrow_forward_ios),
                              tooltip: 'Review Flashcards',
                              onPressed: () {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => FlashcardReviewPage(
                                      subject: set.subject,
                                      topic: set.topic,
                                      dueOnly:
                                          false, // Review all cards in this set
                                    ),
                                  ),
                                );
                              },
                            ),
                          );
                        },
                      ),
                    ),
                    if (pageCount > 1)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text('Page ${_currentPage + 1} of $pageCount'),
                          const SizedBox(width: 16),
                          if (_currentPage > 0)
                            IconButton(
                              icon: const Icon(Icons.arrow_back),
                              onPressed: () {
                                _goToPage(_currentPage - 1);
                              },
                              tooltip: 'Previous page',
                            ),
                          if (_currentPage < pageCount - 1)
                            IconButton(
                              icon: const Icon(Icons.arrow_forward),
                              onPressed: () {
                                _goToPage(_currentPage + 1);
                              },
                              tooltip: 'Next page',
                            ),
                        ],
                      ),
                  ],
                );
              }
              return const Center(child: Text('No flashcard data available.'));
            },
          ),
        ),
      ],
    );
  }

  /// Shows tips for effective flashcard learning
  void _showFlashcardTips(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Flashcard Learning Tips'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('� Keep cards simple with one concept per card'),
              SizedBox(height: 8),
              Text('� Use active recall - test yourself regularly'),
              SizedBox(height: 8),
              Text('� Review difficult cards more frequently'),
              SizedBox(height: 8),
              Text('� Use images and mnemonics when possible'),
              SizedBox(height: 8),
              Text('� Practice consistently for best results'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it!'),
          ),
        ],
      ),
    );
  }
}

/// Dialog for creating flashcard sets
class CreateFlashcardsDialog extends StatefulWidget {
  final Function(String topic, int count) onCreateFlashcards;

  const CreateFlashcardsDialog({super.key, required this.onCreateFlashcards});

  @override
  State<CreateFlashcardsDialog> createState() => _CreateFlashcardsDialogState();
}

class _CreateFlashcardsDialogState extends State<CreateFlashcardsDialog> {
  final TextEditingController _topicController = TextEditingController();
  final TextEditingController _countController = TextEditingController(
    text: '10',
  );

  @override
  void dispose() {
    _topicController.dispose();
    _countController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create Flashcard Set'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _topicController,
            decoration: const InputDecoration(
              labelText: 'Topic',
              hintText: 'e.g., Spanish Vocabulary',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _countController,
            decoration: const InputDecoration(
              labelText: 'Number of cards',
              hintText: '10',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final topic = _topicController.text.trim();
            final count = int.tryParse(_countController.text) ?? 10;

            if (topic.isNotEmpty) {
              widget.onCreateFlashcards(topic, count);
              Navigator.pop(context);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Please enter a topic')),
              );
            }
          },
          child: const Text('Create'),
        ),
      ],
    );
  }
}
