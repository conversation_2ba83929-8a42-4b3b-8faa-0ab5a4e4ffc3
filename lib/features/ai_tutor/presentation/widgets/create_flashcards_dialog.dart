import 'package:flutter/material.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import '../../domain/entities/flashcard.dart';

/// Enhanced dialog for creating flashcard sets with AI integration
class CreateFlashcardsDialog extends StatefulWidget {
  final Function(
    String topic,
    int count,
    DifficultyLevel difficulty,
    String? context,
  )
  onCreateFlashcards;

  const CreateFlashcardsDialog({super.key, required this.onCreateFlashcards});

  @override
  State<CreateFlashcardsDialog> createState() => _CreateFlashcardsDialogState();
}

class _CreateFlashcardsDialogState extends State<CreateFlashcardsDialog> {
  final TextEditingController _topicController = TextEditingController();
  final TextEditingController _countController = TextEditingController(
    text: '10',
  );
  final TextEditingController _contextController = TextEditingController();

  DifficultyLevel _selectedDifficulty = DifficultyLevel.medium;
  bool _includeContext = false;

  @override
  void dispose() {
    _topicController.dispose();
    _countController.dispose();
    _contextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.style, color: AppTheme.primaryGreen),
          SizedBox(width: 8),
          Text('Create Flashcard Set'),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Topic input
            TextField(
              controller: _topicController,
              decoration: const InputDecoration(
                labelText: 'Topic *',
                hintText: 'e.g., Spanish Vocabulary, Calculus Derivatives',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.topic),
              ),
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),

            // Number of cards
            TextField(
              controller: _countController,
              decoration: const InputDecoration(
                labelText: 'Number of cards',
                hintText: '10',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.numbers),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),

            // Difficulty selection
            Text(
              'Difficulty Level',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: DifficultyLevel.values.map((difficulty) {
                return FilterChip(
                  label: Text(difficulty.displayName),
                  selected: _selectedDifficulty == difficulty,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedDifficulty = difficulty;
                      });
                    }
                  },
                  selectedColor: Color(difficulty.colorValue).withOpacity(0.2),
                  checkmarkColor: Color(difficulty.colorValue),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),

            // Context toggle
            CheckboxListTile(
              title: const Text('Add learning context'),
              subtitle: const Text(
                'Provide additional context for better AI generation',
              ),
              value: _includeContext,
              onChanged: (value) {
                setState(() {
                  _includeContext = value ?? false;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
            ),

            // Context input (conditional)
            if (_includeContext) ...[
              const SizedBox(height: 8),
              TextField(
                controller: _contextController,
                decoration: const InputDecoration(
                  labelText: 'Learning Context',
                  hintText:
                      'e.g., Preparing for Spanish exam, Focus on medical terms',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.info_outline),
                ),
                maxLines: 3,
                textCapitalization: TextCapitalization.sentences,
              ),
            ],

            const SizedBox(height: 16),

            // Tips section
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: Colors.blue[700],
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Tips for better flashcards:',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• Be specific with your topic\n'
                    '• Add context for personalized content\n'
                    '• Start with 5-10 cards for new topics\n'
                    '• Choose appropriate difficulty level',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.blue[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton.icon(
          onPressed: _createFlashcards,
          icon: const Icon(Icons.auto_awesome),
          label: const Text('Generate with AI'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryGreen,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  void _createFlashcards() {
    final topic = _topicController.text.trim();
    final count = int.tryParse(_countController.text) ?? 10;
    final contextText = _includeContext ? _contextController.text.trim() : null;

    if (topic.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a topic'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (count < 1 || count > 50) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Number of cards must be between 1 and 50'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    widget.onCreateFlashcards(topic, count, _selectedDifficulty, contextText);
    Navigator.pop(context);
  }
}
