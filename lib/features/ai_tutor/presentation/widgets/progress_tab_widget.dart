import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:diogeneschatbot/features/ai_tutor/presentation/widgets/progress_stat_card_widget.dart';
import 'progress_chart_widget.dart';
import 'recent_activity_list_widget.dart';
import '../../domain/entities/learning_progress.dart';
import '../../data/repositories/ai_tutor_repository.dart';
import '../../domain/services/ai_content_service.dart';

class ProgressTabWidget extends StatelessWidget {
  final LearningProgress? progress;
  final VoidCallback onShowFilters;
  final VoidCallback onExportData;
  const ProgressTabWidget({
    super.key,
    this.progress,
    required this.onShowFilters,
    required this.onExportData,
  });

  @override
  Widget build(BuildContext context) {
    final totalStudyTime = progress != null
        ? '${progress!.stats.totalStudyTime ~/ 60}h ${progress!.stats.totalStudyTime % 60}m'
        : '0h 0m';
    final averageScore = progress != null
        ? '${progress!.stats.averageQuizScore.toStringAsFixed(0)}%'
        : 'N/A';
    final studyStreak = progress?.stats.streakDays.toString() ?? '0 days';
    final conceptsMastered =
        progress?.masteredConcepts.length.toString() ?? '0';
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progress Analytics',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.filter_list),
                    onPressed: onShowFilters,
                    tooltip: 'Filter progress data',
                  ),
                  IconButton(
                    icon: const Icon(Icons.download),
                    onPressed: onExportData,
                    tooltip: 'Export progress data',
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ProgressStatCardWidget(
                  title: 'Total Study Time',
                  value: totalStudyTime,
                  icon: Icons.access_time,
                  color: Colors.blue,
                  subtitle: 'This week',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ProgressStatCardWidget(
                  title: 'Average Score',
                  value: averageScore,
                  icon: Icons.trending_up,
                  color: Colors.green,
                  subtitle: '+5% from last week',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ProgressStatCardWidget(
                  title: 'Study Streak',
                  value: studyStreak,
                  icon: Icons.local_fire_department,
                  color: Colors.orange,
                  subtitle: 'Personal best!',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ProgressStatCardWidget(
                  title: 'Concepts Mastered',
                  value: conceptsMastered,
                  icon: Icons.psychology,
                  color: Colors.purple,
                  subtitle: '3 this week',
                ),
              ),
            ],
          ),
          // Progress chart and recent activity
          const SizedBox(height: 16),
          if (progress != null) ...[
            ProgressChartWidget(
              title: 'Learning Progress',
              subject: progress!.subject,
              topic: progress!.topic,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: RecentActivityListWidget(
                user: FirebaseAuth.instance.currentUser,
                repository: RealAITutorRepository(
                  aiContentService: AIContentService(),
                ),
                subjects: const [
                  'Mathematics',
                  'Science',
                  'History',
                  'Literature',
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
