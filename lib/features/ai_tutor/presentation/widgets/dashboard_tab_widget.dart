import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/features/ai_tutor/presentation/widgets/stat_card_widget.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import '../../domain/entities/learning_progress.dart';
import '../pages/flashcard_review_page.dart';
import '../pages/quiz_taking_page.dart';
import '../bloc/ai_tutor_bloc.dart';

class DashboardTabWidget extends StatelessWidget {
  final LearningProgress? progress;
  const DashboardTabWidget({super.key, this.progress});

  @override
  Widget build(BuildContext context) {
    final studyStreak = progress?.stats.streakDays.toString() ?? '0';
    final cardsReviewed = progress?.stats.flashcardsReviewed.toString() ?? '0';
    final quizzesTaken = progress?.stats.quizzesCompleted.toString() ?? '0';
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          EnhancedCard(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.school,
                          color: AppTheme.primaryGreen,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Welcome to AI Tutor!',
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryGreen,
                                  ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Your personalized learning journey starts here',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: Colors.grey[600]),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: StatCardWidget(
                          title: 'Study Streak',
                          value: studyStreak,
                          icon: Icons.local_fire_department,
                          color: Colors.orange,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: StatCardWidget(
                          title: 'Cards Reviewed',
                          value: cardsReviewed,
                          icon: Icons.style,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: StatCardWidget(
                          title: 'Quizzes Taken',
                          value: quizzesTaken,
                          icon: Icons.quiz,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          // Quick Actions Section
          const SizedBox(height: 20),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Quick Actions',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildQuickActionButton(
                          context,
                          'Review Cards',
                          Icons.style,
                          Colors.blue,
                          () => _navigateToFlashcards(context),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildQuickActionButton(
                          context,
                          'Take Quiz',
                          Icons.quiz,
                          Colors.green,
                          () => _navigateToQuiz(context),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildQuickActionButton(
                          context,
                          'Study Plan',
                          Icons.schedule,
                          Colors.purple,
                          () => _navigateToStudyPlan(context),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildQuickActionButton(
                          context,
                          'Progress',
                          Icons.trending_up,
                          Colors.orange,
                          () => _navigateToProgress(context),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a quick action button with consistent styling
  Widget _buildQuickActionButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Navigate to flashcards review page
  void _navigateToFlashcards(BuildContext context) {
    // Get the bloc instance before navigation
    final aiTutorBloc = context.read<AITutorBloc>();

    // Navigate to flashcard review page
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: aiTutorBloc,
          child: const FlashcardReviewPage(
            dueOnly: true, // Review only due cards
          ),
        ),
      ),
    );
  }

  /// Navigate to quiz taking page
  void _navigateToQuiz(BuildContext context) {
    _showQuizSelectionDialog(context);
  }

  /// Shows a dialog to select from available quizzes or create a new one
  void _showQuizSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Quiz'),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Choose an option:'),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.add_circle, color: Colors.green),
                  title: const Text('Create New Quiz'),
                  subtitle: const Text('Generate a new quiz with AI'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _createNewQuiz(context);
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.quiz, color: Colors.blue),
                  title: const Text('Quick Math Quiz'),
                  subtitle: const Text('5 questions • Medium difficulty'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _startQuickQuiz(context, 'Mathematics');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.science, color: Colors.purple),
                  title: const Text('Quick Science Quiz'),
                  subtitle: const Text('5 questions • Medium difficulty'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _startQuickQuiz(context, 'Science');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.history_edu, color: Colors.orange),
                  title: const Text('Quick History Quiz'),
                  subtitle: const Text('5 questions • Medium difficulty'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _startQuickQuiz(context, 'History');
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  /// Creates a new quiz with user-selected parameters
  void _createNewQuiz(BuildContext context) {
    // Switch to Quiz tab (index 2) to create a new quiz
    DefaultTabController.of(context).animateTo(2);

    // Show a snackbar with instructions
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Use the Quiz tab to create a new quiz with your preferred settings.',
        ),
        duration: Duration(seconds: 3),
      ),
    );
  }

  /// Starts a quick quiz with predefined settings
  void _startQuickQuiz(BuildContext context, String subject) {
    // Show loading indicator while generating quiz
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Generating quiz...'),
          ],
        ),
      ),
    );

    // Generate a quick quiz ID and navigate
    final quickQuizId =
        'quick_${subject.toLowerCase()}_${DateTime.now().millisecondsSinceEpoch}';

    // Close loading dialog
    Navigator.of(context).pop();

    // Get the bloc instance before navigation
    final aiTutorBloc = context.read<AITutorBloc>();

    // Navigate to quiz taking page
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: aiTutorBloc,
          child: QuizTakingPage(quizId: quickQuizId),
        ),
      ),
    );
  }

  /// Navigate to study plan page
  void _navigateToStudyPlan(BuildContext context) {
    // Switch to Learning Plan tab (index 1)
    DefaultTabController.of(context).animateTo(1);
  }

  /// Navigate to progress tracking page
  void _navigateToProgress(BuildContext context) {
    // Switch to Progress tab (index 4)
    DefaultTabController.of(context).animateTo(4);
  }
}
