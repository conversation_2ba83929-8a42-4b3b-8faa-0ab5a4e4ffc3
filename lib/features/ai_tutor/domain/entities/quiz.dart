import 'package:equatable/equatable.dart';
import 'flashcard.dart'; // For DifficultyLevel

/// Represents a quiz in the AI tutor system
class Quiz extends Equatable {
  final String id;
  final String title;
  final String subject;
  final String topic;
  final List<QuizQuestion> questions;
  final DifficultyLevel difficulty;
  final DateTime createdAt;
  final int timeLimit; // in minutes
  final bool isAdaptive;
  final Map<String, dynamic> metadata;

  const Quiz({
    required this.id,
    required this.title,
    required this.subject,
    required this.topic,
    required this.questions,
    required this.difficulty,
    required this.createdAt,
    required this.timeLimit,
    required this.isAdaptive,
    required this.metadata,
  });

  /// Creates a copy of this quiz with the given fields replaced
  Quiz copyWith({
    String? id,
    String? title,
    String? subject,
    String? topic,
    List<QuizQuestion>? questions,
    DifficultyLevel? difficulty,
    DateTime? createdAt,
    int? timeLimit,
    bool? isAdaptive,
    Map<String, dynamic>? metadata,
  }) {
    return Quiz(
      id: id ?? this.id,
      title: title ?? this.title,
      subject: subject ?? this.subject,
      topic: topic ?? this.topic,
      questions: questions ?? this.questions,
      difficulty: difficulty ?? this.difficulty,
      createdAt: createdAt ?? this.createdAt,
      timeLimit: timeLimit ?? this.timeLimit,
      isAdaptive: isAdaptive ?? this.isAdaptive,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Gets the total number of questions in the quiz
  int get totalQuestions => questions.length;

  /// Gets the estimated completion time in minutes
  int get estimatedTime =>
      (questions.length * 1.5).ceil(); // 1.5 minutes per question

  @override
  List<Object> get props => [
    id,
    title,
    subject,
    topic,
    questions,
    difficulty,
    createdAt,
    timeLimit,
    isAdaptive,
    metadata,
  ];

  /// Converts this quiz to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'subject': subject,
      'topic': topic,
      'questions': questions.map((q) => q.toJson()).toList(),
      'difficulty': difficulty.name,
      'createdAt': createdAt.toIso8601String(),
      'timeLimit': timeLimit,
      'isAdaptive': isAdaptive,
      'metadata': metadata,
    };
  }

  /// Creates a quiz from a JSON map
  factory Quiz.fromJson(Map<String, dynamic> json) {
    return Quiz(
      id: json['id'] as String,
      title: json['title'] as String,
      subject: json['subject'] as String,
      topic: json['topic'] as String,
      questions: (json['questions'] as List<dynamic>)
          .map((q) => QuizQuestion.fromJson(q as Map<String, dynamic>))
          .toList(),
      difficulty: DifficultyLevel.values.firstWhere(
        (e) => e.name == json['difficulty'],
        orElse: () => DifficultyLevel.medium,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      timeLimit: json['timeLimit'] as int,
      isAdaptive: json['isAdaptive'] as bool,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
    );
  }

  @override
  String toString() {
    return 'Quiz(id: $id, title: $title, questions: ${questions.length})';
  }
}

/// Represents a single question in a quiz
class QuizQuestion extends Equatable {
  final String id;
  final String question;
  final QuestionType type;
  final List<String> options;
  final List<String> correctAnswers;
  final String explanation;
  final String concept;
  final DifficultyLevel difficulty;
  final int points;

  const QuizQuestion({
    required this.id,
    required this.question,
    required this.type,
    required this.options,
    required this.correctAnswers,
    required this.explanation,
    required this.concept,
    required this.difficulty,
    required this.points,
  });

  /// Creates a copy of this question with the given fields replaced
  QuizQuestion copyWith({
    String? id,
    String? question,
    QuestionType? type,
    List<String>? options,
    List<String>? correctAnswers,
    String? explanation,
    String? concept,
    DifficultyLevel? difficulty,
    int? points,
  }) {
    return QuizQuestion(
      id: id ?? this.id,
      question: question ?? this.question,
      type: type ?? this.type,
      options: options ?? this.options,
      correctAnswers: correctAnswers ?? this.correctAnswers,
      explanation: explanation ?? this.explanation,
      concept: concept ?? this.concept,
      difficulty: difficulty ?? this.difficulty,
      points: points ?? this.points,
    );
  }

  /// Checks if the given answers are correct
  bool isCorrect(List<String> userAnswers) {
    if (userAnswers.length != correctAnswers.length) return false;
    return userAnswers.every((answer) => correctAnswers.contains(answer));
  }

  /// Checks if this is a multiple choice question
  bool get isMultipleChoice => type == QuestionType.multipleChoice;

  /// Checks if this is a true/false question
  bool get isTrueFalse => type == QuestionType.trueFalse;

  /// Checks if this is a fill in the blank question
  bool get isFillInBlank => type == QuestionType.fillInBlank;

  @override
  List<Object> get props => [
    id,
    question,
    type,
    options,
    correctAnswers,
    explanation,
    concept,
    difficulty,
    points,
  ];

  /// Converts this question to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'type': type.name,
      'options': options,
      'correctAnswers': correctAnswers,
      'explanation': explanation,
      'concept': concept,
      'difficulty': difficulty.name,
      'points': points,
    };
  }

  /// Creates a question from a JSON map
  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    return QuizQuestion(
      id: json['id'] as String,
      question: json['question'] as String,
      type: QuestionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => QuestionType.multipleChoice,
      ),
      options: List<String>.from(json['options'] as List),
      correctAnswers: List<String>.from(json['correctAnswers'] as List),
      explanation: json['explanation'] as String,
      concept: json['concept'] as String,
      difficulty: DifficultyLevel.values.firstWhere(
        (e) => e.name == json['difficulty'],
        orElse: () => DifficultyLevel.medium,
      ),
      points: json['points'] as int,
    );
  }

  @override
  String toString() {
    return 'QuizQuestion(id: $id, type: $type, concept: $concept)';
  }
}

/// Enum representing different types of quiz questions
enum QuestionType { multipleChoice, trueFalse, fillInBlank, shortAnswer, essay }

/// Extension for question type
extension QuestionTypeExtension on QuestionType {
  String get displayName {
    switch (this) {
      case QuestionType.multipleChoice:
        return 'Multiple Choice';
      case QuestionType.trueFalse:
        return 'True/False';
      case QuestionType.fillInBlank:
        return 'Fill in the Blank';
      case QuestionType.shortAnswer:
        return 'Short Answer';
      case QuestionType.essay:
        return 'Essay';
    }
  }

  /// Returns the icon name for the question type
  String get iconName {
    switch (this) {
      case QuestionType.multipleChoice:
        return 'radio_button_checked';
      case QuestionType.trueFalse:
        return 'check_box';
      case QuestionType.fillInBlank:
        return 'edit';
      case QuestionType.shortAnswer:
        return 'short_text';
      case QuestionType.essay:
        return 'article';
    }
  }
}

/// Represents the result of a quiz attempt
class QuizResult extends Equatable {
  final String id;
  final String quizId;
  final String userId;
  final List<QuizAnswer> answers;
  final double score;
  final int totalPoints;
  final DateTime startTime;
  final DateTime endTime;
  final Duration timeSpent;
  final String subject;
  final String topic;
  final DateTime completedAt;

  const QuizResult({
    required this.id,
    required this.quizId,
    required this.userId,
    required this.answers,
    required this.score,
    required this.totalPoints,
    required this.startTime,
    required this.endTime,
    required this.timeSpent,
    required this.subject,
    required this.topic,
    required this.completedAt,
  });

  /// Gets the percentage score
  double get percentage => (score / totalPoints) * 100;

  /// Gets the number of correct answers
  int get correctAnswers => answers.where((answer) => answer.isCorrect).length;

  /// Gets the total number of questions
  int get totalQuestions => answers.length;

  /// Converts this quiz result to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quizId': quizId,
      'userId': userId,
      'answers': answers.map((a) => a.toJson()).toList(),
      'score': score,
      'totalPoints': totalPoints,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'timeSpent': timeSpent.inMilliseconds,
      'subject': subject,
      'topic': topic,
      'completedAt': completedAt.toIso8601String(),
    };
  }

  /// Creates a quiz result from a JSON map
  factory QuizResult.fromJson(Map<String, dynamic> json) {
    return QuizResult(
      id: json['id'] as String,
      quizId: json['quizId'] as String,
      userId: json['userId'] as String,
      answers: (json['answers'] as List<dynamic>)
          .map((a) => QuizAnswer.fromJson(a as Map<String, dynamic>))
          .toList(),
      score: (json['score'] as num).toDouble(),
      totalPoints: json['totalPoints'] as int,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      timeSpent: Duration(milliseconds: json['timeSpent'] as int),
      subject: json['subject'] as String,
      topic: json['topic'] as String,
      completedAt: DateTime.parse(json['completedAt'] as String),
    );
  }

  @override
  List<Object> get props => [
    id,
    quizId,
    userId,
    answers,
    score,
    totalPoints,
    startTime,
    endTime,
    timeSpent,
    subject,
    topic,
    completedAt,
  ];
}

/// Represents a user's answer to a quiz question
class QuizAnswer extends Equatable {
  final String questionId;
  final List<String> userAnswers;
  final bool isCorrect;
  final int pointsEarned;
  final String concept;

  const QuizAnswer({
    required this.questionId,
    required this.userAnswers,
    required this.isCorrect,
    required this.pointsEarned,
    required this.concept,
  });

  /// Converts this quiz answer to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'questionId': questionId,
      'userAnswers': userAnswers,
      'isCorrect': isCorrect,
      'pointsEarned': pointsEarned,
      'concept': concept,
    };
  }

  /// Creates a quiz answer from a JSON map
  factory QuizAnswer.fromJson(Map<String, dynamic> json) {
    return QuizAnswer(
      questionId: json['questionId'] as String,
      userAnswers: List<String>.from(json['userAnswers'] as List),
      isCorrect: json['isCorrect'] as bool,
      pointsEarned: json['pointsEarned'] as int,
      concept: json['concept'] as String,
    );
  }

  @override
  List<Object> get props => [
    questionId,
    userAnswers,
    isCorrect,
    pointsEarned,
    concept,
  ];
}
