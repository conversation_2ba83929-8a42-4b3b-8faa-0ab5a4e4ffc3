import 'package:equatable/equatable.dart';

/// Represents a learning session in the AI tutor system
class LearningSession extends Equatable {
  final String id;
  final String userId;
  final String subject;
  final String topic;
  final DateTime startTime;
  final DateTime? endTime;
  final LearningSessionStatus status;
  final List<String> conceptsCovered;
  final double comprehensionScore;
  final Map<String, dynamic> metadata;

  const LearningSession({
    required this.id,
    required this.userId,
    required this.subject,
    required this.topic,
    required this.startTime,
    this.endTime,
    required this.status,
    required this.conceptsCovered,
    required this.comprehensionScore,
    required this.metadata,
  });

  /// Creates a copy of this learning session with the given fields replaced
  LearningSession copyWith({
    String? id,
    String? userId,
    String? subject,
    String? topic,
    DateTime? startTime,
    DateTime? endTime,
    LearningSessionStatus? status,
    List<String>? conceptsCovered,
    double? comprehensionScore,
    Map<String, dynamic>? metadata,
  }) {
    return LearningSession(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      subject: subject ?? this.subject,
      topic: topic ?? this.topic,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      conceptsCovered: conceptsCovered ?? this.conceptsCovered,
      comprehensionScore: comprehensionScore ?? this.comprehensionScore,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Calculates the duration of the learning session
  Duration? get duration {
    if (endTime == null) return null;
    return endTime!.difference(startTime);
  }

  /// Checks if the session is currently active
  bool get isActive => status == LearningSessionStatus.active;

  /// Checks if the session is completed
  bool get isCompleted => status == LearningSessionStatus.completed;

  @override
  List<Object?> get props => [
    id,
    userId,
    subject,
    topic,
    startTime,
    endTime,
    status,
    conceptsCovered,
    comprehensionScore,
    metadata,
  ];

  /// Converts this learning session to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'subject': subject,
      'topic': topic,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'status': status.name,
      'conceptsCovered': conceptsCovered,
      'comprehensionScore': comprehensionScore,
      'metadata': metadata,
    };
  }

  /// Creates a learning session from a JSON map
  factory LearningSession.fromJson(Map<String, dynamic> json) {
    return LearningSession(
      id: json['id'] as String,
      userId: json['userId'] as String,
      subject: json['subject'] as String,
      topic: json['topic'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] != null
          ? DateTime.parse(json['endTime'] as String)
          : null,
      status: LearningSessionStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => LearningSessionStatus.active,
      ),
      conceptsCovered: List<String>.from(json['conceptsCovered'] as List),
      comprehensionScore: (json['comprehensionScore'] as num).toDouble(),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
    );
  }

  @override
  String toString() {
    return 'LearningSession(id: $id, subject: $subject, topic: $topic, status: $status)';
  }
}

/// Enum representing the status of a learning session
enum LearningSessionStatus { active, completed, paused, cancelled }

/// Extension to provide human-readable names for learning session status
extension LearningSessionStatusExtension on LearningSessionStatus {
  String get displayName {
    switch (this) {
      case LearningSessionStatus.active:
        return 'Active';
      case LearningSessionStatus.completed:
        return 'Completed';
      case LearningSessionStatus.paused:
        return 'Paused';
      case LearningSessionStatus.cancelled:
        return 'Cancelled';
    }
  }

  bool get isInProgress {
    return this == LearningSessionStatus.active ||
        this == LearningSessionStatus.paused;
  }
}
