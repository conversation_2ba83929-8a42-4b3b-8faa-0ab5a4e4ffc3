import 'package:equatable/equatable.dart';

/// Represents comprehensive learning analytics for a user
class LearningAnalytics extends Equatable {
  final String id;
  final String userId;
  final String? subject;
  final DateTime startDate;
  final DateTime endDate;
  final int totalStudyTime; // in minutes
  final int totalSessions;
  final double averageSessionDuration; // in minutes
  final int totalQuizzes;
  final double averageQuizScore;
  final int currentStreak; // in days
  final Map<String, int> weeklyActivity; // date -> minutes studied
  final Map<String, Map<String, dynamic>> subjectBreakdown;
  final DateTime generatedAt;
  final Map<String, dynamic> metadata;

  const LearningAnalytics({
    required this.id,
    required this.userId,
    this.subject,
    required this.startDate,
    required this.endDate,
    required this.totalStudyTime,
    required this.totalSessions,
    required this.averageSessionDuration,
    required this.totalQuizzes,
    required this.averageQuizScore,
    required this.currentStreak,
    required this.weeklyActivity,
    required this.subjectBreakdown,
    required this.generatedAt,
    required this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        subject,
        startDate,
        endDate,
        totalStudyTime,
        totalSessions,
        averageSessionDuration,
        totalQuizzes,
        averageQuizScore,
        currentStreak,
        weeklyActivity,
        subjectBreakdown,
        generatedAt,
        metadata,
      ];

  /// Creates a copy of this analytics with updated fields
  LearningAnalytics copyWith({
    String? id,
    String? userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
    int? totalStudyTime,
    int? totalSessions,
    double? averageSessionDuration,
    int? totalQuizzes,
    double? averageQuizScore,
    int? currentStreak,
    Map<String, int>? weeklyActivity,
    Map<String, Map<String, dynamic>>? subjectBreakdown,
    DateTime? generatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return LearningAnalytics(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      subject: subject ?? this.subject,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      totalStudyTime: totalStudyTime ?? this.totalStudyTime,
      totalSessions: totalSessions ?? this.totalSessions,
      averageSessionDuration: averageSessionDuration ?? this.averageSessionDuration,
      totalQuizzes: totalQuizzes ?? this.totalQuizzes,
      averageQuizScore: averageQuizScore ?? this.averageQuizScore,
      currentStreak: currentStreak ?? this.currentStreak,
      weeklyActivity: weeklyActivity ?? this.weeklyActivity,
      subjectBreakdown: subjectBreakdown ?? this.subjectBreakdown,
      generatedAt: generatedAt ?? this.generatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Converts this analytics to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'subject': subject,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'totalStudyTime': totalStudyTime,
      'totalSessions': totalSessions,
      'averageSessionDuration': averageSessionDuration,
      'totalQuizzes': totalQuizzes,
      'averageQuizScore': averageQuizScore,
      'currentStreak': currentStreak,
      'weeklyActivity': weeklyActivity,
      'subjectBreakdown': subjectBreakdown,
      'generatedAt': generatedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Creates analytics from a JSON map
  factory LearningAnalytics.fromJson(Map<String, dynamic> json) {
    return LearningAnalytics(
      id: json['id'] as String,
      userId: json['userId'] as String,
      subject: json['subject'] as String?,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      totalStudyTime: json['totalStudyTime'] as int,
      totalSessions: json['totalSessions'] as int,
      averageSessionDuration: (json['averageSessionDuration'] as num).toDouble(),
      totalQuizzes: json['totalQuizzes'] as int,
      averageQuizScore: (json['averageQuizScore'] as num).toDouble(),
      currentStreak: json['currentStreak'] as int,
      weeklyActivity: Map<String, int>.from(
        (json['weeklyActivity'] as Map).map(
          (key, value) => MapEntry(key as String, value as int),
        ),
      ),
      subjectBreakdown: Map<String, Map<String, dynamic>>.from(
        (json['subjectBreakdown'] as Map).map(
          (key, value) => MapEntry(
            key as String,
            Map<String, dynamic>.from(value as Map),
          ),
        ),
      ),
      generatedAt: DateTime.parse(json['generatedAt'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
    );
  }

  /// Gets the total study time in hours
  double get totalStudyHours => totalStudyTime / 60.0;

  /// Gets the average session duration in hours
  double get averageSessionHours => averageSessionDuration / 60.0;

  /// Gets the total weekly study time
  int get totalWeeklyTime => weeklyActivity.values.fold(0, (sum, time) => sum + time);

  /// Gets the average daily study time for the week
  double get averageDailyTime => totalWeeklyTime / 7.0;

  /// Gets the most active day of the week
  String? get mostActiveDay {
    if (weeklyActivity.isEmpty) return null;
    
    final sortedEntries = weeklyActivity.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedEntries.first.key;
  }

  /// Gets the most studied subject
  String? get mostStudiedSubject {
    if (subjectBreakdown.isEmpty) return null;
    
    final sortedEntries = subjectBreakdown.entries.toList()
      ..sort((a, b) {
        final aTime = a.value['totalTime'] as int? ?? 0;
        final bTime = b.value['totalTime'] as int? ?? 0;
        return bTime.compareTo(aTime);
      });
    
    return sortedEntries.first.key;
  }

  /// Gets the subject with the highest average quiz score
  String? get bestPerformingSubject {
    if (subjectBreakdown.isEmpty) return null;
    
    final sortedEntries = subjectBreakdown.entries.toList()
      ..sort((a, b) {
        final aScore = a.value['averageScore'] as double? ?? 0.0;
        final bScore = b.value['averageScore'] as double? ?? 0.0;
        return bScore.compareTo(aScore);
      });
    
    return sortedEntries.first.key;
  }

  /// Gets learning consistency percentage (days studied / total days)
  double get consistencyPercentage {
    final totalDays = endDate.difference(startDate).inDays + 1;
    final studyDays = weeklyActivity.values.where((time) => time > 0).length;
    return totalDays > 0 ? (studyDays / totalDays).clamp(0.0, 1.0) : 0.0;
  }

  /// Gets learning efficiency (quiz score improvement over time)
  String get learningTrend {
    if (averageQuizScore >= 90) return 'Excellent';
    if (averageQuizScore >= 80) return 'Good';
    if (averageQuizScore >= 70) return 'Fair';
    if (averageQuizScore >= 60) return 'Needs Improvement';
    return 'Poor';
  }

  /// Gets streak status description
  String get streakStatus {
    if (currentStreak >= 30) return 'Amazing streak! 🔥';
    if (currentStreak >= 14) return 'Great streak! 💪';
    if (currentStreak >= 7) return 'Good streak! 👍';
    if (currentStreak >= 3) return 'Building momentum! 📈';
    if (currentStreak >= 1) return 'Getting started! 🌱';
    return 'Start your streak today! ⭐';
  }

  /// Checks if the analytics data is recent (less than 24 hours old)
  bool get isRecent {
    final now = DateTime.now();
    final age = now.difference(generatedAt);
    return age.inHours < 24;
  }

  /// Gets the time period covered by this analytics
  Duration get timePeriod => endDate.difference(startDate);

  /// Gets the number of days covered by this analytics
  int get daysCovered => timePeriod.inDays + 1;

  @override
  String toString() {
    return 'LearningAnalytics(userId: $userId, period: ${daysCovered}d, studyTime: ${totalStudyHours.toStringAsFixed(1)}h, streak: ${currentStreak}d)';
  }
}
