import 'package:equatable/equatable.dart';

/// Represents a learning goal set by the user
class LearningGoal extends Equatable {
  final String id;
  final String userId;
  final String title;
  final String description;
  final String subject;
  final String? topic;
  final GoalType type;
  final DateTime targetDate;
  final DateTime createdAt;
  final bool isCompleted;
  final double progress; // 0.0 to 1.0
  final List<String> milestones;
  final List<String> completedMilestones;
  final Map<String, dynamic> metadata;

  const LearningGoal({
    required this.id,
    required this.userId,
    required this.title,
    required this.description,
    required this.subject,
    this.topic,
    required this.type,
    required this.targetDate,
    required this.createdAt,
    required this.isCompleted,
    required this.progress,
    required this.milestones,
    required this.completedMilestones,
    required this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        title,
        description,
        subject,
        topic,
        type,
        targetDate,
        createdAt,
        isCompleted,
        progress,
        milestones,
        completedMilestones,
        metadata,
      ];

  /// Creates a copy of this goal with updated fields
  LearningGoal copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    String? subject,
    String? topic,
    GoalType? type,
    DateTime? targetDate,
    DateTime? createdAt,
    bool? isCompleted,
    double? progress,
    List<String>? milestones,
    List<String>? completedMilestones,
    Map<String, dynamic>? metadata,
  }) {
    return LearningGoal(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      subject: subject ?? this.subject,
      topic: topic ?? this.topic,
      type: type ?? this.type,
      targetDate: targetDate ?? this.targetDate,
      createdAt: createdAt ?? this.createdAt,
      isCompleted: isCompleted ?? this.isCompleted,
      progress: progress ?? this.progress,
      milestones: milestones ?? this.milestones,
      completedMilestones: completedMilestones ?? this.completedMilestones,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Converts this goal to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'description': description,
      'subject': subject,
      'topic': topic,
      'type': type.name,
      'targetDate': targetDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'isCompleted': isCompleted,
      'progress': progress,
      'milestones': milestones,
      'completedMilestones': completedMilestones,
      'metadata': metadata,
    };
  }

  /// Creates a goal from a JSON map
  factory LearningGoal.fromJson(Map<String, dynamic> json) {
    return LearningGoal(
      id: json['id'] as String,
      userId: json['userId'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      subject: json['subject'] as String,
      topic: json['topic'] as String?,
      type: GoalType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => GoalType.general,
      ),
      targetDate: DateTime.parse(json['targetDate'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      isCompleted: json['isCompleted'] as bool,
      progress: (json['progress'] as num).toDouble(),
      milestones: List<String>.from(json['milestones'] as List),
      completedMilestones: List<String>.from(json['completedMilestones'] as List),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
    );
  }

  /// Checks if the goal is overdue
  bool get isOverdue => !isCompleted && DateTime.now().isAfter(targetDate);

  /// Gets the number of days remaining until the target date
  int get daysRemaining => targetDate.difference(DateTime.now()).inDays;

  /// Gets the completion percentage as an integer (0-100)
  int get completionPercentage => (progress * 100).round();
}

/// Types of learning goals
enum GoalType {
  general,
  exam,
  certification,
  skill,
  course,
  project,
  habit,
}

/// Extension to provide display names for goal types
extension GoalTypeExtension on GoalType {
  String get displayName {
    switch (this) {
      case GoalType.general:
        return 'General Learning';
      case GoalType.exam:
        return 'Exam Preparation';
      case GoalType.certification:
        return 'Certification';
      case GoalType.skill:
        return 'Skill Development';
      case GoalType.course:
        return 'Course Completion';
      case GoalType.project:
        return 'Project-Based Learning';
      case GoalType.habit:
        return 'Learning Habit';
    }
  }

  String get description {
    switch (this) {
      case GoalType.general:
        return 'General learning objectives and knowledge acquisition';
      case GoalType.exam:
        return 'Preparation for specific exams or tests';
      case GoalType.certification:
        return 'Working towards professional certifications';
      case GoalType.skill:
        return 'Developing specific skills or competencies';
      case GoalType.course:
        return 'Completing online courses or educational programs';
      case GoalType.project:
        return 'Learning through hands-on projects and practical application';
      case GoalType.habit:
        return 'Building consistent learning habits and routines';
    }
  }
}
