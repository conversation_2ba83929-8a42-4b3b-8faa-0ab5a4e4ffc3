import 'package:equatable/equatable.dart';

/// Represents a study recommendation for personalized learning
class StudyRecommendation extends Equatable {
  final String id;
  final String title;
  final String description;
  final RecommendationType type;
  final String subject;
  final String? topic;
  final int priority; // 1-5, 5 being highest
  final Duration estimatedTime;
  final Map<String, dynamic> metadata;

  const StudyRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.subject,
    this.topic,
    required this.priority,
    required this.estimatedTime,
    required this.metadata,
  });

  /// Creates a copy of this recommendation with the given fields replaced
  StudyRecommendation copyWith({
    String? id,
    String? title,
    String? description,
    RecommendationType? type,
    String? subject,
    String? topic,
    int? priority,
    Duration? estimatedTime,
    Map<String, dynamic>? metadata,
  }) {
    return StudyRecommendation(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      subject: subject ?? this.subject,
      topic: topic ?? this.topic,
      priority: priority ?? this.priority,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [id, title, description, type, subject, topic, priority, estimatedTime, metadata];

  /// Converts this study recommendation to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'subject': subject,
      'topic': topic,
      'priority': priority,
      'estimatedTime': estimatedTime.inMinutes,
      'metadata': metadata,
    };
  }

  /// Creates a study recommendation from a JSON map
  factory StudyRecommendation.fromJson(Map<String, dynamic> json) {
    // Basic validation and safer parsing with defaults
    final typeStr = json['type'] as String?;
    final type = typeStr != null
        ? RecommendationType.values.firstWhere((e) => e.name == typeStr, orElse: () => RecommendationType.studyConcept)
        : RecommendationType.studyConcept;

    final estimatedMinutesRaw = json['estimatedTime'];
    final estimatedMinutes = estimatedMinutesRaw is int
        ? estimatedMinutesRaw
        : (estimatedMinutesRaw is num ? estimatedMinutesRaw.toInt() : 30); // default 30min

    final metadataRaw = json['metadata'];
    final metadata = metadataRaw is Map ? Map<String, dynamic>.from(metadataRaw) : <String, dynamic>{};

    return StudyRecommendation(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: type,
      subject: json['subject'] as String,
      topic: json['topic'] as String?,
      priority: (json['priority'] as num).toInt(),
      estimatedTime: Duration(minutes: estimatedMinutes),
      metadata: metadata,
    );
  }

  @override
  String toString() {
    return 'StudyRecommendation(title: $title, type: $type, priority: $priority)';
  }
}

/// Enum representing different types of study recommendations
enum RecommendationType {
  reviewFlashcards,
  takeQuiz,
  studyConcept,
  practiceProblems,
  readMaterial,
  watchVideo,
  reviewWeakConcepts,
  practiceFlashcards,
  studySession,
}

/// Extension for recommendation type display and functionality
extension RecommendationTypeExtension on RecommendationType {
  /// Gets the display name for the recommendation type
  String get displayName {
    switch (this) {
      case RecommendationType.reviewFlashcards:
        return 'Review Flashcards';
      case RecommendationType.takeQuiz:
        return 'Take Quiz';
      case RecommendationType.studyConcept:
        return 'Study Concept';
      case RecommendationType.practiceProblems:
        return 'Practice Problems';
      case RecommendationType.readMaterial:
        return 'Read Material';
      case RecommendationType.watchVideo:
        return 'Watch Video';
      case RecommendationType.reviewWeakConcepts:
        return 'Review Weak Concepts';
      case RecommendationType.practiceFlashcards:
        return 'Practice Flashcards';
      case RecommendationType.studySession:
        return 'Study Session';
    }
  }

  /// Gets the icon name for the recommendation type
  String get iconName {
    switch (this) {
      case RecommendationType.reviewFlashcards:
        return 'style';
      case RecommendationType.takeQuiz:
        return 'quiz';
      case RecommendationType.studyConcept:
        return 'school';
      case RecommendationType.practiceProblems:
        return 'calculate';
      case RecommendationType.readMaterial:
        return 'menu_book';
      case RecommendationType.watchVideo:
        return 'play_circle';
      case RecommendationType.reviewWeakConcepts:
        return 'trending_down';
      case RecommendationType.practiceFlashcards:
        return 'style';
      case RecommendationType.studySession:
        return 'school';
    }
  }

  /// Gets the color associated with the recommendation type
  String get colorName {
    switch (this) {
      case RecommendationType.reviewFlashcards:
        return 'blue';
      case RecommendationType.takeQuiz:
        return 'green';
      case RecommendationType.studyConcept:
        return 'purple';
      case RecommendationType.practiceProblems:
        return 'orange';
      case RecommendationType.readMaterial:
        return 'teal';
      case RecommendationType.watchVideo:
        return 'red';
      case RecommendationType.reviewWeakConcepts:
        return 'amber';
      case RecommendationType.practiceFlashcards:
        return 'indigo';
      case RecommendationType.studySession:
        return 'cyan';
    }
  }
}
