import 'package:equatable/equatable.dart';

/// Represents a user's learning streak and study consistency
class LearningStreak extends Equatable {
  final String userId;
  final int currentStreak;
  final int longestStreak;
  final DateTime lastStudyDate;
  final DateTime streakStartDate;
  final List<DateTime> studyDates;
  final Map<String, dynamic> metadata;

  const LearningStreak({
    required this.userId,
    required this.currentStreak,
    required this.longestStreak,
    required this.lastStudyDate,
    required this.streakStartDate,
    required this.studyDates,
    this.metadata = const {},
  });

  @override
  List<Object?> get props => [
    userId,
    currentStreak,
    longestStreak,
    lastStudyDate,
    streakStartDate,
    studyDates,
    metadata,
  ];

  /// Creates a copy of this streak with updated fields
  LearningStreak copyWith({
    String? userId,
    int? currentStreak,
    int? longestStreak,
    DateTime? lastStudyDate,
    DateTime? streakStartDate,
    List<DateTime>? studyDates,
    Map<String, dynamic>? metadata,
  }) {
    return LearningStreak(
      userId: userId ?? this.userId,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      lastStudyDate: lastStudyDate ?? this.lastStudyDate,
      streakStartDate: streakStartDate ?? this.streakStartDate,
      studyDates: studyDates ?? this.studyDates,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Converts this streak to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'lastStudyDate': lastStudyDate.toIso8601String(),
      'streakStartDate': streakStartDate.toIso8601String(),
      'studyDates': studyDates.map((date) => date.toIso8601String()).toList(),
      'metadata': metadata,
    };
  }

  /// Creates a streak from a JSON map
  factory LearningStreak.fromJson(Map<String, dynamic> json) {
    return LearningStreak(
      userId: json['userId'] as String,
      currentStreak: json['currentStreak'] as int,
      longestStreak: json['longestStreak'] as int,
      lastStudyDate: DateTime.parse(json['lastStudyDate'] as String),
      streakStartDate: DateTime.parse(json['streakStartDate'] as String),
      studyDates: (json['studyDates'] as List)
          .map((dateStr) => DateTime.parse(dateStr as String))
          .toList(),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  /// Checks if the streak is still active (studied within the last 2 days)
  bool get isActive {
    final now = DateTime.now();
    final daysSinceLastStudy = now.difference(lastStudyDate).inDays;
    return daysSinceLastStudy <= 1; // Allow 1 day gap
  }

  /// Checks if the streak is at risk (last study was yesterday)
  bool get isAtRisk {
    final now = DateTime.now();
    final daysSinceLastStudy = now.difference(lastStudyDate).inDays;
    return daysSinceLastStudy == 1;
  }

  /// Checks if the streak is broken (more than 1 day since last study)
  bool get isBroken {
    final now = DateTime.now();
    final daysSinceLastStudy = now.difference(lastStudyDate).inDays;
    return daysSinceLastStudy > 1;
  }

  /// Gets the number of days since the last study session
  int get daysSinceLastStudy {
    return DateTime.now().difference(lastStudyDate).inDays;
  }

  /// Gets the total number of study days recorded
  int get totalStudyDays => studyDates.length;

  /// Gets the streak duration in days
  int get streakDuration {
    return DateTime.now().difference(streakStartDate).inDays + 1;
  }

  /// Calculates study consistency percentage over the last 30 days
  double get consistencyPercentage {
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));

    final recentStudyDays = studyDates
        .where(
          (date) =>
              date.isAfter(thirtyDaysAgo) &&
              date.isBefore(now.add(const Duration(days: 1))),
        )
        .length;

    return (recentStudyDays / 30.0).clamp(0.0, 1.0);
  }

  /// Gets study frequency description
  String get frequencyDescription {
    final consistency = consistencyPercentage;
    if (consistency >= 0.9) return 'Excellent';
    if (consistency >= 0.7) return 'Good';
    if (consistency >= 0.5) return 'Fair';
    if (consistency >= 0.3) return 'Needs Improvement';
    return 'Poor';
  }

  /// Updates the streak with a new study session
  LearningStreak updateWithStudySession(DateTime studyDate) {
    final dateOnly = DateTime(studyDate.year, studyDate.month, studyDate.day);
    final lastStudyDateOnly = DateTime(
      lastStudyDate.year,
      lastStudyDate.month,
      lastStudyDate.day,
    );

    // Don't update if already studied today
    if (dateOnly.isAtSameMomentAs(lastStudyDateOnly)) {
      return this;
    }

    final updatedStudyDates = List<DateTime>.from(studyDates);
    if (!updatedStudyDates.any(
      (date) =>
          date.year == dateOnly.year &&
          date.month == dateOnly.month &&
          date.day == dateOnly.day,
    )) {
      updatedStudyDates.add(dateOnly);
    }

    // Calculate new streak
    final daysDifference = dateOnly.difference(lastStudyDateOnly).inDays;
    int newCurrentStreak;
    DateTime newStreakStartDate;

    if (daysDifference == 1) {
      // Consecutive day - continue streak
      newCurrentStreak = currentStreak + 1;
      newStreakStartDate = streakStartDate;
    } else if (daysDifference == 0) {
      // Same day - no change
      newCurrentStreak = currentStreak;
      newStreakStartDate = streakStartDate;
    } else {
      // Gap in streak - reset
      newCurrentStreak = 1;
      newStreakStartDate = dateOnly;
    }

    final newLongestStreak = newCurrentStreak > longestStreak
        ? newCurrentStreak
        : longestStreak;

    return copyWith(
      currentStreak: newCurrentStreak,
      longestStreak: newLongestStreak,
      lastStudyDate: studyDate,
      streakStartDate: newStreakStartDate,
      studyDates: updatedStudyDates,
    );
  }

  /// Resets the streak
  LearningStreak resetStreak() {
    return copyWith(currentStreak: 0, streakStartDate: DateTime.now());
  }

  @override
  String toString() {
    return 'LearningStreak(current: $currentStreak, longest: $longestStreak, active: $isActive)';
  }
}
