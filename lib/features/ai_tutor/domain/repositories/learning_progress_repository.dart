import 'package:dartz/dartz.dart';
import '../entities/learning_progress.dart';
import '../entities/learning_session.dart';
import '../entities/learning_goal.dart';
import '../entities/learning_streak.dart';
import '../../../../core/error/failures.dart';

/// Repository interface for learning progress operations
abstract class LearningProgressRepository {
  /// Saves learning progress to the repository
  Future<Either<Failure, void>> saveLearningProgress(LearningProgress progress);

  /// Retrieves learning progress for a user and subject
  Future<Either<Failure, LearningProgress?>> getLearningProgress({
    required String userId,
    required String subject,
    String? topic,
  });

  /// Retrieves all learning progress for a user
  Future<Either<Failure, List<LearningProgress>>> getUserLearningProgress(
    String userId,
  );

  /// Updates concept progress for a specific concept
  Future<Either<Failure, void>> updateConceptProgress({
    required String userId,
    required String subject,
    required String concept,
    required double progress,
  });

  /// Adds a concept to mastered concepts
  Future<Either<Failure, void>> markConceptAsMastered({
    required String userId,
    required String subject,
    required String concept,
  });

  /// Adds a concept to struggling concepts
  Future<Either<Failure, void>> markConceptAsStruggling({
    required String userId,
    required String subject,
    required String concept,
  });

  /// Removes a concept from struggling concepts
  Future<Either<Failure, void>> removeConceptFromStruggling({
    required String userId,
    required String subject,
    required String concept,
  });

  /// Updates learning statistics
  Future<Either<Failure, void>> updateLearningStats({
    required String userId,
    required String subject,
    required LearningStats stats,
  });

  /// Records a learning session
  Future<Either<Failure, void>> recordLearningSession(LearningSession session);

  /// Gets learning sessions for a user
  Future<Either<Failure, List<LearningSession>>> getLearningSessionsForUser({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Gets learning analytics for a user
  Future<Either<Failure, LearningAnalytics>> getLearningAnalytics({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Gets learning streaks for a user
  Future<Either<Failure, LearningStreak>> getLearningStreak(String userId);

  /// Updates daily learning activity
  Future<Either<Failure, void>> updateDailyActivity({
    required String userId,
    required DateTime date,
    required int minutesStudied,
  });

  /// Gets weekly learning activity
  Future<Either<Failure, Map<String, int>>> getWeeklyActivity({
    required String userId,
    required DateTime weekStart,
  });

  /// Gets learning goals for a user
  Future<Either<Failure, List<LearningGoal>>> getLearningGoals(String userId);

  /// Saves a learning goal
  Future<Either<Failure, void>> saveLearningGoal(LearningGoal goal);

  /// Updates learning goal progress
  Future<Either<Failure, void>> updateLearningGoalProgress({
    required String goalId,
    required double progress,
  });

  /// Deletes a learning goal
  Future<Either<Failure, void>> deleteLearningGoal(String goalId);

  /// Gets learning recommendations based on progress
  Future<Either<Failure, List<LearningRecommendation>>>
  getLearningRecommendations({required String userId, String? subject});
}

/// Represents learning analytics data
class LearningAnalytics {
  final String userId;
  final DateTime startDate;
  final DateTime endDate;
  final int totalStudyTime; // in minutes
  final int totalSessions;
  final double averageSessionDuration;
  final Map<String, int> subjectTimeDistribution;
  final Map<String, double> conceptMasteryRates;
  final List<DailyActivity> dailyActivities;
  final double overallProgress;
  final int streakDays;

  const LearningAnalytics({
    required this.userId,
    required this.startDate,
    required this.endDate,
    required this.totalStudyTime,
    required this.totalSessions,
    required this.averageSessionDuration,
    required this.subjectTimeDistribution,
    required this.conceptMasteryRates,
    required this.dailyActivities,
    required this.overallProgress,
    required this.streakDays,
  });

  /// Gets total study time in hours
  double get totalStudyHours => totalStudyTime / 60.0;

  /// Gets average daily study time in minutes
  double get averageDailyStudyTime {
    final days = endDate.difference(startDate).inDays + 1;
    return totalStudyTime / days;
  }

  @override
  String toString() {
    return 'LearningAnalytics(userId: $userId, totalTime: ${totalStudyHours.toStringAsFixed(1)}h, sessions: $totalSessions)';
  }
}

/// Represents daily learning activity
class DailyActivity {
  final DateTime date;
  final int minutesStudied;
  final int sessionsCompleted;
  final List<String> subjectsStudied;
  final double averageScore;

  const DailyActivity({
    required this.date,
    required this.minutesStudied,
    required this.sessionsCompleted,
    required this.subjectsStudied,
    required this.averageScore,
  });

  /// Gets study time in hours
  double get hoursStudied => minutesStudied / 60.0;

  @override
  String toString() {
    return 'DailyActivity(date: $date, minutes: $minutesStudied, sessions: $sessionsCompleted)';
  }
}

/// Represents a learning recommendation
class LearningRecommendation {
  final String id;
  final String title;
  final String description;
  final RecommendationType type;
  final int priority; // 1-5, 5 being highest
  final String subject;
  final String? topic;
  final Duration estimatedTime;
  final Map<String, dynamic> metadata;

  const LearningRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.priority,
    required this.subject,
    this.topic,
    required this.estimatedTime,
    required this.metadata,
  });

  @override
  String toString() {
    return 'LearningRecommendation(title: $title, type: $type, priority: $priority)';
  }
}

/// Enum representing different types of learning recommendations
enum RecommendationType {
  reviewWeakConcepts,
  practiceFlashcards,
  takeQuiz,
  studyNewTopic,
  reviewMistakes,
  maintainStreak,
}
