import 'package:dartz/dartz.dart';
import '../entities/flashcard.dart';
import '../entities/quiz.dart';
import '../entities/learning_progress.dart';
import '../entities/learning_session.dart';
import '../entities/study_recommendation.dart';
import '../../../../core/error/failures.dart';

/// Repository interface for AI tutor functionality
abstract class AITutorRepository {
  /// Generates a personalized learning plan based on user preferences and goals
  Future<Either<Failure, LearningPlan>> generateLearningPlan({
    required String subject,
    required String currentLevel,
    required List<String> learningGoals,
    required Map<String, dynamic> preferences,
  });

  /// Generates flashcards for a specific topic
  Future<Either<Failure, List<Flashcard>>> generateFlashcards({
    required String topic,
    required int count,
    required DifficultyLevel difficulty,
    String? context,
  });

  /// Generates an adaptive quiz based on user's current level and previous performance
  Future<Either<Failure, Quiz>> generateAdaptiveQuiz({
    required String topic,
    required List<String> concepts,
    required DifficultyLevel currentLevel,
    List<QuizResult>? previousResults,
  });

  /// Explains a concept in simple terms using AI
  Future<Either<Failure, String>> explainConcept({
    required String concept,
    required String context,
    required ExplanationStyle style,
  });

  /// Identifies knowledge gaps based on quiz results and learning history
  Future<Either<Failure, List<String>>> identifyKnowledgeGaps({
    required List<QuizResult> quizResults,
    required String subject,
  });

  /// Saves a learning session to the repository
  Future<Either<Failure, void>> saveLearningSession(LearningSession session);

  /// Retrieves learning sessions for a user
  Future<Either<Failure, List<LearningSession>>> getUserLearningSessions(
    String userId,
  );

  /// Saves a learning plan to the repository
  Future<Either<Failure, void>> saveLearningPlan(LearningPlan plan);

  /// Retrieves learning plans for a user
  Future<Either<Failure, List<LearningPlan>>> getUserLearningPlans(
    String userId,
  );

  /// Updates learning progress for a user
  Future<Either<Failure, void>> updateLearningProgress(
    LearningProgress progress,
  );

  /// Retrieves learning progress for a user and subject
  Future<Either<Failure, LearningProgress?>> getLearningProgress({
    required String userId,
    required String subject,
    String? topic,
  });

  /// Saves quiz results
  Future<Either<Failure, void>> saveQuizResult(QuizResult result);

  /// Retrieves quiz results for a user
  Future<Either<Failure, List<QuizResult>>> getUserQuizResults(String userId);

  /// Generates study recommendations based on user's learning history
  Future<Either<Failure, List<StudyRecommendation>>>
  generateStudyRecommendations({
    required String userId,
    required String subject,
  });

  /// Searches quizzes by query and filters
  Future<Either<Failure, List<Quiz>>> searchQuizzes({
    required String query,
    String? subject,
    String? topic,
    DifficultyLevel? difficulty,
  });
}

/// Enum representing different explanation styles
enum ExplanationStyle { simple, detailed, analogy, stepByStep, visual }

/// Extension for explanation style
extension ExplanationStyleExtension on ExplanationStyle {
  String get displayName {
    switch (this) {
      case ExplanationStyle.simple:
        return 'Simple';
      case ExplanationStyle.detailed:
        return 'Detailed';
      case ExplanationStyle.analogy:
        return 'With Analogies';
      case ExplanationStyle.stepByStep:
        return 'Step by Step';
      case ExplanationStyle.visual:
        return 'Visual';
    }
  }

  String get description {
    switch (this) {
      case ExplanationStyle.simple:
        return 'Easy to understand explanation';
      case ExplanationStyle.detailed:
        return 'Comprehensive and thorough explanation';
      case ExplanationStyle.analogy:
        return 'Explanation using real-world analogies';
      case ExplanationStyle.stepByStep:
        return 'Break down into clear steps';
      case ExplanationStyle.visual:
        return 'Visual representation and diagrams';
    }
  }
}
