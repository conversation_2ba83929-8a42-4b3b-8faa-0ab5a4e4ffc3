import 'dart:math' as math;
import '../entities/flashcard.dart';
import '../entities/learning_progress.dart';
import '../repositories/flashcard_repository.dart';

/// Service for implementing spaced repetition algorithm
class SpacedRepetitionService {
  // SuperMemo 2 algorithm constants
  static const double _initialEaseFactor = 2.5;
  static const double _minEaseFactor = 1.3;

  /// Applies spaced repetition scheduling to a learning plan
  Future<LearningPlan> applySpacedRepetition(LearningPlan plan) async {
    // Calculate optimal review intervals for milestones
    final updatedMilestones = <LearningMilestone>[];

    for (int i = 0; i < plan.milestones.length; i++) {
      final milestone = plan.milestones[i];
      final reviewInterval = _calculateMilestoneInterval(
        i,
        plan.milestones.length,
      );

      final updatedMilestone = milestone.copyWith(
        targetDate: plan.startDate.add(reviewInterval),
        metadata: {
          ...milestone.metadata,
          'reviewInterval': reviewInterval.inDays,
          'spacedRepetition': true,
        },
      );

      updatedMilestones.add(updatedMilestone);
    }

    return plan.copyWith(
      milestones: updatedMilestones,
      lastUpdated: DateTime.now(),
    );
  }

  /// Calculates the next review schedule for a flashcard based on user response
  FlashcardSchedule calculateNextReview({
    required FlashcardResponse response,
    required int currentInterval,
    required double easeFactor,
    required int reviewCount,
  }) {
    double newEaseFactor = easeFactor;
    int newInterval;

    switch (response) {
      case FlashcardResponse.hard:
        // Reduce ease factor and reset interval
        newEaseFactor = math.max(_minEaseFactor, easeFactor - 0.2);
        newInterval = 1;
        break;

      case FlashcardResponse.good:
        // Standard progression
        if (reviewCount == 0) {
          newInterval = 1;
        } else if (reviewCount == 1) {
          newInterval = 6;
        } else {
          newInterval = (currentInterval * easeFactor).round();
        }
        break;

      case FlashcardResponse.easy:
        // Increase ease factor and accelerate interval
        newEaseFactor = math.min(4.0, easeFactor + 0.1); // Cap at 4.0
        if (reviewCount == 0) {
          newInterval = 4;
        } else {
          newInterval = (currentInterval * easeFactor * 1.3).round();
        }
        break;
    }

    return FlashcardSchedule(
      nextReview: DateTime.now().add(Duration(days: newInterval)),
      interval: newInterval,
      easeFactor: newEaseFactor,
      reviewCount: reviewCount + 1,
    );
  }

  /// Gets the optimal review time for a flashcard
  DateTime getOptimalReviewTime(Flashcard flashcard) {
    // Consider user's typical study times and circadian rhythms
    final baseTime = flashcard.nextReview;

    // Adjust for optimal learning times (morning hours)
    final optimalHour = 9; // 9 AM
    final adjustedTime = DateTime(
      baseTime.year,
      baseTime.month,
      baseTime.day,
      optimalHour,
    );

    return adjustedTime;
  }

  /// Calculates the forgetting curve for a concept
  double calculateForgettingCurve({
    required DateTime lastReview,
    required double easeFactor,
    required int reviewCount,
  }) {
    final daysSinceReview = DateTime.now().difference(lastReview).inDays;

    // Simplified forgetting curve: R = e^(-t/S)
    // Where R is retention, t is time, S is strength
    final strength = easeFactor * math.log(reviewCount + 1);
    final retention = math.exp(-daysSinceReview / strength);

    return retention.clamp(0.0, 1.0);
  }

  /// Prioritizes flashcards for review based on urgency and importance
  List<Flashcard> prioritizeFlashcards(List<Flashcard> flashcards) {
    final now = DateTime.now();

    // Calculate priority scores for each flashcard
    final prioritizedCards = flashcards.map((card) {
      final daysPastDue = now.difference(card.nextReview).inDays;
      final forgettingRate = calculateForgettingCurve(
        lastReview: card.lastReviewed,
        easeFactor: card.easeFactor,
        reviewCount: card.reviewCount,
      );

      // Priority = days past due + (1 - forgetting rate) * 10
      final priority = daysPastDue + (1 - forgettingRate) * 10;

      return _FlashcardWithPriority(card, priority);
    }).toList();

    // Sort by priority (highest first)
    prioritizedCards.sort((a, b) => b.priority.compareTo(a.priority));

    return prioritizedCards.map((item) => item.flashcard).toList();
  }

  /// Estimates study time needed for a set of flashcards
  Duration estimateStudyTime(List<Flashcard> flashcards) {
    // Base time per flashcard: 30 seconds for new, 15 seconds for review
    int totalSeconds = 0;

    for (final card in flashcards) {
      if (card.isNew) {
        totalSeconds += 30; // 30 seconds for new cards
      } else {
        // Adjust based on difficulty and ease factor
        final baseTime = 15;
        final difficultyMultiplier = _getDifficultyMultiplier(card.difficulty);
        final easeMultiplier = 3.0 - card.easeFactor; // Lower ease = more time

        totalSeconds += (baseTime * difficultyMultiplier * easeMultiplier)
            .round();
      }
    }

    return Duration(seconds: totalSeconds);
  }

  /// Calculates interval for learning plan milestones
  Duration _calculateMilestoneInterval(
    int milestoneIndex,
    int totalMilestones,
  ) {
    // Distribute milestones using spaced intervals
    final baseInterval = 7; // 1 week base
    final spacingFactor = math.pow(1.5, milestoneIndex); // Exponential spacing

    final days = (baseInterval * spacingFactor).round();
    return Duration(days: days);
  }

  /// Gets difficulty multiplier for time estimation
  double _getDifficultyMultiplier(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return 0.8;
      case DifficultyLevel.medium:
        return 1.0;
      case DifficultyLevel.hard:
        return 1.3;
      case DifficultyLevel.expert:
        return 1.6;
    }
  }

  /// Analyzes review patterns to optimize scheduling
  Map<String, dynamic> analyzeReviewPatterns(List<FlashcardReview> reviews) {
    if (reviews.isEmpty) {
      return {
        'averageAccuracy': 0.0,
        'optimalInterval': 1,
        'recommendedEaseFactor': _initialEaseFactor,
      };
    }

    // Calculate average accuracy
    final correctReviews = reviews
        .where((r) => r.response != FlashcardResponse.hard)
        .length;
    final averageAccuracy = correctReviews / reviews.length;

    // Find optimal interval based on performance
    final intervalPerformance = <int, List<FlashcardResponse>>{};
    for (final review in reviews) {
      intervalPerformance.putIfAbsent(review.intervalBefore, () => []);
      intervalPerformance[review.intervalBefore]!.add(review.response);
    }

    int optimalInterval = 1;
    double bestPerformance = 0.0;

    for (final entry in intervalPerformance.entries) {
      final interval = entry.key;
      final responses = entry.value;
      final performance =
          responses.where((r) => r != FlashcardResponse.hard).length /
          responses.length;

      if (performance > bestPerformance) {
        bestPerformance = performance;
        optimalInterval = interval;
      }
    }

    // Recommend ease factor adjustment
    double recommendedEaseFactor = _initialEaseFactor;
    if (averageAccuracy > 0.8) {
      recommendedEaseFactor = _initialEaseFactor + 0.2;
    } else if (averageAccuracy < 0.6) {
      recommendedEaseFactor = math.max(
        _minEaseFactor,
        _initialEaseFactor - 0.2,
      );
    }

    return {
      'averageAccuracy': averageAccuracy,
      'optimalInterval': optimalInterval,
      'recommendedEaseFactor': recommendedEaseFactor,
      'totalReviews': reviews.length,
      'bestPerformanceInterval': optimalInterval,
    };
  }
}

/// Represents a flashcard review schedule
class FlashcardSchedule {
  final DateTime nextReview;
  final int interval;
  final double easeFactor;
  final int reviewCount;

  FlashcardSchedule({
    required this.nextReview,
    required this.interval,
    required this.easeFactor,
    required this.reviewCount,
  });

  @override
  String toString() {
    return 'FlashcardSchedule(nextReview: $nextReview, interval: $interval days, easeFactor: $easeFactor)';
  }
}

/// Helper class for flashcard prioritization
class _FlashcardWithPriority {
  final Flashcard flashcard;
  final double priority;

  _FlashcardWithPriority(this.flashcard, this.priority);
}
