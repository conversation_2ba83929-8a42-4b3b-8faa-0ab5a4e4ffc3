import 'package:dartz/dartz.dart';
import 'dart:developer' as developer;

import '../entities/learning_session.dart';
import '../../data/repositories/learning_session_repository.dart';
import '../../../../core/error/failures.dart';

/// Service for tracking user learning activities and generating activity data
/// Replaces mock activity data with real user session tracking
class ActivityTrackingService {
  final LearningSessionRepository _sessionRepository;

  ActivityTrackingService({
    required LearningSessionRepository sessionRepository,
  }) : _sessionRepository = sessionRepository;

  /// Track a quiz completion activity
  Future<Either<Failure, void>> trackQuizCompletion({
    required String subject,
    required String quizId,
    required int score,
    required int totalQuestions,
    required Duration timeTaken,
  }) async {
    try {
      final sessionResult = await _sessionRepository.startSession(
        subject: subject,
        topic: 'Quiz',
        activityType: 'quiz_completion',
        metadata: {
          'quizId': quizId,
          'score': score,
          'totalQuestions': totalQuestions,
          'timeTaken': timeTaken.inSeconds,
          'accuracy': (score / totalQuestions * 100).round(),
        },
      );

      if (sessionResult.isLeft()) {
        return Left(sessionResult.fold((l) => l, (r) => throw Exception()));
      }

      final session = sessionResult.fold((l) => throw Exception(), (r) => r);

      // End the session immediately for quiz completion
      await _sessionRepository.endSession(
        sessionId: session.id,
        additionalMetadata: {
          'completedAt': DateTime.now().toIso8601String(),
          'activityType': 'quiz_completion',
        },
      );

      developer.log(
        'Quiz completion tracked: $quizId, Score: $score/$totalQuestions',
        name: 'ActivityTrackingService',
      );

      return const Right(null);
    } catch (e) {
      developer.log(
        'Failed to track quiz completion: $e',
        name: 'ActivityTrackingService',
        error: e,
      );
      return Left(ServerFailure('Failed to track quiz completion: $e'));
    }
  }

  /// Track flashcard review activity
  Future<Either<Failure, void>> trackFlashcardReview({
    required String subject,
    required String topic,
    required int cardsReviewed,
    required Duration sessionDuration,
    required Map<String, int> responseBreakdown,
  }) async {
    try {
      final sessionResult = await _sessionRepository.startSession(
        subject: subject,
        topic: topic,
        activityType: 'flashcard_review',
        metadata: {
          'cardsReviewed': cardsReviewed,
          'sessionDuration': sessionDuration.inSeconds,
          'responseBreakdown': responseBreakdown,
          'averageTimePerCard': cardsReviewed > 0
              ? sessionDuration.inSeconds / cardsReviewed
              : 0,
        },
      );

      if (sessionResult.isLeft()) {
        return Left(sessionResult.fold((l) => l, (r) => throw Exception()));
      }

      final session = sessionResult.fold((l) => throw Exception(), (r) => r);

      // End the session
      await _sessionRepository.endSession(
        sessionId: session.id,
        additionalMetadata: {
          'completedAt': DateTime.now().toIso8601String(),
          'activityType': 'flashcard_review',
        },
      );

      developer.log(
        'Flashcard review tracked: $cardsReviewed cards in ${sessionDuration.inMinutes} minutes',
        name: 'ActivityTrackingService',
      );

      return const Right(null);
    } catch (e) {
      developer.log(
        'Failed to track flashcard review: $e',
        name: 'ActivityTrackingService',
        error: e,
      );
      return Left(ServerFailure('Failed to track flashcard review: $e'));
    }
  }

  /// Track study session activity
  Future<Either<Failure, String>> startStudySession({
    required String subject,
    required String topic,
    String activityType = 'study_session',
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final sessionResult = await _sessionRepository.startSession(
        subject: subject,
        topic: topic,
        activityType: activityType,
        metadata: metadata ?? {},
      );

      if (sessionResult.isLeft()) {
        return Left(sessionResult.fold((l) => l, (r) => throw Exception()));
      }

      final session = sessionResult.fold((l) => throw Exception(), (r) => r);

      developer.log(
        'Study session started: ${session.id}',
        name: 'ActivityTrackingService',
      );

      return Right(session.id);
    } catch (e) {
      developer.log(
        'Failed to start study session: $e',
        name: 'ActivityTrackingService',
        error: e,
      );
      return Left(ServerFailure('Failed to start study session: $e'));
    }
  }

  /// End a study session
  Future<Either<Failure, void>> endStudySession({
    required String sessionId,
    Map<String, dynamic>? additionalMetadata,
  }) async {
    try {
      final result = await _sessionRepository.endSession(
        sessionId: sessionId,
        additionalMetadata: additionalMetadata,
      );

      if (result.isLeft()) {
        return Left(result.fold((l) => l, (r) => throw Exception()));
      }

      developer.log(
        'Study session ended: $sessionId',
        name: 'ActivityTrackingService',
      );

      return const Right(null);
    } catch (e) {
      developer.log(
        'Failed to end study session: $e',
        name: 'ActivityTrackingService',
        error: e,
      );
      return Left(ServerFailure('Failed to end study session: $e'));
    }
  }

  /// Get recent activities for display in analytics
  Future<Either<Failure, List<Map<String, dynamic>>>> getRecentActivities({
    String? userId,
    int limit = 10,
  }) async {
    try {
      final sessionsResult = await _sessionRepository.getRecentSessions(
        userId: userId,
        limit: limit,
        since: DateTime.now().subtract(const Duration(days: 7)),
      );

      if (sessionsResult.isLeft()) {
        return Left(sessionsResult.fold((l) => l, (r) => throw Exception()));
      }

      final sessions = sessionsResult.fold(
        (l) => <LearningSession>[],
        (r) => r,
      );

      final activities = sessions.map((session) {
        final activityType =
            session.metadata['activityType'] as String? ?? 'study_session';
        return {
          'id': session.id,
          'title': _generateActivityTitle(session),
          'timeAgo': _formatTimeAgo(session.startTime),
          'icon': _getActivityIcon(activityType),
          'color': _getActivityColor(activityType),
          'timestamp': session.startTime,
          'subject': session.subject,
          'topic': session.topic,
          'activityType': activityType,
          'duration': session.duration?.inMinutes,
          'metadata': session.metadata,
        };
      }).toList();

      return Right(activities);
    } catch (e) {
      developer.log(
        'Failed to get recent activities: $e',
        name: 'ActivityTrackingService',
        error: e,
      );
      return Left(ServerFailure('Failed to get recent activities: $e'));
    }
  }

  /// Generate a user-friendly activity title
  String _generateActivityTitle(LearningSession session) {
    final activityType =
        session.metadata['activityType'] as String? ?? 'study_session';

    switch (activityType) {
      case 'quiz_completion':
        final score = session.metadata['score'] as int?;
        final totalQuestions = session.metadata['totalQuestions'] as int?;
        if (score != null && totalQuestions != null) {
          return 'Completed ${session.subject} Quiz ($score/$totalQuestions)';
        }
        return 'Completed ${session.subject} Quiz';

      case 'flashcard_review':
        final cardsReviewed = session.metadata['cardsReviewed'] as int?;
        if (cardsReviewed != null) {
          return 'Reviewed $cardsReviewed ${session.subject} Flashcards';
        }
        return 'Reviewed ${session.subject} Flashcards';

      case 'study_session':
        return 'Study Session - ${session.subject}';

      case 'learning_plan_creation':
        return 'Created Learning Plan for ${session.subject}';

      default:
        return '${session.subject} - $activityType';
    }
  }

  /// Get appropriate icon for activity type
  String _getActivityIcon(String activityType) {
    switch (activityType) {
      case 'quiz_completion':
        return 'quiz';
      case 'flashcard_review':
        return 'style';
      case 'study_session':
        return 'school';
      case 'learning_plan_creation':
        return 'map';
      default:
        return 'book';
    }
  }

  /// Get appropriate color for activity type
  String _getActivityColor(String activityType) {
    switch (activityType) {
      case 'quiz_completion':
        return 'green';
      case 'flashcard_review':
        return 'blue';
      case 'study_session':
        return 'purple';
      case 'learning_plan_creation':
        return 'orange';
      default:
        return 'grey';
    }
  }

  /// Format timestamp into human-readable "time ago" string
  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${difference.inDays ~/ 7} weeks ago';
    }
  }
}
