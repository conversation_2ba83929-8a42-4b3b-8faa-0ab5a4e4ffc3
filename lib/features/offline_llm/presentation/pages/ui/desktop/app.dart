import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/app_preferences.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/desktop_navigator.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/static/themes.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/desktop/pages/home_page.dart';
import 'package:diogeneschatbot/services/service_locator.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../../../../generated/l10n/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

/// The [DesktopApp] class represents the main application widget for the desktop platforms.
/// It is a stateless widget that builds the user interface based on the consumed [AppPreferences].
class DesktopApp extends StatelessWidget {
  const DesktopApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Selector<AppPreferences, ThemeMode>(
        selector: (context, appPreferences) => appPreferences.themeMode,
        builder: appBuilder);
  }

  /// Builds the root widget for the Maid desktop app.
  ///
  /// This function takes in the [context], [appPreferences], and [child] parameters
  /// and returns a [MaterialApp] widget that serves as the root of the app.
  /// The [MaterialApp] widget is configured with various properties such as the app title,
  /// theme, initial route, and route mappings.
  /// The [home] property is set to [DesktopHomePage], which serves as the default landing page.
  Widget appBuilder(BuildContext context, ThemeMode themeMode, Widget? child) {
    final localeNotifier = getIt<ValueNotifier<Locale>>();
    return ValueListenableBuilder<Locale>(
        valueListenable: localeNotifier,
        builder: (context, locale, child) {
          return MaterialApp(
              debugShowCheckedModeBanner: false,
              title: 'Diogenes AI Chatbot',
              // theme: Themes.lightTheme(),
              // darkTheme: Themes.darkTheme(),
              themeMode: themeMode,
              initialRoute: '/',
              routes: {},
              locale: locale,
              localizationsDelegates: const [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: AppLocalizations.supportedLocales,
              home: ChangeNotifierProvider(
                  create: (context) => DesktopNavigator(),
                  child: const DesktopHomePage()));
        });
  }
}
