import 'dart:async';
import 'dart:convert';

import 'package:diogeneschatbot/models/bot.dart';
import 'package:diogeneschatbot/models/file_system_item.dart' as file_system_item;
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/services/apiusage_service.dart';
import 'package:diogeneschatbot/services/billing_service.dart';
import 'package:diogeneschatbot/util/functions.dart';
import 'package:diogeneschatbot/util/util.dart';
import 'package:diogeneschatbot/util/util_token.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_socket_channel/status.dart' as status;
import 'package:web_socket_channel/web_socket_channel.dart';

class ChatBotController {
  final String? serverHost;

  ApiUsageService _apiUsageService = ApiUsageService();
  StreamSubscription? subscription;
  StreamController<String> streamController;
  void Function(bool value) setLoading;
  FirebaseAuth auth = FirebaseAuth.instance;

  ChatBotController({this.serverHost, this.subscription, required this.streamController, required this.setLoading});

  Future<String> sendRequestToServer(String text, Bot selectedBot, String? userId, String? conversationId) async {
    if (serverHost == null || serverHost!.isEmpty) {
      logger.d("serverHost is empty or null!");
      return '';
    }

    final String aaskUrl = "${Functions.getWebSocketUri("$serverHost")}/aask";
    FirebaseAuth auth = FirebaseAuth.instance;
    final Map<String, dynamic> requestData = {
      'firebase_urls': selectedBot.knowledge
          .whereType<file_system_item.File>()
          .map((fileItem) => (fileItem).firebaseUrl)
          .toList(),
      'query': text,
      'token': await auth.currentUser!.getIdToken(),
      'bot_id': selectedBot.id,
      'user_id': userId!,
      'conversation_id': conversationId!,
    };

    String responseTotal = '';
    final String queryParams = Uri(
      queryParameters: {
        'firebase_urls': json.encode(requestData['firebase_urls']),
        'query': requestData['query'],
        'token': requestData['token'],
        'bot_id': requestData['bot_id'],
        'user_id': requestData['user_id'],
        'conversation_id': requestData['conversation_id'],
      },
    ).query;

    late WebSocketChannel channel;

    // Retrying the connection in case of connection failureAdding more detailed logging to understand the error
    // logger.d("Attempting to connect to WebSocket: $aaskUrl?$queryParams");

    try {
      channel = WebSocketChannel.connect(Uri.parse('$aaskUrl?$queryParams'));
    } catch (e) {
      logger.e("WebSocket connection failed: $e");
      setLoading(false);
      return "Error connecting to WebSocket";
    }

    // Sending the request data after connection is established
    try {
      // logger.d("Sending data: ${json.encode(requestData)}");
      channel.sink.add(json.encode(requestData)); // Sending the data
    } catch (e) {
      logger.e("Failed to send data over WebSocket: $e");
      channel.sink.close(status.goingAway);
      setLoading(false);
      return "Error sending data over WebSocket";
    }

    subscription = channel.stream.listen(
      (chunkAsString) {
        if (chunkAsString != null) {
          try {
            // Parse the JSON string into a Map
            final Map<String, dynamic> parsedChunk = json.decode(chunkAsString);

            // Extract the token part from the data field
            final String? token = parsedChunk['data'];

            if (token != null) {
              responseTotal += token;
              streamController.add(Functions.filterString(responseTotal));

              // Check for end of agent message
              if (token.contains('<END_OF_AGENT>') || responseTotal.contains('<END_OF_AGENT>')) {
                setLoading(false);

                Future.wait([
                      _apiUsageService.addApiUsage(
                        id: "response_id",
                        url: "openaiApiEndpoint",
                        method: "POST",
                        usageType: UsageType.myBots.toString(),
                        requestToken: Util_Token.countTokens(text.trim()),
                        responseToken: Util_Token.countTokens(responseTotal.trim()),
                        userUUID: Util.getUserID(),
                        date: DateTime.now().toUtc(),
                        question: text,
                        answer: responseTotal,
                        conversationId: conversationId,
                      ),
                    ])
                    .then((_) async {
                      logger.d("Insert successful!");

                      // Deduct tokens after successful API call
                      try {
                        final billing = BillingService();
                        final totalTokens =
                            Util_Token.countTokens(text.trim()) + Util_Token.countTokens(responseTotal.trim());
                        await billing.deductTokens(
                          endpoint: 'chat',
                          actualTokens: totalTokens,
                          requestId: "response_id",
                        );
                        logger.d('Deducted $totalTokens tokens for bot chat usage');
                      } catch (e) {
                        logger.w('Failed to deduct tokens: $e');
                        // Don't fail the main flow, but log for monitoring
                      }

                      // reset responseTotal
                      responseTotal = "";
                    })
                    .catchError((error) {
                      logger.d("Insert failed: $error");
                    });

                channel.sink.close(status.goingAway);
              }
            } else {
              logger.e("No token found in the received chunk.");
            }
          } catch (e) {
            logger.e("Failed to parse the received chunk: $e");
          }
        }
      },
      onError: (error) {
        logger.e("Error event received: ${error.toString()}");
        setLoading(false);
        channel.sink.close(status.goingAway);
      },
    );

    return responseTotal;
  }

  Future<String?> chatWithAgentStreaming({
    required String botId,
    required String userId,
    required String conversationId,
    required String userInput,
  }) async {
    final startTime = DateTime.now().toUtc();
    logger.d('chatWithAgent request started at $startTime');
    final String achatUrl = "${Functions.getWebSocketUri('$serverHost')}/achat";

    final Map<String, dynamic> requestData = {
      'bot_id': botId,
      'user_id': userId,
      'conversation_id': conversationId,
      'user_input': userInput,
    };

    String responseTotal = '';
    final String queryParams = Uri(
      queryParameters: {
        'user_input': requestData['user_input'],
        'bot_id': requestData['bot_id'],
        'user_id': requestData['user_id'],
        'conversation_id': requestData['conversation_id'],
        'token': await auth.currentUser!.getIdToken(),
      },
    ).query;

    if (subscription != null) {
      await subscription!.cancel();
      subscription = null;
    }

    late WebSocketChannel channel;

    // Adding more detailed logging to understand the error
    logger.d("Attempting to connect to WebSocket: $achatUrl?$queryParams");

    try {
      channel = WebSocketChannel.connect(Uri.parse('$achatUrl?$queryParams'));
    } catch (e) {
      logger.e("WebSocket connection failed: $e");
      setLoading(false);
      return null;
    }

    // Sending the request data after connection is established
    try {
      // logger.d("Sending data: ${json.encode(requestData)}");
      channel.sink.add(json.encode(requestData)); // Sending the data
    } catch (e) {
      logger.e("Failed to send data over WebSocket: $e");
      channel.sink.close(status.goingAway);
      setLoading(false);
      return "Error sending data over WebSocket";
    }

    subscription = channel.stream.listen(
      (chunkAsString) {
        if (chunkAsString != null) {
          // Parse the JSON string into a Map
          final Map<String, dynamic> parsedChunk = json.decode(chunkAsString);

          // Extract the token part from the data field
          chunkAsString = parsedChunk['data'];
          responseTotal += chunkAsString;
          streamController.add(Functions.filterString(responseTotal));
          if (chunkAsString.contains('<END_OF_TURN>') ||
              responseTotal.contains('<END_OF_TURN>') ||
              chunkAsString.contains('<END_OF_AGENT>') ||
              responseTotal.contains('<END_OF_AGENT>')) {
            setLoading(false);
            channel.sink.close(status.goingAway);

            Future.wait([
                  _apiUsageService.addApiUsage(
                    id: "response_id",
                    url: "openaiApiEndpoint",
                    method: "POST",
                    usageType: UsageType.myBots.toString(),
                    requestToken: Util_Token.countTokens(userInput.trim()),
                    responseToken: Util_Token.countTokens(responseTotal.trim()),
                    userUUID: Util.getUserID(),
                    date: DateTime.now().toUtc(),
                    question: userInput,
                    answer: responseTotal,
                    conversationId: conversationId,
                  ),
                ])
                .then((_) {
                  logger.d("Insert successful!");
                  // reset responseTotal
                  responseTotal = "";
                })
                .catchError((error) {
                  logger.d("Insert failed: $error");
                });
          }
        }
      },
      onError: (error) {
        logger.e("Error event received: ${error.toString()}");
        setLoading(false);
        channel.sink.close(status.goingAway);
      },
    );

    final endTime = DateTime.now().toUtc();
    logger.d('chatWithAgent request ended at $endTime');

    final duration = endTime.difference(startTime);
    logger.d('chatWithAgent request took ${duration.inMilliseconds} ms');

    return responseTotal;
  }
}
