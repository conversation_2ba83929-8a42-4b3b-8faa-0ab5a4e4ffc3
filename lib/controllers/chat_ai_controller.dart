import 'dart:async';
import 'dart:convert';

import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/services/apiusage_service.dart';
import 'package:diogeneschatbot/util/functions.dart';
import 'package:diogeneschatbot/util/util.dart';
import 'package:diogeneschatbot/util/util_api_usage.dart';
import 'package:diogeneschatbot/util/util_token.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:web_socket_channel/status.dart' as status;
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:diogeneschatbot/services/billing_service.dart';

class ChatAIController {
  final String? serverHost =
      dotenv.env['EMBEDDING_SERVER_HOST'] ?? const String.fromEnvironment("EMBEDDING_SERVER_HOST");
  final List<Map<String, dynamic>> _chatMessages;
  final String? currentUserId;
  final Function setLoading;
  final Usage usage;
  void Function(VoidCallback fn) setState;
  late WebSocketChannel channel;

  StreamController<String> answerStreamController;

  String responseMessage = "";

  ChatAIController(
    this._chatMessages,
    this.currentUserId,
    this.setLoading,
    this.answerStreamController,
    this.usage,
    this.setState,
  );

  Future<void> handleEventData(
    String? eventData,
    String responseId,
    String openaiApiEndpoint,
    String questionContent,
    String userMessage,
    String eventName,
    List<String> imageUrls, // Add this
    List<String> audioUrls, // Add this
    List<String> textUrls, // Add this
    List<String> videoUrls, // Add this
  ) async {
    final ApiUsageService apiUsageService = ApiUsageService();
    int inputTokens = 0;
    int outputTokens = 0;
    int totalTokens = 0;
    if (eventName == "usage") {
      logger.i("usage: ${eventData}");
      Map<String, dynamic> usage = jsonDecode(eventData!);
      inputTokens = usage["input_tokens"];
      outputTokens = usage["output_tokens"];
      totalTokens = usage["total_tokens"];
    }

    if (eventData == '<END_OF_RESPONSE>' || eventData == '<END_OF_AGENT>') {
      responseMessage = Functions.removeEndString(responseMessage);
      responseMessage = Functions.findLastFinalAnswer(responseMessage);
      setState(() {
        answerStreamController.add(Functions.filterString(responseMessage));
        _chatMessages.last['answer'] = responseMessage;
      });

      setLoading(false);
      Future.wait([
            apiUsageService.addApiUsage(
              id: responseId,
              url: openaiApiEndpoint,
              method: "POST",
              usageType: usage.type.toString(),
              requestToken: Util_Token.countTokens(questionContent.trim()),
              responseToken: Util_Token.countTokens(responseMessage.trim()),
              userUUID: Util.getUserID(),
              date: DateTime.now().toUtc(),
              question: userMessage,
              answer: responseMessage,
              conversationId: Util.getUserID(),
              imageUrls: imageUrls, // Pass image URLs
              audioUrls: audioUrls, // Pass audio URLs
              textUrls: textUrls, // Pass text URLs
              videoUrls: videoUrls, // Pass video URLs
            ),
          ])
          .then((_) async {
            logger.d("Insert successful!");

            // Deduct tokens after successful API call
            try {
              final billing = BillingService();
              final totalTokens =
                  Util_Token.countTokens(questionContent.trim()) + Util_Token.countTokens(responseMessage.trim());
              await billing.deductTokens(endpoint: 'chat', actualTokens: totalTokens, requestId: responseId);
              logger.d('Deducted $totalTokens tokens for chat usage');
            } catch (e) {
              logger.w('Failed to deduct tokens: $e');
              // Don't fail the main flow, but log for monitoring
            }

            //  reset responseMessage
            responseMessage = "";
          })
          .catchError((error) {
            logger.d("Insert failed: $error");
          });

      channel?.sink.close(status.goingAway);
    } else {
      responseMessage += eventData!;
      setState(() {
        answerStreamController.add(Functions.filterString(responseMessage));
      });
    }
  }

  Future<void> fetchRealTimeUpdates(
    BuildContext context,
    String userMessage,
    Usage usage,
    List<String> downloadImageUrls, // Accept imageUrls list
    List<String> imageUrls, // Accept imageUrls list
    List<String> audioUrls, // Accept audioUrls list
    List<String> textUrls, // Accept textUrls list
    List<String> videoUrls, // Accept videoUrls list
  ) async {
    setLoading(true);

    String questionContent = userMessage;
    List<Map<String, String>> histories = Util.getHistoryMessages(_chatMessages, 5);
    histories.addAll([
      {"role": "system", "content": usage.systemPromptMessage!},
      {"role": "user", "content": userMessage},
    ]);
    final jsonbody = {
      "model": usage.model,
      "max_tokens": usage.maxTokens,
      "messages": histories,
      "temperature": usage.temperature,
      "stream": true,
      "usage_type": usage.type.name,
      'image_urls': imageUrls,
      'audio_urls': audioUrls,
      'text_urls': textUrls,
      'video_urls': videoUrls,
    };
    final body = json.encode(jsonbody);

    if (serverHost == null || serverHost!.isEmpty) {
      logger.d("serverHost is empty or null!");
      return;
    }

    final String askOpenaiurl = "${Functions.getWebSocketUri("$serverHost")}/ask_openai";

    String responseMessage = "";
    // Construct the chat message with the text content
    Map<String, dynamic> chatMessage = {
      "question": userMessage,
      "answer": '',
      "date": DateTime.now().toUtc().toString(),
      "image_urls": downloadImageUrls, // Add the image URLs
      "audio_urls": audioUrls, // Add the audio URLs
      "text_urls": textUrls, // Add the text URLs
      "video_urls": videoUrls, // Add the video URLs
    };
    setState(() {
      _chatMessages.add(chatMessage);
    });

    // Preflight billing check (server-side enforcement)
    try {
      final billing = BillingService();
      final int estimatedTokens = Util_Token.countTokens(userMessage.trim()) + (usage.maxTokens ?? 0);
      final preflight = await billing.preflightCheck(endpoint: 'chat', estimatedTokens: estimatedTokens);
      if (!preflight.allowed) {
        final reason = preflight.reason ?? "Your current plan/credits do not allow this request.";
        logger.w("Preflight blocked: $reason");
        setLoading(false);
        showDialog(
          context: context,
          barrierDismissible: true,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Usage Limit Reached'),
              content: Text(
                preflight.remainingTokens != null ? "$reason\nRemaining tokens: ${preflight.remainingTokens}" : reason,
              ),
            );
          },
        );
        return;
      }
    } catch (e) {
      // If preflight fails unexpectedly, fall back to local checks
      logger.w("Preflight failed, falling back to local limit check: $e");
    }

    // Local fallback limit check
    if (await Util_API_Usage.isLimitExceeded(usage, currentUserId!)) {
      responseMessage = "Usage limit(daily, monthly, specific API's) exceeded";
      logger.d("Usage limit exceeded");

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(title: Text('Usage Limit Exceeded'), content: Text(responseMessage));
        },
      );

      Future.delayed(Duration(seconds: 3), () async {
        Navigator.of(context).pop();
      });
      return;
    }

    String responseId = "response_id";

    try {
      // Connect to WebSocket
      User? user = FirebaseAuth.instance.currentUser;
      String? idToken = await user?.getIdToken();
      logger.d("Attempting to connect to WebSocket: $askOpenaiurl");
      channel = WebSocketChannel.connect(Uri.parse(askOpenaiurl + "?token=$idToken"));

      // Send the initial data to the server
      logger.d("Sending data: $body");
      channel.sink.add(body);

      // Listen to the incoming stream
      channel.stream.listen(
        (chunkAsString) {
          if (chunkAsString != null) {
            // Parse the JSON string into a Map
            final Map<String, dynamic> parsedChunk = json.decode(chunkAsString);
            if (parsedChunk["event"] == "error") {
              logger.e("Error event received: ${parsedChunk["data"].toString()}");
              setLoading(false);
              channel.sink.close(status.goingAway);
              return;
            } else if (parsedChunk["event"] == "usage") {
              logger.e("Usage event received: ${parsedChunk["data"].toString()}");
            }
            // Extract the token part from the data field
            chunkAsString = parsedChunk['data'];
            handleEventData(
              chunkAsString,
              responseId,
              askOpenaiurl,
              questionContent,
              userMessage,
              parsedChunk['event'],
              downloadImageUrls,
              audioUrls,
              textUrls,
              videoUrls,
            );
          }
        },
        onError: (error) {
          logger.e("Error event received: ${error.toString()}");
          setLoading(false);
          channel.sink.close(status.goingAway);
        },
      );
    } catch (e) {
      logger.e("WebSocket connection failed: $e");
      setLoading(false);
      return;
    }
  }
}
