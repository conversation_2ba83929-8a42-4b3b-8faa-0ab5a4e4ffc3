import 'dart:convert';

import 'package:diogeneschatbot/config/remote_config.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:google_generative_ai/google_generative_ai.dart';

class RemoteConfigService {
  static final remoteConfig = FirebaseRemoteConfig.instance;
  static GenerativeModel? geminiVisionPro;
  static GenerativeModel? geminiPro;

  static Future<void> initialize() async {
    logger.d("Initializing Remote Config...");
    try {
      await remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: const Duration(hours: 1),
      ));
      String encode = json.encode({
        'daily_limit': 200,
        'daily_chat_limit': 40,
        'daily_image_limit': 6,
        'monthly_image_limit': 20,
        'daily_total_token_limit': 10000,
        'monthly_total_token_limit': 30000,
        'default_max_token': 16000,
      });
      String defaultModelsEncode = json.encode({
        'openai': {
          'text': 'gpt-4.1-mini',
          'image_generation': 'dall-e-3',
          'multi_modal': 'gpt-4.1-mini',
          'advanced_reasoning': 'o1-mini',
          'tts': 'tts-1',
          'embedding': 'text-embedding-3-large',
          'vision': 'gpt-4-vision',
          'real_time': 'gpt-4o-audio-preview',
          'top_k': 50,
          'top_p': 1,
          'temperature': 1,
          'max_tokens': 30000
        },
        'gemini': {
          'text': 'gemini-1.5-flash',
          'image_generation': '',
          'multi_modal': 'gemini-1.5-flash',
          'advanced_reasoning': 'gemini-1.5-pro-002',
          'tts': '',
          'embedding': 'text-embedding-004',
          'vision': 'gemini-1.5-pro-002',
          'real_time': '',
          'top_k': 50,
          'top_p': 0.9,
          'temperature': 1,
          'max_tokens': 8000
        }
      });
      await remoteConfig.setDefaults({
        "limits": encode,
        "default_models": defaultModelsEncode,
      });
      await remoteConfig.fetchAndActivate();
      await AppConfig.updateConfigValues();
      logger.d("Remote config fetched and activated.");
    } catch (e) {
      logger.e("Failed to initialize Remote Config: $e");
    }
  }

  static Map<String, dynamic> get limits {
    return json.decode(remoteConfig.getString('limits'));
  }

  static int get max_token {
    return remoteConfig.getInt('max_token');
  }

  static String get default_model {
    return remoteConfig.getString('default_model');
  }

  static Map<String, dynamic> get defaultModels {
    print("📋 Getting default models from remote config...");
    final modelsString = remoteConfig.getString('default_models');
    print("📋 Raw default_models string: $modelsString");
    final decoded = json.decode(modelsString);
    print("✅ Default models decoded successfully");
    return decoded;
  }

  // Accessing specific models or properties
  static Map<String, dynamic> getOpenAIConfig() {
    print("🔧 Getting OpenAI config...");
    final config = defaultModels['openai'];
    print("✅ OpenAI config retrieved: $config");
    return config;
  }

  static Map<String, dynamic> getGeminiConfig() {
    print("🔧 Getting Gemini config...");
    final config = defaultModels['gemini'];
    print("✅ Gemini config retrieved: $config");
    return config;
  }

  static Future<void> initializeModels({required String googleAI_ApiKey}) async {
    print("🤖 Starting Gemini models initialization...");
    try {
      print("📖 Getting Gemini config...");
      final geminiConfig = getGeminiConfig();
      print("✅ Gemini config loaded: $geminiConfig");
      
      print("🔍 Extracting model names...");
      final visionModelName = geminiConfig['vision'];
      final textModelName = geminiConfig['text'];
      print("📝 Vision model: $visionModelName, Text model: $textModelName");
      
      print("⚙️ Creating generation config...");
      final generationConfig = GenerationConfig(
        temperature: geminiConfig['temperature'].toDouble(),
        topK: geminiConfig['top_k'],
        topP: geminiConfig['top_p'].toDouble(),
        maxOutputTokens: geminiConfig['max_tokens'],
      );
      print("✅ Generation config created: temp=${generationConfig.temperature}, topK=${generationConfig.topK}, topP=${generationConfig.topP}, maxTokens=${generationConfig.maxOutputTokens}");
      
      print("🛡️ Setting up safety settings...");
      final safetySettings = [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.high),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.high),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.high),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.high),
      ];
      print("✅ Safety settings configured");

      print("🔑 Checking Google AI API Key...");
      if (googleAI_ApiKey.isEmpty) {
        print("❌ Google AI API Key is not initialized or is empty.");
        logger.e("Google AI API Key is not initialized or is empty.");
        return;
      }
      print("✅ Google AI API Key is available (length: ${googleAI_ApiKey.length})");

      print("🔮 Creating Gemini Vision Pro model...");
      geminiVisionPro = GenerativeModel(
        model: visionModelName,
        apiKey: googleAI_ApiKey,
        generationConfig: generationConfig,
        safetySettings: safetySettings,
      );
      print("✅ Gemini Vision Pro model created");

      print("💬 Creating Gemini Pro model...");
      geminiPro = GenerativeModel(
        model: textModelName,
        apiKey: googleAI_ApiKey,
        generationConfig: generationConfig,
        safetySettings: safetySettings,
      );
      print("✅ Gemini Pro model created");
      
      print("🎉 All Gemini models initialized successfully!");
      logger.d("Gemini models initialized successfully.");
    } catch (e, stackTrace) {
      print("❌ Failed to initialize Gemini models: $e");
      print("Stack trace: $stackTrace");
      logger.e("Failed to initialize Gemini models: $e");
    }
  }

  static int get maxToken {
    return limits['default_max_token'];
  }

  static String get defaultModel {
    return defaultModels['openai']['text']; // Accessing specific model (e.g., text model from OpenAI)
  }

  static int get maxTokensOpenAI {
    return getOpenAIConfig()['max_tokens'];
  }

  static double get temperatureOpenAI {
    return getOpenAIConfig()['temperature'].toDouble();
  }

  static int get topKOpenAI {
    return getOpenAIConfig()['top_k'];
  }

  static double get topPOpenAI {
    return getOpenAIConfig()['top_p'].toDouble();
  }
}