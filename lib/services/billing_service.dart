import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:diogeneschatbot/config/setup_dio.dart';
import 'package:diogeneschatbot/features/payment/data/config.dart';
import 'package:diogeneschatbot/utils/logger.dart';

/// BillingService wraps calls to backend billing endpoints
///
/// Backend endpoints (implemented):
/// - POST /v1/billing/checkout-session  => returns { url }
/// - GET  /v1/billing/portal-link       => returns { url }
/// - POST /v1/billing/preflight         => returns { allowed, reason?, remaining_tokens? }
/// - GET  /v1/billing/credit-packages   => returns { packages: [{ id, label, price_display, tokens }] }
/// - GET  /v1/billing/usage             => returns { remaining_tokens, plan_display, is_subscribed }
/// - POST /v1/billing/sync-subscription => returns { is_subscribed, plan_display }
class BillingService {
  BillingService({Dio? dio}) : _dio = dio ?? setupDio();

  final Dio _dio;

  /// Create a Stripe Checkout Session for credit top-ups.
  /// Returns the hosted checkout URL to open in a browser/webview.
  Future<String> createCreditCheckoutSession({
    required String packageId,
    int quantity = 1,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final resp = await _dio.post(
        '$kApiUrl/v1/billing/checkout-session',
        data: jsonEncode({'package_id': packageId, 'quantity': quantity, if (metadata != null) 'metadata': metadata}),
        options: Options(headers: {'Content-Type': 'application/json'}),
      );
      dynamic data = resp.data;
      if (data is String) data = jsonDecode(data);
      final url = data['url'] as String?;
      if (url == null || url.isEmpty) {
        throw Exception('Missing checkout URL in response');
      }
      logger.i('Created credit checkout session for package=$packageId');
      return url;
    } catch (e, st) {
      logger.e('Failed creating credit checkout session: $e');
      logger.e('$st');
      rethrow;
    }
  }

  /// Retrieve a Stripe Billing Portal link for the current user.
  Future<String> getBillingPortalLink() async {
    try {
      final resp = await _dio.get('$kApiUrl/v1/billing/portal-link');
      dynamic data = resp.data;
      if (data is String) data = jsonDecode(data);
      final url = data['url'] as String?;
      if (url == null || url.isEmpty) {
        throw Exception('Missing portal URL in response');
      }
      logger.i('Fetched billing portal link');
      return url;
    } catch (e, st) {
      logger.e('Failed fetching billing portal link: $e');
      logger.e('$st');
      rethrow;
    }
  }

  /// List available credit packages for top-up.
  Future<List<CreditPackage>> listCreditPackages() async {
    try {
      final resp = await _dio.get('$kApiUrl/v1/billing/credit-packages');
      dynamic data = resp.data;
      if (data is String) data = jsonDecode(data);

      final List<dynamic> rawList;
      if (data is Map<String, dynamic> && data['packages'] is List) {
        rawList = data['packages'] as List;
      } else if (data is List) {
        rawList = data;
      } else {
        throw Exception('Unexpected credit-packages response shape');
      }

      final packages = rawList
          .map((e) {
            if (e is String) {
              // Minimal fallback shape: just an id
              return CreditPackage(id: e, label: e);
            }
            if (e is Map<String, dynamic>) {
              return CreditPackage.fromJson(e);
            }
            throw Exception('Invalid package element');
          })
          .toList(growable: false);

      logger.i('Fetched ${packages.length} credit packages');
      return packages;
    } catch (e, st) {
      logger.e('Failed fetching credit packages: $e');
      logger.e('$st');
      rethrow;
    }
  }

  /// Preflight check before sending a costly API request.
  Future<PreflightResult> preflightCheck({
    required String endpoint,
    required int estimatedTokens,
    String? requestId,
    Map<String, dynamic>? extras,
  }) async {
    try {
      final payload = {
        'endpoint': endpoint,
        'estimated_tokens': estimatedTokens,
        if (requestId != null) 'request_id': requestId,
        if (extras != null) 'extras': extras,
      };
      final resp = await _dio.post(
        '$kApiUrl/v1/billing/preflight',
        data: jsonEncode(payload),
        options: Options(headers: {'Content-Type': 'application/json'}),
      );
      dynamic data = resp.data;
      if (data is String) data = jsonDecode(data);
      return PreflightResult.fromJson(data as Map<String, dynamic>);
    } catch (e, st) {
      logger.e('Preflight check failed: $e');
      logger.e('$st');
      // Fail closed as per design; caller can decide UX.
      return PreflightResult(allowed: false, reason: 'Preflight failed. Please try again later.');
    }
  }

  /// Get usage summary for current user (remaining tokens/plan display/subscription status)
  Future<UsageSummary> getUsageSummary() async {
    try {
      final resp = await _dio.get('$kApiUrl/v1/billing/usage');
      dynamic data = resp.data;
      if (data is String) data = jsonDecode(data);
      return UsageSummary.fromJson(data as Map<String, dynamic>);
    } catch (e, st) {
      logger.e('Failed fetching usage summary: $e');
      logger.e('$st');
      rethrow;
    }
  }

  /// Sync subscription state from Stripe and persist to backend
  Future<SubscriptionState> syncSubscription() async {
    try {
      final resp = await _dio.post('$kApiUrl/v1/billing/sync-subscription');
      dynamic data = resp.data;
      if (data is String) data = jsonDecode(data);
      return SubscriptionState.fromJson(data as Map<String, dynamic>);
    } catch (e, st) {
      logger.e('Failed syncing subscription: $e');
      logger.e('$st');
      rethrow;
    }
  }

  /// Deduct tokens after actual API usage
  Future<TokenDeductionResult> deductTokens({
    required String endpoint,
    required int actualTokens,
    String? requestId,
  }) async {
    try {
      final resp = await _dio.post(
        '$kApiUrl/v1/billing/deduct-tokens',
        data: jsonEncode({
          'endpoint': endpoint,
          'actual_tokens': actualTokens,
          if (requestId != null) 'request_id': requestId,
        }),
        options: Options(headers: {'Content-Type': 'application/json'}),
      );
      dynamic data = resp.data;
      if (data is String) data = jsonDecode(data);
      return TokenDeductionResult.fromJson(data as Map<String, dynamic>);
    } catch (e, st) {
      logger.e('Failed deducting tokens: $e');
      logger.e('$st');
      rethrow;
    }
  }
}

class PreflightResult {
  final bool allowed;
  final String? reason;
  final int? remainingTokens;
  final List<String>? suggestions;

  PreflightResult({required this.allowed, this.reason, this.remainingTokens, this.suggestions});

  factory PreflightResult.fromJson(Map<String, dynamic> json) => PreflightResult(
    allowed: (json['allowed'] as bool?) ?? false,
    reason: json['reason'] as String?,
    remainingTokens: json['remaining_tokens'] as int?,
    suggestions: (json['suggestions'] as List?)?.whereType<String>().toList(growable: false),
  );
}

class CreditPackage {
  final String id;
  final String label;
  final String? priceDisplay;
  final int? tokens;

  CreditPackage({required this.id, required this.label, this.priceDisplay, this.tokens});

  factory CreditPackage.fromJson(Map<String, dynamic> json) => CreditPackage(
    id: json['id'] as String,
    label: (json['label'] as String?) ?? (json['id'] as String),
    priceDisplay: json['price_display'] as String?,
    tokens: (json['tokens'] as num?)?.toInt(),
  );
}

class UsageSummary {
  final int? remainingTokens;
  final String? planDisplay;
  final bool isSubscribed;

  UsageSummary({this.remainingTokens, this.planDisplay, this.isSubscribed = false});

  factory UsageSummary.fromJson(Map<String, dynamic> json) => UsageSummary(
    remainingTokens: (json['remaining_tokens'] as num?)?.toInt(),
    planDisplay: json['plan_display'] as String?,
    isSubscribed: (json['is_subscribed'] as bool?) ?? false,
  );
}

class SubscriptionState {
  final bool isSubscribed;
  final String? planDisplay;

  SubscriptionState({required this.isSubscribed, this.planDisplay});

  factory SubscriptionState.fromJson(Map<String, dynamic> json) => SubscriptionState(
    isSubscribed: (json['is_subscribed'] as bool?) ?? false,
    planDisplay: json['plan_display'] as String?,
  );
}

class TokenDeductionResult {
  final bool success;
  final int tokensDeducted;
  final int remainingTokens;

  TokenDeductionResult({required this.success, required this.tokensDeducted, required this.remainingTokens});

  factory TokenDeductionResult.fromJson(Map<String, dynamic> json) => TokenDeductionResult(
    success: (json['success'] as bool?) ?? false,
    tokensDeducted: (json['tokens_deducted'] as num?)?.toInt() ?? 0,
    remainingTokens: (json['remaining_tokens'] as num?)?.toInt() ?? 0,
  );
}
