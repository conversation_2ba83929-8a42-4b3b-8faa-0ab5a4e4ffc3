# openmeter.model.SubscriptionPhase

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** | A unique identifier for the resource. | 
**name** | **String** | Human-readable name for the resource. Between 1 and 256 characters. | 
**description** | **String** | Optional description of the resource. Maximum 1024 characters. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional metadata for the resource. | [optional] 
**createdAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was created. | 
**updatedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was last updated. | 
**deletedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was permanently deleted. | [optional] 
**key** | **String** | A locally unique identifier for the resource. | 
**discounts** | [**BuiltList&lt;Discount&gt;**](Discount.md) | The discounts on the plan. | [optional] 
**activeFrom** | [**DateTime**](DateTime.md) | The time from which the phase is active. | 
**activeTo** | [**DateTime**](DateTime.md) | The until which the Phase is active. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


