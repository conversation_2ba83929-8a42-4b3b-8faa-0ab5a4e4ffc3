# openmeter.model.PaymentTermDueDate

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **String** | Type of terms to be applied. | 
**detail** | **String** | Text detail of the chosen payment terms. | [optional] 
**notes** | **String** | Description of the conditions for payment. | [optional] 
**dueAt** | [**BuiltList&lt;PaymentDueDate&gt;**](PaymentDueDate.md) | When the payment is due. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


