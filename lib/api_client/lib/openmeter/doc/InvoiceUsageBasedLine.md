# openmeter.model.InvoiceUsageBasedLine

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **String** | Human-readable name for the resource. Between 1 and 256 characters. | 
**description** | **String** | Optional description of the resource. Maximum 1024 characters. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional metadata for the resource. | [optional] 
**createdAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was created. | 
**updatedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was last updated. | 
**deletedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was permanently deleted. | [optional] 
**id** | **String** | ID of the line. | 
**managedBy** | [**InvoiceLineManagedBy**](InvoiceLineManagedBy.md) | managedBy specifies if the line is manually added via the api or managed by OpenMeter. | 
**status** | [**InvoiceLineStatus**](InvoiceLineStatus.md) | Status of the line.  External calls always create valid lines, other line types are managed by the billing engine of OpenMeter. | 
**discounts** | [**BuiltList&lt;InvoiceLineDiscount&gt;**](InvoiceLineDiscount.md) | Discounts detailes applied to this line.  New discounts can be added via the invoice's discounts API, to facilitate discounts that are affecting multiple lines. | [optional] 
**charges** | [**BuiltList&lt;BillingLineCharge&gt;**](BillingLineCharge.md) | Charges applied to this line. (like minimum spend)  New charges can be added via the invoice's charges API, to facilitate charges that are affecting multiple lines. | [optional] 
**invoice** | [**InvoiceReference**](InvoiceReference.md) | The invoice this item belongs to. | [optional] 
**currency** | **String** | The currency of this line. | 
**taxes** | [**BuiltList&lt;InvoiceLineTaxItem&gt;**](InvoiceLineTaxItem.md) | Taxes applied to the invoice totals. | [optional] 
**taxConfig** | [**TaxConfig**](TaxConfig.md) | Tax config specify the tax configuration for this line. | [optional] 
**children** | [**BuiltList&lt;InvoiceLine&gt;**](InvoiceLine.md) | The lines detailing the item or service sold. | [optional] 
**totals** | [**InvoiceTotals**](InvoiceTotals.md) | Totals for this line. | 
**period** | [**Period**](Period.md) | Period of the line item applies to for revenue recognition pruposes.  Billing always treats periods as start being inclusive and end being exclusive. | 
**invoiceAt** | [**DateTime**](DateTime.md) | The time this line item should be invoiced. | 
**externalIds** | [**InvoiceLineAppExternalIds**](InvoiceLineAppExternalIds.md) | External IDs of the invoice in other apps such as Stripe. | [optional] 
**subscriptions** | [**InvoiceLineSubscriptionReference**](InvoiceLineSubscriptionReference.md) | Subscription are the references to the subscritpions that this line is related to. | [optional] 
**type** | **String** | Type of the line. | 
**price** | [**RateCardUsageBasedPrice**](RateCardUsageBasedPrice.md) | Price of the usage-based item being sold. | 
**featureKey** | **String** | The feature that the usage is based on. | 
**quantity** | **String** | The quantity of the item being sold. | [optional] 
**preLinePeriodQuantity** | **String** | The quantity of the item used in before this line's period.  It is non-zero in case of progressive billing, when this shows how much of the usage was already billed. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


