# openmeter.model.Plan

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** | A unique identifier for the resource. | 
**name** | **String** | Human-readable name for the resource. Between 1 and 256 characters. | 
**description** | **String** | Optional description of the resource. Maximum 1024 characters. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional metadata for the resource. | [optional] 
**createdAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was created. | 
**updatedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was last updated. | 
**deletedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was permanently deleted. | [optional] 
**key** | **String** | A semi-unique identifier for the resource. | 
**alignment** | [**Alignment**](Alignment.md) | Alignment configuration for the plan. | [optional] 
**version** | **int** | Version of the plan. Incremented when the plan is updated. | [default to 1]
**currency** | **String** | The currency code of the plan. | [default to USD]
**effectiveFrom** | [**DateTime**](DateTime.md) | The date and time when the plan becomes effective. When not specified, the plan is a draft. | [optional] 
**effectiveTo** | [**DateTime**](DateTime.md) | The date and time when the plan is no longer effective. When not specified, the plan is effective indefinitely. | [optional] 
**status** | [**PlanStatus**](PlanStatus.md) | The status of the plan. Computed based on the effective start and end dates: - draft = no effectiveFrom - active = effectiveFrom <= now < effectiveTo - archived / inactive = effectiveTo <= now - scheduled = now < effectiveFrom < effectiveTo | 
**phases** | [**BuiltList&lt;PlanPhase&gt;**](PlanPhase.md) | The plan phase or pricing ramp allows changing a plan's rate cards over time as a subscription progresses. A phase switch occurs only at the end of a billing period, ensuring that a single subscription invoice will not include charges from different phase prices. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


