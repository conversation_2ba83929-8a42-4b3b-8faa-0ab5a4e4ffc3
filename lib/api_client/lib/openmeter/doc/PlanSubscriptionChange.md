# openmeter.model.PlanSubscriptionChange

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**timing** | [**SubscriptionTiming**](SubscriptionTiming.md) | Timing configuration for the change, when the change should take effect. For changing a subscription, the accepted values depend on the subscription configuration. | 
**alignment** | [**Alignment**](Alignment.md) | What alignment settings the subscription should have. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Arbitrary metadata associated with the subscription. | [optional] 
**plan** | [**PlanReferenceInput**](PlanReferenceInput.md) | The plan reference to change to. | 
**name** | **String** | The name of the Subscription. If not provided the plan name is used. | [optional] 
**description** | **String** | Description for the Subscription. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


