# openmeter.model.MarketplaceAppAPIKeyInstallRequest

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**apiKey** | **String** | The API key for the provider. For example, the Stripe API key. | 
**name** | **String** | Name of the application to install.  If not set defaults to the marketplace item's description. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


