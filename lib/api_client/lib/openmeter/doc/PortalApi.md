# openmeter.api.PortalApi

## Load the API package
```dart
import 'package:openmeter/api.dart';
```

All URIs are relative to *https://127.0.0.1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**createPortalToken**](PortalApi.md#createportaltoken) | **POST** /api/v1/portal/tokens | Create consumer portal token
[**invalidatePortalTokens**](PortalApi.md#invalidateportaltokens) | **POST** /api/v1/portal/tokens/invalidate | Invalidate portal tokens
[**listPortalTokens**](PortalApi.md#listportaltokens) | **GET** /api/v1/portal/tokens | List consumer portal tokens
[**queryPortalMeter**](PortalApi.md#queryportalmeter) | **GET** /api/v1/portal/meters/{meterSlug}/query | Query meter Query meter


# **createPortalToken**
> PortalToken createPortalToken(portalToken)

Create consumer portal token

Create a consumer portal token.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getPortalApi();
final PortalToken portalToken = ; // PortalToken | 

try {
    final response = api.createPortalToken(portalToken);
    print(response);
} catch on DioException (e) {
    print('Exception when calling PortalApi->createPortalToken: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **portalToken** | [**PortalToken**](PortalToken.md)|  | 

### Return type

[**PortalToken**](PortalToken.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **invalidatePortalTokens**
> invalidatePortalTokens(invalidatePortalTokensRequest)

Invalidate portal tokens

Invalidates consumer portal tokens by ID or subject.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getPortalApi();
final InvalidatePortalTokensRequest invalidatePortalTokensRequest = ; // InvalidatePortalTokensRequest | 

try {
    api.invalidatePortalTokens(invalidatePortalTokensRequest);
} catch on DioException (e) {
    print('Exception when calling PortalApi->invalidatePortalTokens: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **invalidatePortalTokensRequest** | [**InvalidatePortalTokensRequest**](InvalidatePortalTokensRequest.md)|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **listPortalTokens**
> BuiltList<PortalToken> listPortalTokens(limit)

List consumer portal tokens

List tokens.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getPortalApi();
final int limit = 56; // int | 

try {
    final response = api.listPortalTokens(limit);
    print(response);
} catch on DioException (e) {
    print('Exception when calling PortalApi->listPortalTokens: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**|  | [optional] [default to 25]

### Return type

[**BuiltList&lt;PortalToken&gt;**](PortalToken.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **queryPortalMeter**
> MeterQueryResult queryPortalMeter(meterSlug, clientId, from, to, windowSize, windowTimeZone, filterGroupBy, groupBy)

Query meter Query meter

Query meter for consumer portal. This endpoint is publicly exposable to consumers. Query meter for consumer portal. This endpoint is publicly exposable to consumers.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getPortalApi();
final String meterSlug = meterSlug_example; // String | 
final String clientId = clientId_example; // String | Client ID Useful to track progress of a query.
final DateTime from = 2023-01-01T01:01:01.001Z; // DateTime | Start date-time in RFC 3339 format.  Inclusive.
final DateTime to = 2023-01-01T01:01:01.001Z; // DateTime | End date-time in RFC 3339 format.  Inclusive.
final WindowSize windowSize = ; // WindowSize | If not specified, a single usage aggregate will be returned for the entirety of the specified period for each subject and group.
final String windowTimeZone = windowTimeZone_example; // String | The value is the name of the time zone as defined in the IANA Time Zone Database (http://www.iana.org/time-zones). If not specified, the UTC timezone will be used.
final BuiltMap<String, String> filterGroupBy = ; // BuiltMap<String, String> | Simple filter for group bys with exact match.
final BuiltList<String> groupBy = ; // BuiltList<String> | If not specified a single aggregate will be returned for each subject and time window. `subject` is a reserved group by value.

try {
    final response = api.queryPortalMeter(meterSlug, clientId, from, to, windowSize, windowTimeZone, filterGroupBy, groupBy);
    print(response);
} catch on DioException (e) {
    print('Exception when calling PortalApi->queryPortalMeter: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **meterSlug** | **String**|  | 
 **clientId** | **String**| Client ID Useful to track progress of a query. | [optional] 
 **from** | **DateTime**| Start date-time in RFC 3339 format.  Inclusive. | [optional] 
 **to** | **DateTime**| End date-time in RFC 3339 format.  Inclusive. | [optional] 
 **windowSize** | [**WindowSize**](.md)| If not specified, a single usage aggregate will be returned for the entirety of the specified period for each subject and group. | [optional] 
 **windowTimeZone** | **String**| The value is the name of the time zone as defined in the IANA Time Zone Database (http://www.iana.org/time-zones). If not specified, the UTC timezone will be used. | [optional] [default to 'UTC']
 **filterGroupBy** | [**BuiltMap&lt;String, String&gt;**](String.md)| Simple filter for group bys with exact match. | [optional] 
 **groupBy** | [**BuiltList&lt;String&gt;**](String.md)| If not specified a single aggregate will be returned for each subject and time window. `subject` is a reserved group by value. | [optional] 

### Return type

[**MeterQueryResult**](MeterQueryResult.md)

### Authorization

[PortalTokenAuth](../README.md#PortalTokenAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, text/csv, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

