# openmeter.model.SpendCommitments

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**minimumAmount** | **String** | The customer is committed to spend at least the amount. | [optional] 
**maximumAmount** | **String** | The customer is limited to spend at most the amount. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


