# openmeter.model.Subscription

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** | A unique identifier for the resource. | 
**name** | **String** | Human-readable name for the resource. Between 1 and 256 characters. | 
**description** | **String** | Optional description of the resource. Maximum 1024 characters. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional metadata for the resource. | [optional] 
**createdAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was created. | 
**updatedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was last updated. | 
**deletedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was permanently deleted. | [optional] 
**activeFrom** | [**DateTime**](DateTime.md) | The cadence start of the resource. | 
**activeTo** | [**DateTime**](DateTime.md) | The cadence end of the resource. | [optional] 
**alignment** | [**Alignment**](Alignment.md) | Alignment configuration for the plan. | [optional] 
**status** | [**SubscriptionStatus**](SubscriptionStatus.md) | The status of the subscription. | 
**customerId** | **String** | The customer ID of the subscription. | 
**plan** | [**PlanReference**](PlanReference.md) | The plan of the subscription. | [optional] 
**currency** | **String** | The currency code of the subscription. Will be revised once we add multi currency support. | [default to USD]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


