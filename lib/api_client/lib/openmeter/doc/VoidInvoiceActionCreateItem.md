# openmeter.model.VoidInvoiceActionCreateItem

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**percentage** | **double** | How much of the total line items to be voided? (e.g. 100% means all charges are voided) | 
**action** | [**VoidInvoiceLineActionCreateItem**](VoidInvoiceLineActionCreateItem.md) | The action to take on the line items. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


