# openmeter.model.SubscriptionEdit

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**customizations** | [**BuiltList&lt;SubscriptionEditOperation&gt;**](SubscriptionEditOperation.md) | Batch processing commands for manipulating running subscriptions. The key format is `/phases/{phaseKey}` or `/phases/{phaseKey}/items/{itemKey}`. | 
**timing** | [**SubscriptionTiming**](SubscriptionTiming.md) | Whether the billing period should be restarted.Timing configuration to allow for the changes to take effect at different times. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


