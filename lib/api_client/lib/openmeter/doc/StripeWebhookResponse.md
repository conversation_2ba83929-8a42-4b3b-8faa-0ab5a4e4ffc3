# openmeter.model.StripeWebhookResponse

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**namespaceId** | **String** | ULID (Universally Unique Lexicographically Sortable Identifier). | 
**appId** | **String** | ULID (Universally Unique Lexicographically Sortable Identifier). | 
**customerId** | **String** | ULID (Universally Unique Lexicographically Sortable Identifier). | [optional] 
**message** | **String** |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


