# openmeter.model.NotificationRuleBalanceThresholdValue

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**value** | **double** | Value of the threshold. | 
**type** | [**NotificationRuleBalanceThresholdValueType**](NotificationRuleBalanceThresholdValueType.md) | Type of the threshold. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


