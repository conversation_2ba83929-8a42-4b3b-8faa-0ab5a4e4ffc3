# openmeter.model.MarketplaceListingList

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**page** | **int** | The page number. | 
**pageSize** | **int** | The number of items in the page. | 
**totalCount** | **int** | The total number of items. | 
**items** | [**BuiltList&lt;MarketplaceListing&gt;**](MarketplaceListing.md) | The items in the page. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


