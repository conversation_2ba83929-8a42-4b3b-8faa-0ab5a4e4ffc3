# openmeter.model.InvoicePendingLinesActionInput

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**filters** | [**InvoicePendingLinesActionFiltersInput**](InvoicePendingLinesActionFiltersInput.md) | Filters to apply when creating the invoice. | [optional] 
**asOf** | [**DateTime**](DateTime.md) | The time as of which the invoice is created.  If not provided, the current time is used. | [optional] 
**customerId** | **String** | The customer ID for which to create the invoice. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


