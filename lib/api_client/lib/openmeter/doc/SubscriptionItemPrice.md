# openmeter.model.SubscriptionItemPrice

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **String** | The type of the price. | 
**amount** | **String** | The amount of the unit price. | 
**paymentTerm** | [**PricePaymentTerm**](PricePaymentTerm.md) | The payment term of the flat price. Defaults to in advance. | [optional] [default to PricePaymentTerm.inAdvance]
**minimumAmount** | **String** | The customer is committed to spend at least the amount. | [optional] 
**maximumAmount** | **String** | The customer is limited to spend at most the amount. | [optional] 
**mode** | [**TieredPriceMode**](TieredPriceMode.md) | Defines if the tiering mode is volume-based or graduated: - In `volume`-based tiering, the maximum quantity within a period determines the per unit price. - In `graduated` tiering, pricing can change as the quantity grows. | 
**tiers** | [**BuiltList&lt;PriceTier&gt;**](PriceTier.md) | The tiers of the tiered price. At least one price component is required in each tier. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


