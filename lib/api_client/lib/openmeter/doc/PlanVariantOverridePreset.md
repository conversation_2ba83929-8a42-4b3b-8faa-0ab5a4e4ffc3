# openmeter.model.PlanVariantOverridePreset

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**variant** | [**PlanVariant**](PlanVariant.md) | The variant of the plan. | 
**currency** | **String** | The currency code. | [optional] 
**rateCards** | [**RateCardOverrides**](RateCardOverrides.md) |  | [optional] 
**discounts** | [**BuiltList&lt;Discount&gt;**](Discount.md) | The discounts on the plan. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


