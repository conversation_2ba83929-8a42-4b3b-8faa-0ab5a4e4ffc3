# openmeter.model.InvoiceSimulationInput

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**number** | **String** | The number of the invoice. | [optional] 
**currency** | **String** | Currency for all invoice line items.  Multi currency invoices are not supported yet. | 
**lines** | [**BuiltList&lt;InvoiceSimulationLine&gt;**](InvoiceSimulationLine.md) | Lines to be included in the generated invoice. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


