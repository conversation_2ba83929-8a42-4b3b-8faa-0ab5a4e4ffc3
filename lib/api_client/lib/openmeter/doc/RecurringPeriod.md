# openmeter.model.RecurringPeriod

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**interval** | [**RecurringPeriodInterval**](RecurringPeriodInterval.md) | The unit of time for the interval. Heuristically maps ISO duraitons to enum values or returns the ISO duration. | 
**intervalISO** | **String** | The unit of time for the interval in ISO8601 format. | 
**anchor** | [**DateTime**](DateTime.md) | A date-time anchor to base the recurring period on. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


