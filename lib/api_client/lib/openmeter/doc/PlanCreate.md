# openmeter.model.PlanCreate

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **String** | Human-readable name for the resource. Between 1 and 256 characters. | 
**description** | **String** | Optional description of the resource. Maximum 1024 characters. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional metadata for the resource. | [optional] 
**key** | **String** | A semi-unique identifier for the resource. | 
**alignment** | [**Alignment**](Alignment.md) | Alignment configuration for the plan. | [optional] 
**currency** | **String** | The currency code of the plan. | [default to USD]
**phases** | [**BuiltList&lt;PlanPhase&gt;**](PlanPhase.md) | The plan phase or pricing ramp allows changing a plan's rate cards over time as a subscription progresses. A phase switch occurs only at the end of a billing period, ensuring that a single subscription invoice will not include charges from different phase prices. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


