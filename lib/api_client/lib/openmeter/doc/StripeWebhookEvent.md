# openmeter.model.StripeWebhookEvent

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** | The event ID. | 
**type** | **String** | The event type. | 
**livemode** | **bool** | Live mode. | 
**created** | **int** | The event created timestamp. | 
**data** | [**StripeWebhookEventData**](StripeWebhookEventData.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


