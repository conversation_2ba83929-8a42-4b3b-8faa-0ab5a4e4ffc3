# openmeter.model.ResetEntitlementUsageInput

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**effectiveAt** | [**DateTime**](DateTime.md) | The time at which the reset takes effect, defaults to now. The reset cannot be in the future. The provided value is truncated to the minute due to how historical meter data is stored. | [optional] 
**retainAnchor** | **bool** | Determines whether the usage period anchor is retained or reset to the effectiveAt time. - If true, the usage period anchor is retained. - If false, the usage period anchor is reset to the effectiveAt time. | [optional] 
**preserveOverage** | **bool** | Determines whether the overage is preserved or forgiven, overriding the entitlement's default behavior. - If true, the overage is preserved. - If false, the overage is forgiven. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


