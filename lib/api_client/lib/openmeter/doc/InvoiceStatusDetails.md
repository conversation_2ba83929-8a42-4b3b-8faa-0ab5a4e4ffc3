# openmeter.model.InvoiceStatusDetails

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**immutable** | **bool** | Is the invoice editable? | 
**failed** | **bool** | Is the invoice in a failed state? | 
**extendedStatus** | **String** | Extended status information for the invoice. | 
**availableActions** | [**InvoiceAvailableActions**](InvoiceAvailableActions.md) | The actions that can be performed on the invoice. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


