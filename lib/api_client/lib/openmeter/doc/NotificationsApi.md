# openmeter.api.NotificationsApi

## Load the API package
```dart
import 'package:openmeter/api.dart';
```

All URIs are relative to *https://127.0.0.1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**createNotificationChannel**](NotificationsApi.md#createnotificationchannel) | **POST** /api/v1/notification/channels | Create a notification channel
[**createNotificationRule**](NotificationsApi.md#createnotificationrule) | **POST** /api/v1/notification/rules | Create a notification rule
[**deleteNotificationChannel**](NotificationsApi.md#deletenotificationchannel) | **DELETE** /api/v1/notification/channels/{channelId} | Delete a notification channel
[**deleteNotificationRule**](NotificationsApi.md#deletenotificationrule) | **DELETE** /api/v1/notification/rules/{ruleId} | Delete a notification rule
[**getNotificationChannel**](NotificationsApi.md#getnotificationchannel) | **GET** /api/v1/notification/channels/{channelId} | Get notification channel
[**getNotificationEvent**](NotificationsApi.md#getnotificationevent) | **GET** /api/v1/notification/events/{eventId} | Get notification event
[**getNotificationRule**](NotificationsApi.md#getnotificationrule) | **GET** /api/v1/notification/rules/{ruleId} | Get notification rule
[**listNotificationChannels**](NotificationsApi.md#listnotificationchannels) | **GET** /api/v1/notification/channels | List notification channels
[**listNotificationEvents**](NotificationsApi.md#listnotificationevents) | **GET** /api/v1/notification/events | List notification events
[**listNotificationRules**](NotificationsApi.md#listnotificationrules) | **GET** /api/v1/notification/rules | List notification rules
[**testNotificationRule**](NotificationsApi.md#testnotificationrule) | **POST** /api/v1/notification/rules/{ruleId}/test | Test notification rule
[**updateNotificationChannel**](NotificationsApi.md#updatenotificationchannel) | **PUT** /api/v1/notification/channels/{channelId} | Update a notification channel
[**updateNotificationRule**](NotificationsApi.md#updatenotificationrule) | **PUT** /api/v1/notification/rules/{ruleId} | Update a notification rule


# **createNotificationChannel**
> NotificationChannel createNotificationChannel(notificationChannelCreateRequest)

Create a notification channel

Create a new notification channel.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final NotificationChannelCreateRequest notificationChannelCreateRequest = ; // NotificationChannelCreateRequest | 

try {
    final response = api.createNotificationChannel(notificationChannelCreateRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->createNotificationChannel: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **notificationChannelCreateRequest** | [**NotificationChannelCreateRequest**](NotificationChannelCreateRequest.md)|  | 

### Return type

[**NotificationChannel**](NotificationChannel.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **createNotificationRule**
> NotificationRule createNotificationRule(notificationRuleCreateRequest)

Create a notification rule

Create a new notification rule.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final NotificationRuleCreateRequest notificationRuleCreateRequest = ; // NotificationRuleCreateRequest | 

try {
    final response = api.createNotificationRule(notificationRuleCreateRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->createNotificationRule: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **notificationRuleCreateRequest** | [**NotificationRuleCreateRequest**](NotificationRuleCreateRequest.md)|  | 

### Return type

[**NotificationRule**](NotificationRule.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteNotificationChannel**
> deleteNotificationChannel(channelId)

Delete a notification channel

Soft delete notification channel by id.  Once a notification channel is deleted it cannot be undeleted.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final String channelId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 

try {
    api.deleteNotificationChannel(channelId);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->deleteNotificationChannel: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **channelId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteNotificationRule**
> deleteNotificationRule(ruleId)

Delete a notification rule

Soft delete notification rule by id.  Once a notification rule is deleted it cannot be undeleted.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final String ruleId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 

try {
    api.deleteNotificationRule(ruleId);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->deleteNotificationRule: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ruleId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getNotificationChannel**
> NotificationChannel getNotificationChannel(channelId)

Get notification channel

Get a notification channel by id.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final String channelId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 

try {
    final response = api.getNotificationChannel(channelId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->getNotificationChannel: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **channelId** | **String**|  | 

### Return type

[**NotificationChannel**](NotificationChannel.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getNotificationEvent**
> NotificationEvent getNotificationEvent(eventId)

Get notification event

Get a notification event by id.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final String eventId = eventId_example; // String | 

try {
    final response = api.getNotificationEvent(eventId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->getNotificationEvent: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **eventId** | **String**|  | 

### Return type

[**NotificationEvent**](NotificationEvent.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getNotificationRule**
> NotificationRule getNotificationRule(ruleId)

Get notification rule

Get a notification rule by id.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final String ruleId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 

try {
    final response = api.getNotificationRule(ruleId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->getNotificationRule: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ruleId** | **String**|  | 

### Return type

[**NotificationRule**](NotificationRule.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **listNotificationChannels**
> NotificationChannelPaginatedResponse listNotificationChannels(includeDeleted, includeDisabled, page, pageSize, order, orderBy)

List notification channels

List all notification channels.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final bool includeDeleted = true; // bool | Include deleted notification channels in response.  Usage: `?includeDeleted=true`
final bool includeDisabled = true; // bool | Include disabled notification channels in response.  Usage: `?includeDisabled=false`
final int page = 56; // int | Start date-time in RFC 3339 format.  Inclusive.
final int pageSize = 56; // int | Number of items per page.  Default is 100.
final SortOrder order = ; // SortOrder | The order direction.
final NotificationChannelOrderBy orderBy = ; // NotificationChannelOrderBy | The order by field.

try {
    final response = api.listNotificationChannels(includeDeleted, includeDisabled, page, pageSize, order, orderBy);
    print(response);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->listNotificationChannels: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **includeDeleted** | **bool**| Include deleted notification channels in response.  Usage: `?includeDeleted=true` | [optional] [default to false]
 **includeDisabled** | **bool**| Include disabled notification channels in response.  Usage: `?includeDisabled=false` | [optional] [default to false]
 **page** | **int**| Start date-time in RFC 3339 format.  Inclusive. | [optional] [default to 1]
 **pageSize** | **int**| Number of items per page.  Default is 100. | [optional] [default to 100]
 **order** | [**SortOrder**](.md)| The order direction. | [optional] 
 **orderBy** | [**NotificationChannelOrderBy**](.md)| The order by field. | [optional] 

### Return type

[**NotificationChannelPaginatedResponse**](NotificationChannelPaginatedResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **listNotificationEvents**
> NotificationEventPaginatedResponse listNotificationEvents(from, to, feature, subject, rule, channel, page, pageSize, order, orderBy)

List notification events

List all notification events.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final DateTime from = 2023-01-01T01:01:01.001Z; // DateTime | Start date-time in RFC 3339 format. Inclusive.
final DateTime to = 2023-01-01T01:01:01.001Z; // DateTime | End date-time in RFC 3339 format. Inclusive.
final BuiltList<String> feature = ; // BuiltList<String> | Filtering by multiple feature ids or keys.  Usage: `?feature=feature-1&feature=feature-2`
final BuiltList<String> subject = ; // BuiltList<String> | Filtering by multiple subject ids or keys.  Usage: `?subject=subject-1&subject=subject-2`
final BuiltList<String> rule = ; // BuiltList<String> | Filtering by multiple rule ids.  Usage: `?rule=01J8J2XYZ2N5WBYK09EDZFBSZM&rule=01J8J4R4VZH180KRKQ63NB2VA5`
final BuiltList<String> channel = ; // BuiltList<String> | Filtering by multiple channel ids.  Usage: `?channel=01J8J4RXH778XB056JS088PCYT&channel=01J8J4S1R1G9EVN62RG23A9M6J`
final int page = 56; // int | Start date-time in RFC 3339 format.  Inclusive.
final int pageSize = 56; // int | Number of items per page.  Default is 100.
final SortOrder order = ; // SortOrder | The order direction.
final NotificationEventOrderBy orderBy = ; // NotificationEventOrderBy | The order by field.

try {
    final response = api.listNotificationEvents(from, to, feature, subject, rule, channel, page, pageSize, order, orderBy);
    print(response);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->listNotificationEvents: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **from** | **DateTime**| Start date-time in RFC 3339 format. Inclusive. | [optional] 
 **to** | **DateTime**| End date-time in RFC 3339 format. Inclusive. | [optional] 
 **feature** | [**BuiltList&lt;String&gt;**](String.md)| Filtering by multiple feature ids or keys.  Usage: `?feature=feature-1&feature=feature-2` | [optional] 
 **subject** | [**BuiltList&lt;String&gt;**](String.md)| Filtering by multiple subject ids or keys.  Usage: `?subject=subject-1&subject=subject-2` | [optional] 
 **rule** | [**BuiltList&lt;String&gt;**](String.md)| Filtering by multiple rule ids.  Usage: `?rule=01J8J2XYZ2N5WBYK09EDZFBSZM&rule=01J8J4R4VZH180KRKQ63NB2VA5` | [optional] 
 **channel** | [**BuiltList&lt;String&gt;**](String.md)| Filtering by multiple channel ids.  Usage: `?channel=01J8J4RXH778XB056JS088PCYT&channel=01J8J4S1R1G9EVN62RG23A9M6J` | [optional] 
 **page** | **int**| Start date-time in RFC 3339 format.  Inclusive. | [optional] [default to 1]
 **pageSize** | **int**| Number of items per page.  Default is 100. | [optional] [default to 100]
 **order** | [**SortOrder**](.md)| The order direction. | [optional] 
 **orderBy** | [**NotificationEventOrderBy**](.md)| The order by field. | [optional] 

### Return type

[**NotificationEventPaginatedResponse**](NotificationEventPaginatedResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **listNotificationRules**
> NotificationRulePaginatedResponse listNotificationRules(includeDeleted, includeDisabled, feature, channel, page, pageSize, order, orderBy)

List notification rules

List all notification rules.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final bool includeDeleted = true; // bool | Include deleted notification rules in response.  Usage: `?includeDeleted=true`
final bool includeDisabled = true; // bool | Include disabled notification rules in response.  Usage: `?includeDisabled=false`
final BuiltList<String> feature = ; // BuiltList<String> | Filtering by multiple feature ids/keys.  Usage: `?feature=feature-1&feature=feature-2`
final BuiltList<String> channel = ; // BuiltList<String> | Filtering by multiple notifiaction channel ids.  Usage: `?channel=01ARZ3NDEKTSV4RRFFQ69G5FAV&channel=01J8J2Y5X4NNGQS32CF81W95E3`
final int page = 56; // int | Start date-time in RFC 3339 format.  Inclusive.
final int pageSize = 56; // int | Number of items per page.  Default is 100.
final SortOrder order = ; // SortOrder | The order direction.
final NotificationRuleOrderBy orderBy = ; // NotificationRuleOrderBy | The order by field.

try {
    final response = api.listNotificationRules(includeDeleted, includeDisabled, feature, channel, page, pageSize, order, orderBy);
    print(response);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->listNotificationRules: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **includeDeleted** | **bool**| Include deleted notification rules in response.  Usage: `?includeDeleted=true` | [optional] [default to false]
 **includeDisabled** | **bool**| Include disabled notification rules in response.  Usage: `?includeDisabled=false` | [optional] [default to false]
 **feature** | [**BuiltList&lt;String&gt;**](String.md)| Filtering by multiple feature ids/keys.  Usage: `?feature=feature-1&feature=feature-2` | [optional] 
 **channel** | [**BuiltList&lt;String&gt;**](String.md)| Filtering by multiple notifiaction channel ids.  Usage: `?channel=01ARZ3NDEKTSV4RRFFQ69G5FAV&channel=01J8J2Y5X4NNGQS32CF81W95E3` | [optional] 
 **page** | **int**| Start date-time in RFC 3339 format.  Inclusive. | [optional] [default to 1]
 **pageSize** | **int**| Number of items per page.  Default is 100. | [optional] [default to 100]
 **order** | [**SortOrder**](.md)| The order direction. | [optional] 
 **orderBy** | [**NotificationRuleOrderBy**](.md)| The order by field. | [optional] 

### Return type

[**NotificationRulePaginatedResponse**](NotificationRulePaginatedResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **testNotificationRule**
> NotificationEvent testNotificationRule(ruleId)

Test notification rule

Test a notification rule by sending a test event with random data.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final String ruleId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 

try {
    final response = api.testNotificationRule(ruleId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->testNotificationRule: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ruleId** | **String**|  | 

### Return type

[**NotificationEvent**](NotificationEvent.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateNotificationChannel**
> NotificationChannel updateNotificationChannel(channelId, notificationChannelCreateRequest)

Update a notification channel

Update notification channel.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final String channelId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 
final NotificationChannelCreateRequest notificationChannelCreateRequest = ; // NotificationChannelCreateRequest | 

try {
    final response = api.updateNotificationChannel(channelId, notificationChannelCreateRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->updateNotificationChannel: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **channelId** | **String**|  | 
 **notificationChannelCreateRequest** | [**NotificationChannelCreateRequest**](NotificationChannelCreateRequest.md)|  | 

### Return type

[**NotificationChannel**](NotificationChannel.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateNotificationRule**
> NotificationRule updateNotificationRule(ruleId, notificationRuleCreateRequest)

Update a notification rule

Update notification rule.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getNotificationsApi();
final String ruleId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 
final NotificationRuleCreateRequest notificationRuleCreateRequest = ; // NotificationRuleCreateRequest | 

try {
    final response = api.updateNotificationRule(ruleId, notificationRuleCreateRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling NotificationsApi->updateNotificationRule: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ruleId** | **String**|  | 
 **notificationRuleCreateRequest** | [**NotificationRuleCreateRequest**](NotificationRuleCreateRequest.md)|  | 

### Return type

[**NotificationRule**](NotificationRule.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

