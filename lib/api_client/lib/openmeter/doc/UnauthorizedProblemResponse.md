# openmeter.model.UnauthorizedProblemResponse

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **String** | Type contains a URI that identifies the problem type. | [default to 'about:blank']
**title** | **String** | A a short, human-readable summary of the problem type. | 
**status** | **int** | The HTTP status code generated by the origin server for this occurrence of the problem. | [optional] 
**detail** | **String** | A human-readable explanation specific to this occurrence of the problem. | 
**instance** | **String** | A URI reference that identifies the specific occurrence of the problem. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


