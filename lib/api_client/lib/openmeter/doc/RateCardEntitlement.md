# openmeter.model.RateCardEntitlement

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional metadata for the feature. | [optional] 
**type** | **String** |  | 
**isSoftLimit** | **bool** | If softLimit=true the subject can use the feature even if the entitlement is exhausted, hasAccess will always be true. | [optional] [default to false]
**issueAfterReset** | **double** | You can grant usage automatically alongside the entitlement, the example scenario would be creating a starting balance. If an amount is specified here, a grant will be created alongside the entitlement with the specified amount. That grant will have it's rollover settings configured in a way that after each reset operation, the balance will return the original amount specified here. Manually creating such a grant would mean having the \"amount\", \"minRolloverAmount\", and \"maxRolloverAmount\" fields all be the same. | [optional] 
**issueAfterResetPriority** | **int** | Defines the grant priority for the default grant. | [optional] [default to 1]
**preserveOverageAtReset** | **bool** | If true, the overage is preserved at reset. If false, the usage is reset to 0. | [optional] [default to false]
**usagePeriod** | **String** | The interval of the metered entitlement. Defaults to the billing cadence of the rate card. | [optional] 
**config** | **String** | The JSON parsable config of the entitlement. This value is also returned when checking entitlement access and it is useful for configuring fine-grained access settings to the feature, implemented in your own system. Has to be an object. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


