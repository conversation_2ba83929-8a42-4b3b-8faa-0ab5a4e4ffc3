# openmeter.model.ValidationIssue

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**createdAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was created. | 
**updatedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was last updated. | 
**deletedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was permanently deleted. | [optional] 
**id** | **String** | ID of the charge or discount. | 
**severity** | [**ValidationIssueSeverity**](ValidationIssueSeverity.md) | The severity of the issue. | 
**field** | **String** | The field that the issue is related to, if available in JSON path format. | [optional] 
**code** | **String** | Machine indentifiable code for the issue, if available. | [optional] 
**component** | **String** | Component reporting the issue. | 
**message** | **String** | A human-readable description of the issue. | 
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional context for the issue. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


