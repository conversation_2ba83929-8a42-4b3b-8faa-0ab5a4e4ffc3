# openmeter.model.TieredPrice

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **String** | The type of the price.  One of: flat, unit, or tiered. | 
**mode** | [**TieredPriceMode**](TieredPriceMode.md) | Defines if the tiering mode is volume-based or graduated: - In `volume`-based tiering, the maximum quantity within a period determines the per unit price. - In `graduated` tiering, pricing can change as the quantity grows. | 
**tiers** | [**BuiltList&lt;PriceTier&gt;**](PriceTier.md) | The tiers of the tiered price. At least one price component is required in each tier. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


