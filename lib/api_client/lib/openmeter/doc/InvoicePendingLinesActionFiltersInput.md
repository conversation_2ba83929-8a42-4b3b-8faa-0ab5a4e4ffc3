# openmeter.model.InvoicePendingLinesActionFiltersInput

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**lineIds** | **BuiltList&lt;String&gt;** | The pending line items to include in the invoice, if not provided: - all line items that have invoice_at < asOf will be included - [progressive billing only] all usage based line items will be included up to asOf, new usage-based line items will be staged for the rest of the billing cycle  All lineIDs present in the list, must exists and must be invoicable as of asOf, or the action will fail. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


