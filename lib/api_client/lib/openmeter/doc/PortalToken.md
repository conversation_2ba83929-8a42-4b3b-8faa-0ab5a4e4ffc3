# openmeter.model.PortalToken

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** | ULID (Universally Unique Lexicographically Sortable Identifier). | [optional] 
**subject** | **String** |  | 
**expiresAt** | [**DateTime**](DateTime.md) | [RFC3339](https://tools.ietf.org/html/rfc3339) formatted date-time string in UTC. | [optional] 
**expired** | **bool** |  | [optional] 
**createdAt** | [**DateTime**](DateTime.md) | [RFC3339](https://tools.ietf.org/html/rfc3339) formatted date-time string in UTC. | [optional] 
**token** | **String** | The token is only returned at creation. | [optional] 
**allowedMeterSlugs** | **BuiltList&lt;String&gt;** | Optional, if defined only the specified meters will be allowed. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


