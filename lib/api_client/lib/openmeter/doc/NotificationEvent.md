# openmeter.model.NotificationEvent

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** | A unique identifier of the notification event. | 
**type** | [**NotificationEventType**](NotificationEventType.md) | Type of the notification event. | 
**createdAt** | [**DateTime**](DateTime.md) | Timestamp when the notification event was created in RFC 3339 format. | 
**rule** | [**NotificationRule**](NotificationRule.md) | The nnotification rule which generated this event. | 
**deliveryStatus** | [**BuiltList&lt;NotificationEventDeliveryStatus&gt;**](NotificationEventDeliveryStatus.md) | The delivery status of the notification event. | 
**payload** | [**NotificationEventPayload**](NotificationEventPayload.md) | Timestamp when the notification event was created in RFC 3339 format. | 
**annotations** | [**BuiltMap&lt;String, JsonObject&gt;**](JsonObject.md) | Set of key-value pairs managed by the system. Cannot be modified by user. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


