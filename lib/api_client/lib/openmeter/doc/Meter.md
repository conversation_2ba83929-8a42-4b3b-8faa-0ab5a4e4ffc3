# openmeter.model.Meter

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** | A unique identifier for the resource. | 
**description** | **String** | Optional description of the resource. Maximum 1024 characters. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional metadata for the resource. | [optional] 
**createdAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was created. | 
**updatedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was last updated. | 
**deletedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was permanently deleted. | [optional] 
**name** | **String** | Human-readable name for the resource. Between 1 and 256 characters. Defaults to the slug if not specified. | [optional] 
**slug** | **String** | A unique, human-readable identifier for the meter. Must consist only alphanumeric and underscore characters. | 
**aggregation** | [**MeterAggregation**](MeterAggregation.md) | The aggregation type to use for the meter. | 
**eventType** | **String** | The event type to aggregate. | 
**eventFrom** | [**DateTime**](DateTime.md) | The date since the meter should include events. Useful to skip old events. If not specified, all historical events are included. | [optional] 
**valueProperty** | **String** | JSONPath expression to extract the value from the ingested event's data property.  The ingested value for SUM, AVG, MIN, and MAX aggregations is a number or a string that can be parsed to a number.  For UNIQUE_COUNT aggregation, the ingested value must be a string. For COUNT aggregation the valueProperty is ignored. | [optional] 
**groupBy** | **BuiltMap&lt;String, String&gt;** | Named JSONPath expressions to extract the group by values from the event data.  Keys must be unique and consist only alphanumeric and underscore characters.  TODO: add key format enforcement | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


