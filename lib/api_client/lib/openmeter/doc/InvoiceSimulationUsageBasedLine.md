# openmeter.model.InvoiceSimulationUsageBasedLine

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **String** | Human-readable name for the resource. Between 1 and 256 characters. | 
**description** | **String** | Optional description of the resource. Maximum 1024 characters. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional metadata for the resource. | [optional] 
**taxConfig** | [**TaxConfig**](TaxConfig.md) | Tax config specify the tax configuration for this line. | [optional] 
**period** | [**Period**](Period.md) | Period of the line item applies to for revenue recognition pruposes.  Billing always treats periods as start being inclusive and end being exclusive. | 
**invoiceAt** | [**DateTime**](DateTime.md) | The time this line item should be invoiced. | 
**type** | **String** | Type of the line. | 
**price** | [**RateCardUsageBasedPrice**](RateCardUsageBasedPrice.md) | Price of the usage-based item being sold. | 
**featureKey** | **String** | The feature that the usage is based on. | 
**quantity** | **String** | The quantity of the item being sold. | 
**preLinePeriodQuantity** | **String** | The quantity of the item used before this line's period, if the line is billed progressively. | [optional] 
**id** | **String** | ID of the line. If not specified it will be auto-generated.  When discounts are specified, this must be provided, so that the discount can reference it. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


