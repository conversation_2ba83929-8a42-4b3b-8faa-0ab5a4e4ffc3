# openmeter.api.MetersApi

## Load the API package
```dart
import 'package:openmeter/api.dart';
```

All URIs are relative to *https://127.0.0.1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**createMeter**](MetersApi.md#createmeter) | **POST** /api/v1/meters | Create meter
[**deleteMeter**](MetersApi.md#deletemeter) | **DELETE** /api/v1/meters/{meterIdOrSlug} | Delete meter
[**getMeter**](MetersApi.md#getmeter) | **GET** /api/v1/meters/{meterIdOrSlug} | Get meter
[**listMeterSubjects**](MetersApi.md#listmetersubjects) | **GET** /api/v1/meters/{meterIdOrSlug}/subjects | List meter subjects
[**listMeters**](MetersApi.md#listmeters) | **GET** /api/v1/meters | List meters
[**queryMeter**](MetersApi.md#querymeter) | **GET** /api/v1/meters/{meterIdOrSlug}/query | Query meter Query meter
[**updateMeter**](MetersApi.md#updatemeter) | **PUT** /api/v1/meters/{meterIdOrSlug} | Update meter


# **createMeter**
> Meter createMeter(meterCreate)

Create meter

Create a meter.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getMetersApi();
final MeterCreate meterCreate = ; // MeterCreate | 

try {
    final response = api.createMeter(meterCreate);
    print(response);
} catch on DioException (e) {
    print('Exception when calling MetersApi->createMeter: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **meterCreate** | [**MeterCreate**](MeterCreate.md)|  | 

### Return type

[**Meter**](Meter.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteMeter**
> deleteMeter(meterIdOrSlug)

Delete meter

Delete a meter.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getMetersApi();
final String meterIdOrSlug = meterIdOrSlug_example; // String | 

try {
    api.deleteMeter(meterIdOrSlug);
} catch on DioException (e) {
    print('Exception when calling MetersApi->deleteMeter: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **meterIdOrSlug** | **String**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getMeter**
> Meter getMeter(meterIdOrSlug)

Get meter

Get a meter by ID or slug.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getMetersApi();
final String meterIdOrSlug = meterIdOrSlug_example; // String | 

try {
    final response = api.getMeter(meterIdOrSlug);
    print(response);
} catch on DioException (e) {
    print('Exception when calling MetersApi->getMeter: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **meterIdOrSlug** | **String**|  | 

### Return type

[**Meter**](Meter.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **listMeterSubjects**
> BuiltList<String> listMeterSubjects(meterIdOrSlug)

List meter subjects

List subjects for a meter.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getMetersApi();
final String meterIdOrSlug = meterIdOrSlug_example; // String | 

try {
    final response = api.listMeterSubjects(meterIdOrSlug);
    print(response);
} catch on DioException (e) {
    print('Exception when calling MetersApi->listMeterSubjects: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **meterIdOrSlug** | **String**|  | 

### Return type

**BuiltList&lt;String&gt;**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **listMeters**
> BuiltList<Meter> listMeters()

List meters

List meters.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getMetersApi();

try {
    final response = api.listMeters();
    print(response);
} catch on DioException (e) {
    print('Exception when calling MetersApi->listMeters: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;Meter&gt;**](Meter.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **queryMeter**
> MeterQueryResult queryMeter(meterIdOrSlug, clientId, from, to, windowSize, windowTimeZone, subject, filterGroupBy, groupBy)

Query meter Query meter

Query meter for usage. Query meter for usage.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getMetersApi();
final String meterIdOrSlug = meterIdOrSlug_example; // String | 
final String clientId = clientId_example; // String | Client ID Useful to track progress of a query.
final DateTime from = 2023-01-01T01:01:01.001Z; // DateTime | Start date-time in RFC 3339 format.  Inclusive.
final DateTime to = 2023-01-01T01:01:01.001Z; // DateTime | End date-time in RFC 3339 format.  Inclusive.
final WindowSize windowSize = ; // WindowSize | If not specified, a single usage aggregate will be returned for the entirety of the specified period for each subject and group.
final String windowTimeZone = windowTimeZone_example; // String | The value is the name of the time zone as defined in the IANA Time Zone Database (http://www.iana.org/time-zones). If not specified, the UTC timezone will be used.
final BuiltList<String> subject = ; // BuiltList<String> | Filtering by multiple subjects.
final BuiltMap<String, String> filterGroupBy = ; // BuiltMap<String, String> | Simple filter for group bys with exact match.
final BuiltList<String> groupBy = ; // BuiltList<String> | If not specified a single aggregate will be returned for each subject and time window. `subject` is a reserved group by value.

try {
    final response = api.queryMeter(meterIdOrSlug, clientId, from, to, windowSize, windowTimeZone, subject, filterGroupBy, groupBy);
    print(response);
} catch on DioException (e) {
    print('Exception when calling MetersApi->queryMeter: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **meterIdOrSlug** | **String**|  | 
 **clientId** | **String**| Client ID Useful to track progress of a query. | [optional] 
 **from** | **DateTime**| Start date-time in RFC 3339 format.  Inclusive. | [optional] 
 **to** | **DateTime**| End date-time in RFC 3339 format.  Inclusive. | [optional] 
 **windowSize** | [**WindowSize**](.md)| If not specified, a single usage aggregate will be returned for the entirety of the specified period for each subject and group. | [optional] 
 **windowTimeZone** | **String**| The value is the name of the time zone as defined in the IANA Time Zone Database (http://www.iana.org/time-zones). If not specified, the UTC timezone will be used. | [optional] [default to 'UTC']
 **subject** | [**BuiltList&lt;String&gt;**](String.md)| Filtering by multiple subjects. | [optional] 
 **filterGroupBy** | [**BuiltMap&lt;String, String&gt;**](String.md)| Simple filter for group bys with exact match. | [optional] 
 **groupBy** | [**BuiltList&lt;String&gt;**](String.md)| If not specified a single aggregate will be returned for each subject and time window. `subject` is a reserved group by value. | [optional] 

### Return type

[**MeterQueryResult**](MeterQueryResult.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, text/csv, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateMeter**
> Meter updateMeter(meterIdOrSlug, meterUpdate)

Update meter

Update a meter.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getMetersApi();
final String meterIdOrSlug = meterIdOrSlug_example; // String | 
final MeterUpdate meterUpdate = ; // MeterUpdate | 

try {
    final response = api.updateMeter(meterIdOrSlug, meterUpdate);
    print(response);
} catch on DioException (e) {
    print('Exception when calling MetersApi->updateMeter: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **meterIdOrSlug** | **String**|  | 
 **meterUpdate** | [**MeterUpdate**](MeterUpdate.md)|  | 

### Return type

[**Meter**](Meter.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

