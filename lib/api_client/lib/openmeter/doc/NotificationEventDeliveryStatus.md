# openmeter.model.NotificationEventDeliveryStatus

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**state** | [**NotificationEventDeliveryStatusState**](NotificationEventDeliveryStatusState.md) | Delivery state of the notification event to the channel. | 
**reason** | **String** | The reason of the last deliverry state update. | 
**updatedAt** | [**DateTime**](DateTime.md) | Timestamp of when the status was last updated in RFC 3339 format. | 
**channel** | [**NotificationChannelMeta**](NotificationChannelMeta.md) | Notification channel the delivery sattus associated with. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


