# openmeter.model.SubscriptionItemIncluded

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**feature** | [**Feature**](Feature.md) | The feature the customer is entitled to use. | 
**entitlement** | [**Entitlement**](Entitlement.md) | The entitlement of the Subscription Item. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


