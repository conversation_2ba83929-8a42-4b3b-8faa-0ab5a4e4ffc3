# openmeter.model.VoidInvoiceActionLineOverride

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**lineId** | **String** | The line item ID to override. | 
**action** | [**VoidInvoiceActionCreateItem**](VoidInvoiceActionCreateItem.md) | The action to take on the line item. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


