# openmeter.model.SubscriptionChangeResponseBody

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**current** | [**Subscription**](Subscription.md) | The current subscription before the change. | 
**next** | [**SubscriptionExpanded**](SubscriptionExpanded.md) | The new state of the subscription after the change. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


