# openmeter.model.TaxConfig

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**behavior** | [**TaxBehavior**](TaxBehavior.md) | Tax behavior.  If not specified the billing profile is used to determine the tax behavior. If not specified in the billing profile, the provider's default behavior is used. | [optional] 
**stripe** | [**StripeTaxConfig**](StripeTaxConfig.md) | Stripe tax config. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


