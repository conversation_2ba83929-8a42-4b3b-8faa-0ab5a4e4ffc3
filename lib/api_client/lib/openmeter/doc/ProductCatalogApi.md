# openmeter.api.ProductCatalogApi

## Load the API package
```dart
import 'package:openmeter/api.dart';
```

All URIs are relative to *https://127.0.0.1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**archivePlan**](ProductCatalogApi.md#archiveplan) | **POST** /api/v1/plans/{planId}/archive | Archive plan version
[**createPlan**](ProductCatalogApi.md#createplan) | **POST** /api/v1/plans | Create a plan
[**deletePlan**](ProductCatalogApi.md#deleteplan) | **DELETE** /api/v1/plans/{planId} | Delete plan
[**getPlan**](ProductCatalogApi.md#getplan) | **GET** /api/v1/plans/{planId} | Get plan
[**listPlans**](ProductCatalogApi.md#listplans) | **GET** /api/v1/plans | List plans
[**nextPlan**](ProductCatalogApi.md#nextplan) | **POST** /api/v1/plans/{planIdOrKey}/next | New draft plan
[**publishPlan**](ProductCatalogApi.md#publishplan) | **POST** /api/v1/plans/{planId}/publish | Publish plan
[**updatePlan**](ProductCatalogApi.md#updateplan) | **PUT** /api/v1/plans/{planId} | Update a plan


# **archivePlan**
> Plan archivePlan(planId)

Archive plan version

Archive a plan version.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getProductCatalogApi();
final String planId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 

try {
    final response = api.archivePlan(planId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProductCatalogApi->archivePlan: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **planId** | **String**|  | 

### Return type

[**Plan**](Plan.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **createPlan**
> Plan createPlan(planCreate)

Create a plan

Create a new plan.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getProductCatalogApi();
final PlanCreate planCreate = ; // PlanCreate | 

try {
    final response = api.createPlan(planCreate);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProductCatalogApi->createPlan: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **planCreate** | [**PlanCreate**](PlanCreate.md)|  | 

### Return type

[**Plan**](Plan.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deletePlan**
> deletePlan(planId)

Delete plan

Soft delete plan by plan.id.  Once a plan is deleted it cannot be undeleted.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getProductCatalogApi();
final String planId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 

try {
    api.deletePlan(planId);
} catch on DioException (e) {
    print('Exception when calling ProductCatalogApi->deletePlan: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **planId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getPlan**
> Plan getPlan(planId, includeLatest)

Get plan

Get a plan by id or key. The latest published version is returned if latter is used.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getProductCatalogApi();
final NextPlanPlanIdOrKeyParameter planId = ; // NextPlanPlanIdOrKeyParameter | 
final bool includeLatest = true; // bool | Include latest version of the Plan instead of the version in active state.  Usage: `?includeLatest=true`

try {
    final response = api.getPlan(planId, includeLatest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProductCatalogApi->getPlan: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **planId** | [**NextPlanPlanIdOrKeyParameter**](.md)|  | 
 **includeLatest** | **bool**| Include latest version of the Plan instead of the version in active state.  Usage: `?includeLatest=true` | [optional] [default to false]

### Return type

[**Plan**](Plan.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **listPlans**
> PlanPaginatedResponse listPlans(includeDeleted, id, key, keyVersion, status, currency, page, pageSize, order, orderBy)

List plans

List all plans.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getProductCatalogApi();
final bool includeDeleted = true; // bool | Include deleted plans in response.  Usage: `?includeDeleted=true`
final BuiltList<String> id = ; // BuiltList<String> | Filter by plan.id attribute
final BuiltList<String> key = ; // BuiltList<String> | Filter by plan.key attribute
final BuiltMap<String, BuiltList<int>> keyVersion = ; // BuiltMap<String, BuiltList<int>> | Filter by plan.key and plan.version attributes
final BuiltList<PlanStatus> status = ; // BuiltList<PlanStatus> | Only return plans with the given status.  Usage: - `?status=active`: return only the currently active plan - `?status=draft`: return only the draft plan - `?status=archived`: return only the archived plans
final BuiltList<String> currency = ; // BuiltList<String> | Filter by plan.currency attribute
final int page = 56; // int | Start date-time in RFC 3339 format.  Inclusive.
final int pageSize = 56; // int | Number of items per page.  Default is 100.
final SortOrder order = ; // SortOrder | The order direction.
final PlanOrderBy orderBy = ; // PlanOrderBy | The order by field.

try {
    final response = api.listPlans(includeDeleted, id, key, keyVersion, status, currency, page, pageSize, order, orderBy);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProductCatalogApi->listPlans: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **includeDeleted** | **bool**| Include deleted plans in response.  Usage: `?includeDeleted=true` | [optional] [default to false]
 **id** | [**BuiltList&lt;String&gt;**](String.md)| Filter by plan.id attribute | [optional] 
 **key** | [**BuiltList&lt;String&gt;**](String.md)| Filter by plan.key attribute | [optional] 
 **keyVersion** | [**BuiltMap&lt;String, BuiltList&lt;int&gt;&gt;**](BuiltList&lt;int&gt;.md)| Filter by plan.key and plan.version attributes | [optional] 
 **status** | [**BuiltList&lt;PlanStatus&gt;**](PlanStatus.md)| Only return plans with the given status.  Usage: - `?status=active`: return only the currently active plan - `?status=draft`: return only the draft plan - `?status=archived`: return only the archived plans | [optional] 
 **currency** | [**BuiltList&lt;String&gt;**](String.md)| Filter by plan.currency attribute | [optional] 
 **page** | **int**| Start date-time in RFC 3339 format.  Inclusive. | [optional] [default to 1]
 **pageSize** | **int**| Number of items per page.  Default is 100. | [optional] [default to 100]
 **order** | [**SortOrder**](.md)| The order direction. | [optional] 
 **orderBy** | [**PlanOrderBy**](.md)| The order by field. | [optional] 

### Return type

[**PlanPaginatedResponse**](PlanPaginatedResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **nextPlan**
> Plan nextPlan(planIdOrKey)

New draft plan

Create a new draft version from plan. It returns error if there is already a plan in draft or planId does not reference the latest published version.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getProductCatalogApi();
final NextPlanPlanIdOrKeyParameter planIdOrKey = ; // NextPlanPlanIdOrKeyParameter | 

try {
    final response = api.nextPlan(planIdOrKey);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProductCatalogApi->nextPlan: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **planIdOrKey** | [**NextPlanPlanIdOrKeyParameter**](.md)|  | 

### Return type

[**Plan**](Plan.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **publishPlan**
> Plan publishPlan(planId)

Publish plan

Publish a plan version.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getProductCatalogApi();
final String planId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 

try {
    final response = api.publishPlan(planId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProductCatalogApi->publishPlan: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **planId** | **String**|  | 

### Return type

[**Plan**](Plan.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updatePlan**
> Plan updatePlan(planId, planReplaceUpdate)

Update a plan

Update plan by id.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getProductCatalogApi();
final String planId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 
final PlanReplaceUpdate planReplaceUpdate = ; // PlanReplaceUpdate | 

try {
    final response = api.updatePlan(planId, planReplaceUpdate);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProductCatalogApi->updatePlan: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **planId** | **String**|  | 
 **planReplaceUpdate** | [**PlanReplaceUpdate**](PlanReplaceUpdate.md)|  | 

### Return type

[**Plan**](Plan.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

