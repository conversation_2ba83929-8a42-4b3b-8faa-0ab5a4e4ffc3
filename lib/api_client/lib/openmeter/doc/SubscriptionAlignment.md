# openmeter.model.SubscriptionAlignment

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**billablesMustAlign** | **bool** | Whether all Billable items and RateCards must align. Alignment means the Price's BillingCadence must align for both duration and anchor time. | [optional] 
**currentAlignedBillingPeriod** | [**Period**](Period.md) | The current billing period. Only has value if the subscription is aligned and active. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


