# openmeter.model.RateCard

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **String** | The type of the RateCard. | 
**key** | **String** | A semi-unique identifier for the resource. | 
**name** | **String** | Human-readable name for the resource. Between 1 and 256 characters. | 
**description** | **String** | Optional description of the resource. Maximum 1024 characters. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional metadata for the resource. | [optional] 
**featureKey** | **String** | The feature the customer is entitled to use. | [optional] 
**entitlementTemplate** | [**RateCardEntitlement**](RateCardEntitlement.md) | The entitlement of the rate card. Only available when featureKey is set. | [optional] 
**taxConfig** | [**TaxConfig**](TaxConfig.md) | The tax config of the rate card. When undefined, the tax config of the feature or the default tax config of the plan is used. | [optional] 
**billingCadence** | **String** | The billing cadence of the rate card. | 
**price** | [**RateCardUsageBasedPrice**](RateCardUsageBasedPrice.md) | The price of the rate card. When null, the feature or service is free. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


