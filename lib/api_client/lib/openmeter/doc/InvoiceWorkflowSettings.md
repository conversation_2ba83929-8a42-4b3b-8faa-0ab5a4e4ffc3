# openmeter.model.InvoiceWorkflowSettings

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**apps** | [**BillingProfileAppsOrReference**](BillingProfileAppsOrReference.md) | The apps that will be used to orchestrate the invoice's workflow. | [optional] 
**sourceBillingProfileId** | **String** | sourceBillingProfileID is the billing profile on which the workflow was based on.  The profile is snapshotted on invoice creation, after which it can be altered independently of the profile itself. | 
**workflow** | [**BillingWorkflow**](BillingWorkflow.md) | The workflow details used by this invoice. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


