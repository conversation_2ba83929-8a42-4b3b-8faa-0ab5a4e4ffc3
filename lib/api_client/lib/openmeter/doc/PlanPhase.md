# openmeter.model.PlanPhase

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**key** | **String** | A semi-unique identifier for the resource. | 
**name** | **String** | Human-readable name for the resource. Between 1 and 256 characters. | 
**description** | **String** | Optional description of the resource. Maximum 1024 characters. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional metadata for the resource. | [optional] 
**duration** | **String** | The duration of the phase. | 
**rateCards** | [**BuiltList&lt;RateCard&gt;**](RateCard.md) | The rate cards of the plan. | 
**discounts** | [**BuiltList&lt;Discount&gt;**](Discount.md) | The discounts on the plan. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


