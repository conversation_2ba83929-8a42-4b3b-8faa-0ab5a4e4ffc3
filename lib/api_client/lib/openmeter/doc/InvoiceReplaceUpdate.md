# openmeter.model.InvoiceReplaceUpdate

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**description** | **String** | Optional description of the resource. Maximum 1024 characters. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional metadata for the resource. | [optional] 
**draftUntil** | [**DateTime**](DateTime.md) | The time until the invoice is in draft status.  On draft invoice creation it is calculated from the workflow settings.  If manual approval is required, the draftUntil time is set. | [optional] 
**supplier** | [**BillingPartyReplaceUpdate**](BillingPartyReplaceUpdate.md) | The supplier of the lines included in the invoice. | 
**customer** | [**BillingPartyReplaceUpdate**](BillingPartyReplaceUpdate.md) | The customer the invoice is sent to. | 
**lines** | [**BuiltList&lt;InvoiceLineReplaceUpdate&gt;**](InvoiceLineReplaceUpdate.md) | The lines included in the invoice. | 
**workflow** | [**InvoiceWorkflowReplaceUpdate**](InvoiceWorkflowReplaceUpdate.md) | The workflow settings for the invoice. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


