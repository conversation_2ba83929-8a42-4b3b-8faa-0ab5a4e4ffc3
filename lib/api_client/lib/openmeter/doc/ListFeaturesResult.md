# openmeter.model.ListFeaturesResult

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**totalCount** | **int** | The items in the current page. | 
**page** | **int** | The items in the current page. | 
**pageSize** | **int** | The items in the current page. | 
**items** | [**BuiltList&lt;Feature&gt;**](Feature.md) | The items in the current page. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


