# openmeter.model.InvoiceWorkflowInvoicingSettingsReplaceUpdate

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**autoAdvance** | **bool** | Whether to automatically issue the invoice after the draftPeriod has passed. | [optional] [default to true]
**draftPeriod** | **String** | The period for the invoice to be kept in draft status for manual reviews. | [optional] [default to 'P1D']
**dueAfter** | **String** | The period after which the invoice is due. | [optional] [default to 'P7D']
**defaultTaxConfig** | [**TaxConfig**](TaxConfig.md) | Default tax configuration to apply to the invoices. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


