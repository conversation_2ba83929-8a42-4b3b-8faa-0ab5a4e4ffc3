# openmeter.model.WindowedBalanceHistory

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**windowedHistory** | [**BuiltList&lt;BalanceHistoryWindow&gt;**](BalanceHistoryWindow.md) | The windowed balance history. - It only returns rows for windows where there was usage. - The windows are inclusive at their start and exclusive at their end. - The last window may be smaller than the window size and is inclusive at both ends. | 
**burndownHistory** | [**BuiltList&lt;GrantBurnDownHistorySegment&gt;**](GrantBurnDownHistorySegment.md) | Grant burndown history. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


