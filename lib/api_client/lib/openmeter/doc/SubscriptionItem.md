# openmeter.model.SubscriptionItem

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** | A unique identifier for the resource. | 
**name** | **String** | Human-readable name for the resource. Between 1 and 256 characters. | 
**description** | **String** | Optional description of the resource. Maximum 1024 characters. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Additional metadata for the resource. | [optional] 
**createdAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was created. | 
**updatedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was last updated. | 
**deletedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was permanently deleted. | [optional] 
**activeFrom** | [**DateTime**](DateTime.md) | The cadence start of the resource. | 
**activeTo** | [**DateTime**](DateTime.md) | The cadence end of the resource. | [optional] 
**key** | **String** | The identifier of the RateCard. SubscriptionItem/RateCard can be identified, it has a reference:  1. If a Feature is associated with the SubscriptionItem, it is identified by the Feature 1.1 It can be an ID reference, for an exact version of the Feature (Features can change across versions) 1.2 It can be a Key reference, which always refers to the latest (active or inactive) version of a Feature  2. If a Feature is not associated with the SubscriptionItem, it is referenced by the Price  We say “referenced by the Price” regardless of how a price itself is referenced, it colloquially makes sense to say “paying the same price for the same thing”. In practice this should be derived from what's printed on the invoice line-item. | 
**featureKey** | **String** | The feature's key (if present). | [optional] 
**billingCadence** | **String** | The billing cadence of the rate card. When null, the rate card is a one-time purchase. | 
**price** | [**SubscriptionItemPrice**](SubscriptionItemPrice.md) | The price of the rate card. When null, the feature or service is free. | 
**included** | [**SubscriptionItemIncluded**](SubscriptionItemIncluded.md) | Describes what access is gained via the SubscriptionItem | [optional] 
**taxConfig** | [**TaxConfig**](TaxConfig.md) | The tax config of the Subscription Item. When undefined, the tax config of the feature or the default tax config of the plan is used. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


