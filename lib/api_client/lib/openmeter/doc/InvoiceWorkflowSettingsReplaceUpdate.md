# openmeter.model.InvoiceWorkflowSettingsReplaceUpdate

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**invoicing** | [**InvoiceWorkflowInvoicingSettingsReplaceUpdate**](InvoiceWorkflowInvoicingSettingsReplaceUpdate.md) | The invoicing settings for this workflow | 
**payment** | [**BillingWorkflowPaymentSettings**](BillingWorkflowPaymentSettings.md) | The payment settings for this workflow | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


