# openmeter.model.MeterQueryResult

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**from** | [**DateTime**](DateTime.md) | The start of the period the usage is queried from. If not specified, the usage is queried from the beginning of time. | [optional] 
**to** | [**DateTime**](DateTime.md) | The end of the period the usage is queried to. If not specified, the usage is queried up to the current time. | [optional] 
**windowSize** | [**WindowSize**](WindowSize.md) | The window size that the usage is aggregated. If not specified, the usage is aggregated over the entire period. | [optional] 
**data** | [**BuiltList&lt;MeterQueryRow&gt;**](MeterQueryRow.md) | The usage data. If no data is available, an empty array is returned. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


