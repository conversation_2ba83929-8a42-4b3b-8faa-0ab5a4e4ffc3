# openmeter.model.PriceTier

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**upToAmount** | **String** | Up to and including to this quantity will be contained in the tier. If null, the tier is open-ended. | [optional] 
**flatPrice** | [**FlatPrice**](FlatPrice.md) | The flat price component of the tier. | 
**unitPrice** | [**UnitPrice**](UnitPrice.md) | The unit price component of the tier. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


