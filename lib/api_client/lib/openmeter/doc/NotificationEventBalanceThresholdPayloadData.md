# openmeter.model.NotificationEventBalanceThresholdPayloadData

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**entitlement** | [**EntitlementMetered**](EntitlementMetered.md) |  | 
**feature** | [**Feature**](Feature.md) |  | 
**subject** | [**Subject**](Subject.md) |  | 
**value** | [**EntitlementValue**](EntitlementValue.md) |  | 
**threshold** | [**NotificationRuleBalanceThresholdValue**](NotificationRuleBalanceThresholdValue.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


