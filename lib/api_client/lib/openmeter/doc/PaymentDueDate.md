# openmeter.model.PaymentDueDate

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**dueAt** | [**DateTime**](DateTime.md) | When the payment is due. | 
**notes** | **String** | Other details to take into account for the due date. | [optional] 
**amount** | **String** | How much needs to be paid by the date. | 
**percent** | **double** | Percentage of the total that should be paid by the date. | [optional] 
**currency** | **String** | If different from the parent document's base currency. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


