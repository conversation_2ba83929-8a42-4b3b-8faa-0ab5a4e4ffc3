# openmeter.model.MarketplaceListing

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | [**AppType**](AppType.md) | The app's type | 
**name** | **String** | The app's name. | 
**description** | **String** | The app's description. | 
**capabilities** | [**BuiltList&lt;AppCapability&gt;**](AppCapability.md) | The app's capabilities. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


