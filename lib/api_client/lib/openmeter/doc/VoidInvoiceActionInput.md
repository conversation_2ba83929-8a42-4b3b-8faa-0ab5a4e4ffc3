# openmeter.model.VoidInvoiceActionInput

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**action** | [**VoidInvoiceActionCreate**](VoidInvoiceActionCreate.md) | The action to take on the voided line items. | 
**reason** | **String** | The reason for voiding the invoice. | 
**overrides** | [**BuiltList&lt;VoidInvoiceActionLineOverride&gt;**](VoidInvoiceActionLineOverride.md) | Per line item overrides for the action.  If not specified, the `action` will be applied to all line items. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


