# openmeter.model.VoidInvoiceLineActionCreateItem

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **String** | The action to take on the line item. | 
**nextInvoiceAt** | [**DateTime**](DateTime.md) | The time at which the line item should be invoiced again.  If not provided, the line item will be re-invoiced now. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


