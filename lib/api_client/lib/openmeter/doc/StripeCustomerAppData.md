# openmeter.model.StripeCustomerAppData

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** | The app ID. If not provided, it will use the global default for the app type. | [optional] 
**type** | **String** | The app name. | 
**app** | [**StripeApp**](StripeApp.md) | The installed stripe app this data belongs to. | [optional] 
**stripeCustomerId** | **String** | The Stripe customer ID. | 
**stripeDefaultPaymentMethodId** | **String** | The Stripe default payment method ID. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


