# openmeter.model.NotificationChannelWebhook

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**createdAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was created. | 
**updatedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was last updated. | 
**deletedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was permanently deleted. | [optional] 
**id** | **String** | Identifies the notification channel. | 
**type** | **String** | Notification channel type. | 
**name** | **String** | User friendly name of the channel. | 
**disabled** | **bool** | Whether the channel is disabled or not. | [optional] [default to false]
**url** | **String** | Webhook URL where the notification is sent. | 
**customHeaders** | **BuiltMap&lt;String, String&gt;** | Custom HTTP headers sent as part of the webhook request. | [optional] 
**signingSecret** | **String** | Signing secret used for webhook request validation on the receiving end.  Format: `base64` encoded random bytes optionally prefixed with `whsec_`. Recommended size: 24 | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


