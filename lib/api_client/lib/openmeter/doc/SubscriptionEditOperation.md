# openmeter.model.SubscriptionEditOperation

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**op** | **String** |  | 
**phaseKey** | **String** |  | 
**rateCard** | [**RateCard**](RateCard.md) |  | 
**itemKey** | **String** |  | 
**phase** | [**SubscriptionPhaseCreate**](SubscriptionPhaseCreate.md) |  | 
**shift** | [**RemovePhaseShifting**](RemovePhaseShifting.md) |  | 
**extendBy** | **String** |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


