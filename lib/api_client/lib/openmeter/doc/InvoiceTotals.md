# openmeter.model.InvoiceTotals

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**amount** | **String** | The total value of the line before taxes, discounts and commitments. | 
**chargesTotal** | **String** | The amount of value of the line that are due to additional charges. | 
**discountsTotal** | **String** | The amount of value of the line that are due to discounts. | 
**taxesInclusiveTotal** | **String** | The total amount of taxes that are included in the line. | 
**taxesExclusiveTotal** | **String** | The total amount of taxes that are added on top of amount from the line. | 
**taxesTotal** | **String** | The total amount of taxes for this line. | 
**total** | **String** | The total amount value of the line after taxes, discounts and commitments. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


