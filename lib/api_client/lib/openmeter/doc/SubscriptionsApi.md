# openmeter.api.SubscriptionsApi

## Load the API package
```dart
import 'package:openmeter/api.dart';
```

All URIs are relative to *https://127.0.0.1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**cancelSubscription**](SubscriptionsApi.md#cancelsubscription) | **POST** /api/v1/subscriptions/{subscriptionId}/cancel | Cancel subscription
[**changeSubscription**](SubscriptionsApi.md#changesubscription) | **POST** /api/v1/subscriptions/{subscriptionId}/change | Change subscription
[**createSubscription**](SubscriptionsApi.md#createsubscription) | **POST** /api/v1/subscriptions | Create subscription
[**editSubscription**](SubscriptionsApi.md#editsubscription) | **PATCH** /api/v1/subscriptions/{subscriptionId} | Edit subscription
[**getSubscription**](SubscriptionsApi.md#getsubscription) | **GET** /api/v1/subscriptions/{subscriptionId} | Get subscription
[**migrateSubscription**](SubscriptionsApi.md#migratesubscription) | **POST** /api/v1/subscriptions/{subscriptionId}/migrate | Migrate subscription
[**restoreSubscription**](SubscriptionsApi.md#restoresubscription) | **POST** /api/v1/subscriptions/{subscriptionId}/restore | Restore subscription
[**unscheduleCancelation**](SubscriptionsApi.md#unschedulecancelation) | **POST** /api/v1/subscriptions/{subscriptionId}/unschedule-cancelation | Unschedule cancelation


# **cancelSubscription**
> Subscription cancelSubscription(subscriptionId, cancelSubscriptionRequest)

Cancel subscription

Cancels the subscription. Will result in a scheduling conflict if there are other subscriptions scheduled to start after the cancellation time.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getSubscriptionsApi();
final String subscriptionId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 
final CancelSubscriptionRequest cancelSubscriptionRequest = ; // CancelSubscriptionRequest | 

try {
    final response = api.cancelSubscription(subscriptionId, cancelSubscriptionRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling SubscriptionsApi->cancelSubscription: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **subscriptionId** | **String**|  | 
 **cancelSubscriptionRequest** | [**CancelSubscriptionRequest**](CancelSubscriptionRequest.md)|  | 

### Return type

[**Subscription**](Subscription.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **changeSubscription**
> SubscriptionChangeResponseBody changeSubscription(subscriptionId, subscriptionChange)

Change subscription

Closes a running subscription and starts a new one according to the specification. Can be used for upgrades, downgrades, and plan changes.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getSubscriptionsApi();
final String subscriptionId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 
final SubscriptionChange subscriptionChange = ; // SubscriptionChange | 

try {
    final response = api.changeSubscription(subscriptionId, subscriptionChange);
    print(response);
} catch on DioException (e) {
    print('Exception when calling SubscriptionsApi->changeSubscription: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **subscriptionId** | **String**|  | 
 **subscriptionChange** | [**SubscriptionChange**](SubscriptionChange.md)|  | 

### Return type

[**SubscriptionChangeResponseBody**](SubscriptionChangeResponseBody.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **createSubscription**
> Subscription createSubscription(subscriptionCreate)

Create subscription

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getSubscriptionsApi();
final SubscriptionCreate subscriptionCreate = ; // SubscriptionCreate | 

try {
    final response = api.createSubscription(subscriptionCreate);
    print(response);
} catch on DioException (e) {
    print('Exception when calling SubscriptionsApi->createSubscription: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **subscriptionCreate** | [**SubscriptionCreate**](SubscriptionCreate.md)|  | 

### Return type

[**Subscription**](Subscription.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **editSubscription**
> Subscription editSubscription(subscriptionId, subscriptionEdit)

Edit subscription

Batch processing commands for manipulating running subscriptions. The key format is `/phases/{phaseKey}` or `/phases/{phaseKey}/items/{itemKey}`.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getSubscriptionsApi();
final String subscriptionId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 
final SubscriptionEdit subscriptionEdit = ; // SubscriptionEdit | 

try {
    final response = api.editSubscription(subscriptionId, subscriptionEdit);
    print(response);
} catch on DioException (e) {
    print('Exception when calling SubscriptionsApi->editSubscription: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **subscriptionId** | **String**|  | 
 **subscriptionEdit** | [**SubscriptionEdit**](SubscriptionEdit.md)|  | 

### Return type

[**Subscription**](Subscription.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getSubscription**
> SubscriptionExpanded getSubscription(subscriptionId, at)

Get subscription

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getSubscriptionsApi();
final String subscriptionId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 
final DateTime at = 2023-01-01T01:01:01.001Z; // DateTime | The time at which the subscription should be queried. If not provided the current time is used.

try {
    final response = api.getSubscription(subscriptionId, at);
    print(response);
} catch on DioException (e) {
    print('Exception when calling SubscriptionsApi->getSubscription: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **subscriptionId** | **String**|  | 
 **at** | **DateTime**| The time at which the subscription should be queried. If not provided the current time is used. | [optional] 

### Return type

[**SubscriptionExpanded**](SubscriptionExpanded.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **migrateSubscription**
> SubscriptionChangeResponseBody migrateSubscription(subscriptionId, migrateSubscriptionRequest)

Migrate subscription

Migrates the subscripiton to the provided version of the current plan. If possible, the migration will be done immediately. If not, the migration will be scheduled to the end of the current billing period.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getSubscriptionsApi();
final String subscriptionId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 
final MigrateSubscriptionRequest migrateSubscriptionRequest = ; // MigrateSubscriptionRequest | 

try {
    final response = api.migrateSubscription(subscriptionId, migrateSubscriptionRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling SubscriptionsApi->migrateSubscription: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **subscriptionId** | **String**|  | 
 **migrateSubscriptionRequest** | [**MigrateSubscriptionRequest**](MigrateSubscriptionRequest.md)|  | 

### Return type

[**SubscriptionChangeResponseBody**](SubscriptionChangeResponseBody.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **restoreSubscription**
> Subscription restoreSubscription(subscriptionId)

Restore subscription

Restores a canceled subscription. Any subscription scheduled to start later will be deleted and this subscription will be continued indefinitely.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getSubscriptionsApi();
final String subscriptionId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 

try {
    final response = api.restoreSubscription(subscriptionId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling SubscriptionsApi->restoreSubscription: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **subscriptionId** | **String**|  | 

### Return type

[**Subscription**](Subscription.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unscheduleCancelation**
> Subscription unscheduleCancelation(subscriptionId)

Unschedule cancelation

Cancels the scheduled cancelation.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getSubscriptionsApi();
final String subscriptionId = 01G65Z755AFWAKHE12NY0CQ9FH; // String | 

try {
    final response = api.unscheduleCancelation(subscriptionId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling SubscriptionsApi->unscheduleCancelation: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **subscriptionId** | **String**|  | 

### Return type

[**Subscription**](Subscription.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

