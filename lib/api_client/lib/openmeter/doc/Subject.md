# openmeter.model.Subject

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** | A unique identifier for the subject. | 
**key** | **String** | A unique, human-readable identifier for the subject. | 
**displayName** | **String** | A human-readable display name for the subject. | [optional] 
**metadata** | [**BuiltMap&lt;String, JsonObject&gt;**](JsonObject.md) |  | [optional] 
**currentPeriodStart** | [**DateTime**](DateTime.md) | [RFC3339](https://tools.ietf.org/html/rfc3339) formatted date-time string in UTC. | [optional] 
**currentPeriodEnd** | [**DateTime**](DateTime.md) | [RFC3339](https://tools.ietf.org/html/rfc3339) formatted date-time string in UTC. | [optional] 
**stripeCustomerId** | **String** |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


