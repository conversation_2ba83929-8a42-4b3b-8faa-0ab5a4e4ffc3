# openmeter.model.SandboxCustomerAppData

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**app** | [**SandboxApp**](SandboxApp.md) | The installed sandbox app this data belongs to. | [optional] 
**id** | **String** | The app ID. If not provided, it will use the global default for the app type. | [optional] 
**type** | **String** | The app name. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


