# openmeter.model.MeterQueryRow

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**value** | **double** | The aggregated value. | 
**windowStart** | [**DateTime**](DateTime.md) | The start of the window the value is aggregated over. | 
**windowEnd** | [**DateTime**](DateTime.md) | The end of the window the value is aggregated over. | 
**subject** | **String** | The subject the value is aggregated over. If not specified, the value is aggregated over all subjects. | 
**groupBy** | **BuiltMap&lt;String, String&gt;** | The group by values the value is aggregated over. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


