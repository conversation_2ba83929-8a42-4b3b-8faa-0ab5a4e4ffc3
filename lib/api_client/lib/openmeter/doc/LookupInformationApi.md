# openmeter.api.LookupInformationApi

## Load the API package
```dart
import 'package:openmeter/api.dart';
```

All URIs are relative to *https://127.0.0.1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**getProgress**](LookupInformationApi.md#getprogress) | **GET** /api/v1/info/progress/{id} | Get progress
[**listCurrencies**](LookupInformationApi.md#listcurrencies) | **GET** /api/v1/info/currencies | List supported currencies


# **getProgress**
> Progress getProgress(id)

Get progress

Get progress

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getLookupInformationApi();
final String id = id_example; // String | 

try {
    final response = api.getProgress(id);
    print(response);
} catch on DioException (e) {
    print('Exception when calling LookupInformationApi->getProgress: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**Progress**](Progress.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **listCurrencies**
> BuiltList<Currency> listCurrencies()

List supported currencies

List all supported currencies.

### Example
```dart
import 'package:openmeter/api.dart';

final api = Openmeter().getLookupInformationApi();

try {
    final response = api.listCurrencies();
    print(response);
} catch on DioException (e) {
    print('Exception when calling LookupInformationApi->listCurrencies: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;Currency&gt;**](Currency.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json, application/problem+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

