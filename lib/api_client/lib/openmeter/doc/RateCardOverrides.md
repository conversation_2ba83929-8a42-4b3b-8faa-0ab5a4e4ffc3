# openmeter.model.RateCardOverrides

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**add** | [**BuiltList&lt;RateCard&gt;**](RateCard.md) | Add rate cards to the plan. | [optional] 
**remove** | **BuiltList&lt;String&gt;** | Remove rate cards from the plan. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


