# openmeter.model.SvixOperationalWebhookRequest

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **String** | The type of the Svix operational webhook request. | 
**data** | **BuiltMap&lt;String, String&gt;** | The payload of the Svix operational webhook request. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


