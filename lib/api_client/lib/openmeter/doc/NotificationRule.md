# openmeter.model.NotificationRule

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**createdAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was created. | 
**updatedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was last updated. | 
**deletedAt** | [**DateTime**](DateTime.md) | Timestamp of when the resource was permanently deleted. | [optional] 
**id** | **String** | Identifies the notification rule. | 
**type** | **String** | Notification rule type. | 
**name** | **String** | The user friendly name of the notification rule. | 
**disabled** | **bool** | Whether the rule is disabled or not. | [optional] [default to false]
**channels** | [**BuiltList&lt;NotificationChannelMeta&gt;**](NotificationChannelMeta.md) | List of notification channels the rule applies to. | 
**thresholds** | [**BuiltList&lt;NotificationRuleBalanceThresholdValue&gt;**](NotificationRuleBalanceThresholdValue.md) | List of thresholds the rule suppose to be triggered. | 
**features** | [**BuiltList&lt;FeatureMeta&gt;**](FeatureMeta.md) | Optional field containing list of features the rule applies to. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


