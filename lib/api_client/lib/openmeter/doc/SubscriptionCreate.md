# openmeter.model.SubscriptionCreate

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**alignment** | [**Alignment**](Alignment.md) | What alignment settings the subscription should have. | [optional] 
**metadata** | **BuiltMap&lt;String, String&gt;** | Arbitrary metadata associated with the subscription. | [optional] 
**plan** | [**PlanReferenceInput**](PlanReferenceInput.md) | The plan reference to change to. | 
**name** | **String** | The name of the Subscription. If not provided the plan name is used. | [optional] 
**description** | **String** | Description for the Subscription. | [optional] 
**timing** | [**SubscriptionTiming**](SubscriptionTiming.md) | Timing configuration for the change, when the change should take effect. The default is immediate. | [optional] [default to immediate]
**customerId** | **String** | The ID of the customer. Provide either the key or ID. Has presedence over the key. | [optional] 
**customerKey** | **String** | The key of the customer. Provide either the key or ID. | [optional] 
**customPlan** | [**CustomPlanInput**](CustomPlanInput.md) | The custom plan description which defines the Subscription. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


