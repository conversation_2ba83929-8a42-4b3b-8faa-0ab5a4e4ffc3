# openmeter.model.NotificationRuleCreateRequest

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **String** | Notification rule type. | 
**name** | **String** | The user friendly name of the notification rule. | 
**disabled** | **bool** | Whether the rule is disabled or not. | [optional] [default to false]
**thresholds** | [**BuiltList&lt;NotificationRuleBalanceThresholdValue&gt;**](NotificationRuleBalanceThresholdValue.md) | List of thresholds the rule suppose to be triggered. | 
**channels** | **BuiltList&lt;String&gt;** | List of notification channels the rule is applied to. | 
**features** | **BuiltList&lt;String&gt;** | Optional field for defining the scope of notification by feature. It may contain features by id or key. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


