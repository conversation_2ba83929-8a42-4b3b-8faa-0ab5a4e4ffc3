# openmeter.model.SubscriptionPhaseCreate

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**startAfter** | **String** | Interval after the subscription starts to transition to the phase. When null, the phase starts immediately after the subscription starts. | 
**duration** | **String** | The intended duration of the new phase. Duration is required when the phase will not be the last phase. | [optional] 
**discounts** | [**BuiltList&lt;Discount&gt;**](Discount.md) | The discounts on the plan. | [optional] 
**key** | **String** | A locally unique identifier for the phase. | 
**name** | **String** | The name of the phase. | 
**description** | **String** | The description of the phase. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


