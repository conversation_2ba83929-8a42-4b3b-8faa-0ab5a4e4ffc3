# openmeter.model.Progress

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**success** | **int** | Success is the number of items that succeeded | 
**failed** | **int** | Failed is the number of items that failed | 
**total** | **int** | The total number of items to process | 
**updatedAt** | [**DateTime**](DateTime.md) | The time the progress was last updated | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


