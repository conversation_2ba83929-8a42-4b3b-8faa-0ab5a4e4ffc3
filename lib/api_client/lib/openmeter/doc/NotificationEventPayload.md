# openmeter.model.NotificationEventPayload

## Load the model package
```dart
import 'package:openmeter/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** | A unique identifier for the notification event the payload belongs to. | 
**type** | **String** | Type of the notification event. | 
**timestamp** | [**DateTime**](DateTime.md) | Timestamp when the notification event was created in RFC 3339 format. | 
**data** | [**NotificationEventBalanceThresholdPayloadData**](NotificationEventBalanceThresholdPayloadData.md) | The data of the payload. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


