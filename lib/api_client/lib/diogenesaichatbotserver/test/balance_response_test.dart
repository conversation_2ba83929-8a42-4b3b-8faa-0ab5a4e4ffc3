import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for BalanceResponse
void main() {
  final instance = BalanceResponseBuilder();
  // TODO add properties to the builder and call build()

  group(BalanceResponse, () {
    // num balance
    test('to test the property `balance`', () async {
      // TODO
    });

    // String clientSecret
    test('to test the property `clientSecret`', () async {
      // TODO
    });

  });
}
