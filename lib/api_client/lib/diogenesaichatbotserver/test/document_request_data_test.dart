import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for DocumentRequestData
void main() {
  final instance = DocumentRequestDataBuilder();
  // TODO add properties to the builder and call build()

  group(DocumentRequestData, () {
    // String originalUserId
    test('to test the property `originalUserId`', () async {
      // TODO
    });

    // BuiltList<String> documentFirebaseUrls
    test('to test the property `documentFirebaseUrls`', () async {
      // TODO
    });

  });
}
