import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for BodyGetBotDocumentsGetBotDocumentsGet
void main() {
  final instance = BodyGetBotDocumentsGetBotDocumentsGetBuilder();
  // TODO add properties to the builder and call build()

  group(BodyGetBotDocumentsGetBotDocumentsGet, () {
    // String originalBotId
    test('to test the property `originalBotId`', () async {
      // TODO
    });

    // String originalUserId
    test('to test the property `originalUserId`', () async {
      // TODO
    });

  });
}
