import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost
void main() {
  final instance = BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPostBuilder();
  // TODO add properties to the builder and call build()

  group(BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost, () {
    // BuiltList<String> firebaseUrls
    test('to test the property `firebaseUrls`', () async {
      // TODO
    });

    // String originalUserId
    test('to test the property `originalUserId`', () async {
      // TODO
    });

  });
}
