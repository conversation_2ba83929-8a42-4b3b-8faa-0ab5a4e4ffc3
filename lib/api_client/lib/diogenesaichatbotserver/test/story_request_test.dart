import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for StoryRequest
void main() {
  final instance = StoryRequestBuilder();
  // TODO add properties to the builder and call build()

  group(StoryRequest, () {
    // String length
    test('to test the property `length`', () async {
      // TODO
    });

    // String genre
    test('to test the property `genre`', () async {
      // TODO
    });

    // BuiltList<JsonObject> characters
    test('to test the property `characters`', () async {
      // TODO
    });

    // String ageGroup
    test('to test the property `ageGroup`', () async {
      // TODO
    });

    // BuiltList<String> themes
    test('to test the property `themes`', () async {
      // TODO
    });

    // String briefStory
    test('to test the property `briefStory`', () async {
      // TODO
    });

    // String imageStyle
    test('to test the property `imageStyle`', () async {
      // TODO
    });

    // String voice
    test('to test the property `voice`', () async {
      // TODO
    });

  });
}
