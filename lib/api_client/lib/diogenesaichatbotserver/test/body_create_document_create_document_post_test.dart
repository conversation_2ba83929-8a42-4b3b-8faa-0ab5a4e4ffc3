import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for BodyCreateDocumentCreateDocumentPost
void main() {
  final instance = BodyCreateDocumentCreateDocumentPostBuilder();
  // TODO add properties to the builder and call build()

  group(BodyCreateDocumentCreateDocumentPost, () {
    // JsonObject documentData
    test('to test the property `documentData`', () async {
      // TODO
    });

    // String originalUserId
    test('to test the property `originalUserId`', () async {
      // TODO
    });

  });
}
