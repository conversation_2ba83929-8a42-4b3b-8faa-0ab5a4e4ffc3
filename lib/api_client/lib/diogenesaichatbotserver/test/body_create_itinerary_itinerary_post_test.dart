import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for BodyCreateItineraryItineraryPost
void main() {
  final instance = BodyCreateItineraryItineraryPostBuilder();
  // TODO add properties to the builder and call build()

  group(BodyCreateItineraryItineraryPost, () {
    // String interests
    test('to test the property `interests`', () async {
      // TODO
    });

    // String startLocation
    test('to test the property `startLocation`', () async {
      // TODO
    });

    // String endLocation
    test('to test the property `endLocation`', () async {
      // TODO
    });

    // BuiltList<String> places (default value: ListBuilder())
    test('to test the property `places`', () async {
      // TODO
    });

    // bool orderMatters (default value: false)
    test('to test the property `orderMatters`', () async {
      // TODO
    });

    // String startDate
    test('to test the property `startDate`', () async {
      // TODO
    });

    // String endDate
    test('to test the property `endDate`', () async {
      // TODO
    });

    // bool rentCar (default value: true)
    test('to test the property `rentCar`', () async {
      // TODO
    });

    // String travelStyle
    test('to test the property `travelStyle`', () async {
      // TODO
    });

    // BuiltList<String> travelFocus (default value: ListBuilder())
    test('to test the property `travelFocus`', () async {
      // TODO
    });

  });
}
