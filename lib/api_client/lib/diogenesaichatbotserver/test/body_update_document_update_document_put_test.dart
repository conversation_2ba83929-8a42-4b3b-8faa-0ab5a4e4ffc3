import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for BodyUpdateDocumentUpdateDocumentPut
void main() {
  final instance = BodyUpdateDocumentUpdateDocumentPutBuilder();
  // TODO add properties to the builder and call build()

  group(BodyUpdateDocumentUpdateDocumentPut, () {
    // String documentId
    test('to test the property `documentId`', () async {
      // TODO
    });

    // JsonObject updatedData
    test('to test the property `updatedData`', () async {
      // TODO
    });

    // String originalUserId
    test('to test the property `originalUserId`', () async {
      // TODO
    });

  });
}
