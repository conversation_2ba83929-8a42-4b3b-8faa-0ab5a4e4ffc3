import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for ValidationError
void main() {
  final instance = ValidationErrorBuilder();
  // TODO add properties to the builder and call build()

  group(ValidationError, () {
    // BuiltList<ValidationErrorLocInner> loc
    test('to test the property `loc`', () async {
      // TODO
    });

    // String msg
    test('to test the property `msg`', () async {
      // TODO
    });

    // String type
    test('to test the property `type`', () async {
      // TODO
    });

  });
}
