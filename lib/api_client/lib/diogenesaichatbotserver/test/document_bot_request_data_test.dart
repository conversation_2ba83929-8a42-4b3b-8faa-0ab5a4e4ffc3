import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for DocumentBotRequestData
void main() {
  final instance = DocumentBotRequestDataBuilder();
  // TODO add properties to the builder and call build()

  group(DocumentBotRequestData, () {
    // String originalBotId
    test('to test the property `originalBotId`', () async {
      // TODO
    });

    // BuiltList<String> documentFirebaseUrls
    test('to test the property `documentFirebaseUrls`', () async {
      // TODO
    });

    // String originalUserId
    test('to test the property `originalUserId`', () async {
      // TODO
    });

  });
}
