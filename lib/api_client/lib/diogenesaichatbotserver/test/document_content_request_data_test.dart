import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for DocumentContentRequestData
void main() {
  final instance = DocumentContentRequestDataBuilder();
  // TODO add properties to the builder and call build()

  group(DocumentContentRequestData, () {
    // BuiltList<String> firebaseUrls
    test('to test the property `firebaseUrls`', () async {
      // TODO
    });

    // String originalUserId
    test('to test the property `originalUserId`', () async {
      // TODO
    });

  });
}
