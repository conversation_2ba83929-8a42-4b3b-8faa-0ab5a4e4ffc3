import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for HTTPValidationError
void main() {
  final instance = HTTPValidationErrorBuilder();
  // TODO add properties to the builder and call build()

  group(HTTPValidationError, () {
    // BuiltList<ValidationError> detail
    test('to test the property `detail`', () async {
      // TODO
    });

  });
}
