//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'body_delete_document_delete_document_delete.g.dart';

/// BodyDeleteDocumentDeleteDocumentDelete
///
/// Properties:
/// * [documentId] 
/// * [originalUserId] 
@BuiltValue()
abstract class BodyDeleteDocumentDeleteDocumentDelete implements Built<BodyDeleteDocumentDeleteDocumentDelete, BodyDeleteDocumentDeleteDocumentDeleteBuilder> {
  @BuiltValueField(wireName: r'document_id')
  String get documentId;

  @BuiltValueField(wireName: r'original_user_id')
  String get originalUserId;

  BodyDeleteDocumentDeleteDocumentDelete._();

  factory BodyDeleteDocumentDeleteDocumentDelete([void updates(BodyDeleteDocumentDeleteDocumentDeleteBuilder b)]) = _$BodyDeleteDocumentDeleteDocumentDelete;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BodyDeleteDocumentDeleteDocumentDeleteBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BodyDeleteDocumentDeleteDocumentDelete> get serializer => _$BodyDeleteDocumentDeleteDocumentDeleteSerializer();
}

class _$BodyDeleteDocumentDeleteDocumentDeleteSerializer implements PrimitiveSerializer<BodyDeleteDocumentDeleteDocumentDelete> {
  @override
  final Iterable<Type> types = const [BodyDeleteDocumentDeleteDocumentDelete, _$BodyDeleteDocumentDeleteDocumentDelete];

  @override
  final String wireName = r'BodyDeleteDocumentDeleteDocumentDelete';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BodyDeleteDocumentDeleteDocumentDelete object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'document_id';
    yield serializers.serialize(
      object.documentId,
      specifiedType: const FullType(String),
    );
    yield r'original_user_id';
    yield serializers.serialize(
      object.originalUserId,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    BodyDeleteDocumentDeleteDocumentDelete object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BodyDeleteDocumentDeleteDocumentDeleteBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'document_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.documentId = valueDes;
          break;
        case r'original_user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.originalUserId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BodyDeleteDocumentDeleteDocumentDelete deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BodyDeleteDocumentDeleteDocumentDeleteBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

