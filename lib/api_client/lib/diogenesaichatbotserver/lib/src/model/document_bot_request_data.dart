//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'document_bot_request_data.g.dart';

/// DocumentBotRequestData
///
/// Properties:
/// * [originalBotId] 
/// * [documentFirebaseUrls] 
/// * [originalUserId] 
@BuiltValue()
abstract class DocumentBotRequestData implements Built<DocumentBotRequestData, DocumentBotRequestDataBuilder> {
  @BuiltValueField(wireName: r'original_bot_id')
  String get originalBotId;

  @BuiltValueField(wireName: r'document_firebase_urls')
  BuiltList<String>? get documentFirebaseUrls;

  @BuiltValueField(wireName: r'original_user_id')
  String get originalUserId;

  DocumentBotRequestData._();

  factory DocumentBotRequestData([void updates(DocumentBotRequestDataBuilder b)]) = _$DocumentBotRequestData;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(DocumentBotRequestDataBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<DocumentBotRequestData> get serializer => _$DocumentBotRequestDataSerializer();
}

class _$DocumentBotRequestDataSerializer implements PrimitiveSerializer<DocumentBotRequestData> {
  @override
  final Iterable<Type> types = const [DocumentBotRequestData, _$DocumentBotRequestData];

  @override
  final String wireName = r'DocumentBotRequestData';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    DocumentBotRequestData object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'original_bot_id';
    yield serializers.serialize(
      object.originalBotId,
      specifiedType: const FullType(String),
    );
    if (object.documentFirebaseUrls != null) {
      yield r'document_firebase_urls';
      yield serializers.serialize(
        object.documentFirebaseUrls,
        specifiedType: const FullType(BuiltList, [FullType(String)]),
      );
    }
    yield r'original_user_id';
    yield serializers.serialize(
      object.originalUserId,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    DocumentBotRequestData object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required DocumentBotRequestDataBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'original_bot_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.originalBotId = valueDes;
          break;
        case r'document_firebase_urls':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.documentFirebaseUrls.replace(valueDes);
          break;
        case r'original_user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.originalUserId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  DocumentBotRequestData deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = DocumentBotRequestDataBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

