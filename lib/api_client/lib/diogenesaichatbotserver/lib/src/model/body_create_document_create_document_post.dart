//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/json_object.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'body_create_document_create_document_post.g.dart';

/// BodyCreateDocumentCreateDocumentPost
///
/// Properties:
/// * [documentData] 
/// * [originalUserId] 
@BuiltValue()
abstract class BodyCreateDocumentCreateDocumentPost implements Built<BodyCreateDocumentCreateDocumentPost, BodyCreateDocumentCreateDocumentPostBuilder> {
  @BuiltValueField(wireName: r'document_data')
  JsonObject get documentData;

  @BuiltValueField(wireName: r'original_user_id')
  String get originalUserId;

  BodyCreateDocumentCreateDocumentPost._();

  factory BodyCreateDocumentCreateDocumentPost([void updates(BodyCreateDocumentCreateDocumentPostBuilder b)]) = _$BodyCreateDocumentCreateDocumentPost;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BodyCreateDocumentCreateDocumentPostBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BodyCreateDocumentCreateDocumentPost> get serializer => _$BodyCreateDocumentCreateDocumentPostSerializer();
}

class _$BodyCreateDocumentCreateDocumentPostSerializer implements PrimitiveSerializer<BodyCreateDocumentCreateDocumentPost> {
  @override
  final Iterable<Type> types = const [BodyCreateDocumentCreateDocumentPost, _$BodyCreateDocumentCreateDocumentPost];

  @override
  final String wireName = r'BodyCreateDocumentCreateDocumentPost';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BodyCreateDocumentCreateDocumentPost object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'document_data';
    yield serializers.serialize(
      object.documentData,
      specifiedType: const FullType(JsonObject),
    );
    yield r'original_user_id';
    yield serializers.serialize(
      object.originalUserId,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    BodyCreateDocumentCreateDocumentPost object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BodyCreateDocumentCreateDocumentPostBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'document_data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(JsonObject),
          ) as JsonObject;
          result.documentData = valueDes;
          break;
        case r'original_user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.originalUserId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BodyCreateDocumentCreateDocumentPost deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BodyCreateDocumentCreateDocumentPostBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

