//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'body_create_itinerary_itinerary_post.g.dart';

/// BodyCreateItineraryItineraryPost
///
/// Properties:
/// * [interests] 
/// * [startLocation] 
/// * [endLocation] 
/// * [places] 
/// * [orderMatters] 
/// * [startDate] 
/// * [endDate] 
/// * [rentCar] 
/// * [travelStyle] 
/// * [travelFocus] 
@BuiltValue()
abstract class BodyCreateItineraryItineraryPost implements Built<BodyCreateItineraryItineraryPost, BodyCreateItineraryItineraryPostBuilder> {
  @BuiltValueField(wireName: r'interests')
  String get interests;

  @BuiltValueField(wireName: r'start_location')
  String get startLocation;

  @BuiltValueField(wireName: r'end_location')
  String get endLocation;

  @BuiltValueField(wireName: r'places')
  BuiltList<String>? get places;

  @BuiltValueField(wireName: r'order_matters')
  bool? get orderMatters;

  @BuiltValueField(wireName: r'start_date')
  String? get startDate;

  @BuiltValueField(wireName: r'end_date')
  String? get endDate;

  @BuiltValueField(wireName: r'rent_car')
  bool? get rentCar;

  @BuiltValueField(wireName: r'travel_style')
  String get travelStyle;

  @BuiltValueField(wireName: r'travel_focus')
  BuiltList<String>? get travelFocus;

  BodyCreateItineraryItineraryPost._();

  factory BodyCreateItineraryItineraryPost([void updates(BodyCreateItineraryItineraryPostBuilder b)]) = _$BodyCreateItineraryItineraryPost;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BodyCreateItineraryItineraryPostBuilder b) => b
      ..places = ListBuilder()
      ..orderMatters = false
      ..rentCar = true
      ..travelFocus = ListBuilder();

  @BuiltValueSerializer(custom: true)
  static Serializer<BodyCreateItineraryItineraryPost> get serializer => _$BodyCreateItineraryItineraryPostSerializer();
}

class _$BodyCreateItineraryItineraryPostSerializer implements PrimitiveSerializer<BodyCreateItineraryItineraryPost> {
  @override
  final Iterable<Type> types = const [BodyCreateItineraryItineraryPost, _$BodyCreateItineraryItineraryPost];

  @override
  final String wireName = r'BodyCreateItineraryItineraryPost';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BodyCreateItineraryItineraryPost object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'interests';
    yield serializers.serialize(
      object.interests,
      specifiedType: const FullType(String),
    );
    yield r'start_location';
    yield serializers.serialize(
      object.startLocation,
      specifiedType: const FullType(String),
    );
    yield r'end_location';
    yield serializers.serialize(
      object.endLocation,
      specifiedType: const FullType(String),
    );
    if (object.places != null) {
      yield r'places';
      yield serializers.serialize(
        object.places,
        specifiedType: const FullType(BuiltList, [FullType(String)]),
      );
    }
    if (object.orderMatters != null) {
      yield r'order_matters';
      yield serializers.serialize(
        object.orderMatters,
        specifiedType: const FullType(bool),
      );
    }
    if (object.startDate != null) {
      yield r'start_date';
      yield serializers.serialize(
        object.startDate,
        specifiedType: const FullType(String),
      );
    }
    if (object.endDate != null) {
      yield r'end_date';
      yield serializers.serialize(
        object.endDate,
        specifiedType: const FullType(String),
      );
    }
    if (object.rentCar != null) {
      yield r'rent_car';
      yield serializers.serialize(
        object.rentCar,
        specifiedType: const FullType(bool),
      );
    }
    yield r'travel_style';
    yield serializers.serialize(
      object.travelStyle,
      specifiedType: const FullType(String),
    );
    if (object.travelFocus != null) {
      yield r'travel_focus';
      yield serializers.serialize(
        object.travelFocus,
        specifiedType: const FullType(BuiltList, [FullType(String)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    BodyCreateItineraryItineraryPost object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BodyCreateItineraryItineraryPostBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'interests':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.interests = valueDes;
          break;
        case r'start_location':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.startLocation = valueDes;
          break;
        case r'end_location':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.endLocation = valueDes;
          break;
        case r'places':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.places.replace(valueDes);
          break;
        case r'order_matters':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.orderMatters = valueDes;
          break;
        case r'start_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.startDate = valueDes;
          break;
        case r'end_date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.endDate = valueDes;
          break;
        case r'rent_car':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.rentCar = valueDes;
          break;
        case r'travel_style':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.travelStyle = valueDes;
          break;
        case r'travel_focus':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.travelFocus.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BodyCreateItineraryItineraryPost deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BodyCreateItineraryItineraryPostBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

