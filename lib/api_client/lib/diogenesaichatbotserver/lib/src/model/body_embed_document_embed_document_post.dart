//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'body_embed_document_embed_document_post.g.dart';

/// BodyEmbedDocumentEmbedDocumentPost
///
/// Properties:
/// * [firebaseUrls] 
/// * [userId] 
/// * [boolUpdateOnExist] 
/// * [optionalPredefinedContentTofill] 
@BuiltValue()
abstract class BodyEmbedDocumentEmbedDocumentPost implements Built<BodyEmbedDocumentEmbedDocumentPost, BodyEmbedDocumentEmbedDocumentPostBuilder> {
  @BuiltValueField(wireName: r'firebase_urls')
  BuiltList<String> get firebaseUrls;

  @BuiltValueField(wireName: r'user_id')
  String get userId;

  @BuiltValueField(wireName: r'bool_updateOnExist')
  bool get boolUpdateOnExist;

  @BuiltValueField(wireName: r'optional_predefined_content_tofill')
  BuiltList<String> get optionalPredefinedContentTofill;

  BodyEmbedDocumentEmbedDocumentPost._();

  factory BodyEmbedDocumentEmbedDocumentPost([void updates(BodyEmbedDocumentEmbedDocumentPostBuilder b)]) = _$BodyEmbedDocumentEmbedDocumentPost;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BodyEmbedDocumentEmbedDocumentPostBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BodyEmbedDocumentEmbedDocumentPost> get serializer => _$BodyEmbedDocumentEmbedDocumentPostSerializer();
}

class _$BodyEmbedDocumentEmbedDocumentPostSerializer implements PrimitiveSerializer<BodyEmbedDocumentEmbedDocumentPost> {
  @override
  final Iterable<Type> types = const [BodyEmbedDocumentEmbedDocumentPost, _$BodyEmbedDocumentEmbedDocumentPost];

  @override
  final String wireName = r'BodyEmbedDocumentEmbedDocumentPost';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BodyEmbedDocumentEmbedDocumentPost object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'firebase_urls';
    yield serializers.serialize(
      object.firebaseUrls,
      specifiedType: const FullType(BuiltList, [FullType(String)]),
    );
    yield r'user_id';
    yield serializers.serialize(
      object.userId,
      specifiedType: const FullType(String),
    );
    yield r'bool_updateOnExist';
    yield serializers.serialize(
      object.boolUpdateOnExist,
      specifiedType: const FullType(bool),
    );
    yield r'optional_predefined_content_tofill';
    yield serializers.serialize(
      object.optionalPredefinedContentTofill,
      specifiedType: const FullType(BuiltList, [FullType(String)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    BodyEmbedDocumentEmbedDocumentPost object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BodyEmbedDocumentEmbedDocumentPostBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'firebase_urls':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.firebaseUrls.replace(valueDes);
          break;
        case r'user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.userId = valueDes;
          break;
        case r'bool_updateOnExist':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.boolUpdateOnExist = valueDes;
          break;
        case r'optional_predefined_content_tofill':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.optionalPredefinedContentTofill.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BodyEmbedDocumentEmbedDocumentPost deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BodyEmbedDocumentEmbedDocumentPostBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

