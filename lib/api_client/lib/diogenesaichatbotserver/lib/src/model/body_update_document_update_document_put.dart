//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/json_object.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'body_update_document_update_document_put.g.dart';

/// BodyUpdateDocumentUpdateDocumentPut
///
/// Properties:
/// * [documentId] 
/// * [updatedData] 
/// * [originalUserId] 
@BuiltValue()
abstract class BodyUpdateDocumentUpdateDocumentPut implements Built<BodyUpdateDocumentUpdateDocumentPut, BodyUpdateDocumentUpdateDocumentPutBuilder> {
  @BuiltValueField(wireName: r'document_id')
  String get documentId;

  @BuiltValueField(wireName: r'updated_data')
  JsonObject get updatedData;

  @BuiltValueField(wireName: r'original_user_id')
  String get originalUserId;

  BodyUpdateDocumentUpdateDocumentPut._();

  factory BodyUpdateDocumentUpdateDocumentPut([void updates(BodyUpdateDocumentUpdateDocumentPutBuilder b)]) = _$BodyUpdateDocumentUpdateDocumentPut;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BodyUpdateDocumentUpdateDocumentPutBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BodyUpdateDocumentUpdateDocumentPut> get serializer => _$BodyUpdateDocumentUpdateDocumentPutSerializer();
}

class _$BodyUpdateDocumentUpdateDocumentPutSerializer implements PrimitiveSerializer<BodyUpdateDocumentUpdateDocumentPut> {
  @override
  final Iterable<Type> types = const [BodyUpdateDocumentUpdateDocumentPut, _$BodyUpdateDocumentUpdateDocumentPut];

  @override
  final String wireName = r'BodyUpdateDocumentUpdateDocumentPut';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BodyUpdateDocumentUpdateDocumentPut object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'document_id';
    yield serializers.serialize(
      object.documentId,
      specifiedType: const FullType(String),
    );
    yield r'updated_data';
    yield serializers.serialize(
      object.updatedData,
      specifiedType: const FullType(JsonObject),
    );
    yield r'original_user_id';
    yield serializers.serialize(
      object.originalUserId,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    BodyUpdateDocumentUpdateDocumentPut object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BodyUpdateDocumentUpdateDocumentPutBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'document_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.documentId = valueDes;
          break;
        case r'updated_data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(JsonObject),
          ) as JsonObject;
          result.updatedData = valueDes;
          break;
        case r'original_user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.originalUserId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BodyUpdateDocumentUpdateDocumentPut deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BodyUpdateDocumentUpdateDocumentPutBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

