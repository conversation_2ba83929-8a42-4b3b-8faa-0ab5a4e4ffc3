//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'document_request_data.g.dart';

/// DocumentRequestData
///
/// Properties:
/// * [originalUserId] 
/// * [documentFirebaseUrls] 
@BuiltValue()
abstract class DocumentRequestData implements Built<DocumentRequestData, DocumentRequestDataBuilder> {
  @BuiltValueField(wireName: r'original_user_id')
  String get originalUserId;

  @BuiltValueField(wireName: r'document_firebase_urls')
  BuiltList<String>? get documentFirebaseUrls;

  DocumentRequestData._();

  factory DocumentRequestData([void updates(DocumentRequestDataBuilder b)]) = _$DocumentRequestData;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(DocumentRequestDataBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<DocumentRequestData> get serializer => _$DocumentRequestDataSerializer();
}

class _$DocumentRequestDataSerializer implements PrimitiveSerializer<DocumentRequestData> {
  @override
  final Iterable<Type> types = const [DocumentRequestData, _$DocumentRequestData];

  @override
  final String wireName = r'DocumentRequestData';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    DocumentRequestData object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'original_user_id';
    yield serializers.serialize(
      object.originalUserId,
      specifiedType: const FullType(String),
    );
    if (object.documentFirebaseUrls != null) {
      yield r'document_firebase_urls';
      yield serializers.serialize(
        object.documentFirebaseUrls,
        specifiedType: const FullType(BuiltList, [FullType(String)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    DocumentRequestData object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required DocumentRequestDataBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'original_user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.originalUserId = valueDes;
          break;
        case r'document_firebase_urls':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.documentFirebaseUrls.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  DocumentRequestData deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = DocumentRequestDataBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

