//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'body_delete_documents_by_firebase_urls_delete_documents_by_firebase_urls_post.g.dart';

/// BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost
///
/// Properties:
/// * [firebaseUrls] 
/// * [originalUserId] 
@BuiltValue()
abstract class BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost implements Built<BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost, BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPostBuilder> {
  @BuiltValueField(wireName: r'firebase_urls')
  BuiltList<String> get firebaseUrls;

  @BuiltValueField(wireName: r'original_user_id')
  String get originalUserId;

  BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost._();

  factory BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost([void updates(BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPostBuilder b)]) = _$BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPostBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost> get serializer => _$BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPostSerializer();
}

class _$BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPostSerializer implements PrimitiveSerializer<BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost> {
  @override
  final Iterable<Type> types = const [BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost, _$BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost];

  @override
  final String wireName = r'BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'firebase_urls';
    yield serializers.serialize(
      object.firebaseUrls,
      specifiedType: const FullType(BuiltList, [FullType(String)]),
    );
    yield r'original_user_id';
    yield serializers.serialize(
      object.originalUserId,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPostBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'firebase_urls':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.firebaseUrls.replace(valueDes);
          break;
        case r'original_user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.originalUserId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPostBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

