//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'document_content_request_data.g.dart';

/// DocumentContentRequestData
///
/// Properties:
/// * [firebaseUrls] 
/// * [originalUserId] 
@BuiltValue()
abstract class DocumentContentRequestData implements Built<DocumentContentRequestData, DocumentContentRequestDataBuilder> {
  @BuiltValueField(wireName: r'firebase_urls')
  BuiltList<String>? get firebaseUrls;

  @BuiltValueField(wireName: r'original_user_id')
  String get originalUserId;

  DocumentContentRequestData._();

  factory DocumentContentRequestData([void updates(DocumentContentRequestDataBuilder b)]) = _$DocumentContentRequestData;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(DocumentContentRequestDataBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<DocumentContentRequestData> get serializer => _$DocumentContentRequestDataSerializer();
}

class _$DocumentContentRequestDataSerializer implements PrimitiveSerializer<DocumentContentRequestData> {
  @override
  final Iterable<Type> types = const [DocumentContentRequestData, _$DocumentContentRequestData];

  @override
  final String wireName = r'DocumentContentRequestData';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    DocumentContentRequestData object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.firebaseUrls != null) {
      yield r'firebase_urls';
      yield serializers.serialize(
        object.firebaseUrls,
        specifiedType: const FullType(BuiltList, [FullType(String)]),
      );
    }
    yield r'original_user_id';
    yield serializers.serialize(
      object.originalUserId,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    DocumentContentRequestData object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required DocumentContentRequestDataBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'firebase_urls':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.firebaseUrls.replace(valueDes);
          break;
        case r'original_user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.originalUserId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  DocumentContentRequestData deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = DocumentContentRequestDataBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

