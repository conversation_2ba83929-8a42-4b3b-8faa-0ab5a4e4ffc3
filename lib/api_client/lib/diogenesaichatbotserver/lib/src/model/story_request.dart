//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/json_object.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'story_request.g.dart';

/// StoryRequest
///
/// Properties:
/// * [length] 
/// * [genre] 
/// * [characters] 
/// * [ageGroup] 
/// * [themes] 
/// * [briefStory] 
/// * [imageStyle] 
/// * [voice] 
@BuiltValue()
abstract class StoryRequest implements Built<StoryRequest, StoryRequestBuilder> {
  @BuiltValueField(wireName: r'length')
  String get length;

  @BuiltValueField(wireName: r'genre')
  String get genre;

  @BuiltValueField(wireName: r'characters')
  BuiltList<JsonObject?> get characters;

  @BuiltValueField(wireName: r'age_group')
  String get ageGroup;

  @BuiltValueField(wireName: r'themes')
  BuiltList<String> get themes;

  @BuiltValueField(wireName: r'brief_story')
  String get briefStory;

  @BuiltValueField(wireName: r'image_style')
  String get imageStyle;

  @BuiltValueField(wireName: r'voice')
  StoryRequestVoiceEnum get voice;
  // enum voiceEnum {  alloy,  echo,  fable,  onyx,  nova,  shimmer,  };

  StoryRequest._();

  factory StoryRequest([void updates(StoryRequestBuilder b)]) = _$StoryRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(StoryRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<StoryRequest> get serializer => _$StoryRequestSerializer();
}

class _$StoryRequestSerializer implements PrimitiveSerializer<StoryRequest> {
  @override
  final Iterable<Type> types = const [StoryRequest, _$StoryRequest];

  @override
  final String wireName = r'StoryRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    StoryRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'length';
    yield serializers.serialize(
      object.length,
      specifiedType: const FullType(String),
    );
    yield r'genre';
    yield serializers.serialize(
      object.genre,
      specifiedType: const FullType(String),
    );
    yield r'characters';
    yield serializers.serialize(
      object.characters,
      specifiedType: const FullType(BuiltList, [FullType.nullable(JsonObject)]),
    );
    yield r'age_group';
    yield serializers.serialize(
      object.ageGroup,
      specifiedType: const FullType(String),
    );
    yield r'themes';
    yield serializers.serialize(
      object.themes,
      specifiedType: const FullType(BuiltList, [FullType(String)]),
    );
    yield r'brief_story';
    yield serializers.serialize(
      object.briefStory,
      specifiedType: const FullType(String),
    );
    yield r'image_style';
    yield serializers.serialize(
      object.imageStyle,
      specifiedType: const FullType(String),
    );
    yield r'voice';
    yield serializers.serialize(
      object.voice,
      specifiedType: const FullType(StoryRequestVoiceEnum),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    StoryRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required StoryRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'length':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.length = valueDes;
          break;
        case r'genre':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.genre = valueDes;
          break;
        case r'characters':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType.nullable(JsonObject)]),
          ) as BuiltList<JsonObject?>;
          result.characters.replace(valueDes);
          break;
        case r'age_group':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.ageGroup = valueDes;
          break;
        case r'themes':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.themes.replace(valueDes);
          break;
        case r'brief_story':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.briefStory = valueDes;
          break;
        case r'image_style':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.imageStyle = valueDes;
          break;
        case r'voice':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(StoryRequestVoiceEnum),
          ) as StoryRequestVoiceEnum;
          result.voice = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  StoryRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = StoryRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class StoryRequestVoiceEnum extends EnumClass {

  @BuiltValueEnumConst(wireName: r'alloy')
  static const StoryRequestVoiceEnum alloy = _$storyRequestVoiceEnum_alloy;
  @BuiltValueEnumConst(wireName: r'echo')
  static const StoryRequestVoiceEnum echo = _$storyRequestVoiceEnum_echo;
  @BuiltValueEnumConst(wireName: r'fable')
  static const StoryRequestVoiceEnum fable = _$storyRequestVoiceEnum_fable;
  @BuiltValueEnumConst(wireName: r'onyx')
  static const StoryRequestVoiceEnum onyx = _$storyRequestVoiceEnum_onyx;
  @BuiltValueEnumConst(wireName: r'nova')
  static const StoryRequestVoiceEnum nova = _$storyRequestVoiceEnum_nova;
  @BuiltValueEnumConst(wireName: r'shimmer')
  static const StoryRequestVoiceEnum shimmer = _$storyRequestVoiceEnum_shimmer;

  static Serializer<StoryRequestVoiceEnum> get serializer => _$storyRequestVoiceEnumSerializer;

  const StoryRequestVoiceEnum._(String name): super(name);

  static BuiltSet<StoryRequestVoiceEnum> get values => _$storyRequestVoiceEnumValues;
  static StoryRequestVoiceEnum valueOf(String name) => _$storyRequestVoiceEnumValueOf(name);
}

