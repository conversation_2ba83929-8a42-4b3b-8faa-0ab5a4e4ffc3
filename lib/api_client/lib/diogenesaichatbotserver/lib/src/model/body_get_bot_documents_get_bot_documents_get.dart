//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'body_get_bot_documents_get_bot_documents_get.g.dart';

/// BodyGetBotDocumentsGetBotDocumentsGet
///
/// Properties:
/// * [originalBotId] 
/// * [originalUserId] 
@BuiltValue()
abstract class BodyGetBotDocumentsGetBotDocumentsGet implements Built<BodyGetBotDocumentsGetBotDocumentsGet, BodyGetBotDocumentsGetBotDocumentsGetBuilder> {
  @BuiltValueField(wireName: r'original_bot_id')
  String get originalBotId;

  @BuiltValueField(wireName: r'original_user_id')
  String get originalUserId;

  BodyGetBotDocumentsGetBotDocumentsGet._();

  factory BodyGetBotDocumentsGetBotDocumentsGet([void updates(BodyGetBotDocumentsGetBotDocumentsGetBuilder b)]) = _$BodyGetBotDocumentsGetBotDocumentsGet;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BodyGetBotDocumentsGetBotDocumentsGetBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BodyGetBotDocumentsGetBotDocumentsGet> get serializer => _$BodyGetBotDocumentsGetBotDocumentsGetSerializer();
}

class _$BodyGetBotDocumentsGetBotDocumentsGetSerializer implements PrimitiveSerializer<BodyGetBotDocumentsGetBotDocumentsGet> {
  @override
  final Iterable<Type> types = const [BodyGetBotDocumentsGetBotDocumentsGet, _$BodyGetBotDocumentsGetBotDocumentsGet];

  @override
  final String wireName = r'BodyGetBotDocumentsGetBotDocumentsGet';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BodyGetBotDocumentsGetBotDocumentsGet object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'original_bot_id';
    yield serializers.serialize(
      object.originalBotId,
      specifiedType: const FullType(String),
    );
    yield r'original_user_id';
    yield serializers.serialize(
      object.originalUserId,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    BodyGetBotDocumentsGetBotDocumentsGet object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BodyGetBotDocumentsGetBotDocumentsGetBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'original_bot_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.originalBotId = valueDes;
          break;
        case r'original_user_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.originalUserId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BodyGetBotDocumentsGetBotDocumentsGet deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BodyGetBotDocumentsGetBotDocumentsGetBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

