//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_import

import 'package:one_of_serializer/any_of_serializer.dart';
import 'package:one_of_serializer/one_of_serializer.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/json_object.dart';
import 'package:built_value/serializer.dart';
import 'package:built_value/standard_json_plugin.dart';
import 'package:built_value/iso_8601_date_time_serializer.dart';
import 'package:diogenesaichatbotserver/src/date_serializer.dart';
import 'package:diogenesaichatbotserver/src/model/date.dart';

import 'package:diogenesaichatbotserver/src/model/balance_request.dart';
import 'package:diogenesaichatbotserver/src/model/balance_response.dart';
import 'package:diogenesaichatbotserver/src/model/body_create_document_create_document_post.dart';
import 'package:diogenesaichatbotserver/src/model/body_create_itinerary_itinerary_post.dart';
import 'package:diogenesaichatbotserver/src/model/body_delete_document_delete_document_delete.dart';
import 'package:diogenesaichatbotserver/src/model/body_delete_documents_by_firebase_urls_delete_documents_by_firebase_urls_post.dart';
import 'package:diogenesaichatbotserver/src/model/body_embed_document_embed_document_post.dart';
import 'package:diogenesaichatbotserver/src/model/body_get_bot_documents_get_bot_documents_get.dart';
import 'package:diogenesaichatbotserver/src/model/body_update_document_update_document_put.dart';
import 'package:diogenesaichatbotserver/src/model/document_bot_request_data.dart';
import 'package:diogenesaichatbotserver/src/model/document_content_request_data.dart';
import 'package:diogenesaichatbotserver/src/model/document_request_data.dart';
import 'package:diogenesaichatbotserver/src/model/http_validation_error.dart';
import 'package:diogenesaichatbotserver/src/model/story_request.dart';
import 'package:diogenesaichatbotserver/src/model/validation_error.dart';
import 'package:diogenesaichatbotserver/src/model/validation_error_loc_inner.dart';

part 'serializers.g.dart';

@SerializersFor([
  BalanceRequest,
  BalanceResponse,
  BodyCreateDocumentCreateDocumentPost,
  BodyCreateItineraryItineraryPost,
  BodyDeleteDocumentDeleteDocumentDelete,
  BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost,
  BodyEmbedDocumentEmbedDocumentPost,
  BodyGetBotDocumentsGetBotDocumentsGet,
  BodyUpdateDocumentUpdateDocumentPut,
  DocumentBotRequestData,
  DocumentContentRequestData,
  DocumentRequestData,
  HTTPValidationError,
  StoryRequest,
  ValidationError,
  ValidationErrorLocInner,
])
Serializers serializers = (_$serializers.toBuilder()
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(num)]),
        () => ListBuilder<num>(),
      )
      ..add(const OneOfSerializer())
      ..add(const AnyOfSerializer())
      ..add(const DateSerializer())
      ..add(Iso8601DateTimeSerializer()))
    .build();

Serializers standardSerializers =
    (serializers.toBuilder()..addPlugin(StandardJsonPlugin())).build();
