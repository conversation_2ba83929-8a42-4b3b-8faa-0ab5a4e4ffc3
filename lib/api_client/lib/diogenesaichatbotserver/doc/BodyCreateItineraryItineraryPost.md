# diogenesaichatbotserver.model.BodyCreateItineraryItineraryPost

## Load the model package
```dart
import 'package:diogenesaichatbotserver/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**interests** | **String** |  | 
**startLocation** | **String** |  | 
**endLocation** | **String** |  | 
**places** | **BuiltList&lt;String&gt;** |  | [optional] [default to ListBuilder()]
**orderMatters** | **bool** |  | [optional] [default to false]
**startDate** | **String** |  | [optional] 
**endDate** | **String** |  | [optional] 
**rentCar** | **bool** |  | [optional] [default to true]
**travelStyle** | **String** |  | 
**travelFocus** | **BuiltList&lt;String&gt;** |  | [optional] [default to ListBuilder()]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


