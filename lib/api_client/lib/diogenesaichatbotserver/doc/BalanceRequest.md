# diogenesaichatbotserver.model.BalanceRequest

## Load the model package
```dart
import 'package:diogenesaichatbotserver/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **String** |  | 
**userId** | **String** |  | 
**currency** | **String** |  | [optional] [default to 'usd']
**amount** | **num** |  | [optional] 
**paymentMethodTypes** | **BuiltList&lt;String&gt;** |  | [optional] [default to ListBuilder()]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


