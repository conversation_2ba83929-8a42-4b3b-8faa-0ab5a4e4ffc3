# diogenesaichatbotserver.api.DefaultApi

## Load the API package
```dart
import 'package:diogenesaichatbotserver/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**assignDocumentsToBotAssignDocumentsToBotPost**](DefaultApi.md#assigndocumentstobotassigndocumentstobotpost) | **POST** /assign_documents_to_bot | Assign Documents To Bot
[**createDocumentCreateDocumentPost**](DefaultApi.md#createdocumentcreatedocumentpost) | **POST** /create_document | Create Document
[**createItineraryItineraryPost**](DefaultApi.md#createitineraryitinerarypost) | **POST** /itinerary/ | Create Itinerary
[**createPaymentIntentCreatePaymentIntentPost**](DefaultApi.md#createpaymentintentcreatepaymentintentpost) | **POST** /create-payment-intent | Create Payment Intent
[**createPaymentSheetPaymentSheetPost**](DefaultApi.md#createpaymentsheetpaymentsheetpost) | **POST** /payment-sheet | Create Payment Sheet
[**deleteDocumentDeleteDocumentDelete**](DefaultApi.md#deletedocumentdeletedocumentdelete) | **DELETE** /delete_document | Delete Document
[**deleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost**](DefaultApi.md#deletedocumentsbyfirebaseurlsdeletedocumentsbyfirebaseurlspost) | **POST** /delete_documents_by_firebase_urls | Delete Documents By Firebase Urls
[**documentExistsDocumentExistsGet**](DefaultApi.md#documentexistsdocumentexistsget) | **GET** /document_exists | Document Exists
[**embedDocumentEmbedDocumentPost**](DefaultApi.md#embeddocumentembeddocumentpost) | **POST** /embed_document | Embed Document
[**generateAudioApiGenerateAudioPost**](DefaultApi.md#generateaudioapigenerateaudiopost) | **POST** /generate_audio | Generate Audio Api
[**generateStoryGenerateStoryPost**](DefaultApi.md#generatestorygeneratestorypost) | **POST** /generate-story/ | Generate Story
[**getBotDocumentsGetBotDocumentsGet**](DefaultApi.md#getbotdocumentsgetbotdocumentsget) | **GET** /get_bot_documents | Get Bot Documents
[**getDocumentContentGetDocumentContentPost**](DefaultApi.md#getdocumentcontentgetdocumentcontentpost) | **POST** /get_document_content | Get Document Content
[**getDocumentIdsByFirebaseUrlsGetDocumentIdsByFirebaseUrlsPost**](DefaultApi.md#getdocumentidsbyfirebaseurlsgetdocumentidsbyfirebaseurlspost) | **POST** /get_document_ids_by_firebase_urls | Get Document Ids By Firebase Urls
[**getTokenGetTokenGet**](DefaultApi.md#gettokengettokenget) | **GET** /getToken | Get Token
[**handlePaymentRequestPaymentPost**](DefaultApi.md#handlepaymentrequestpaymentpost) | **POST** /payment | Handle Payment Request
[**indexGet**](DefaultApi.md#indexget) | **GET** / | Index
[**indexPost**](DefaultApi.md#indexpost) | **POST** / | Index
[**removeDocumentsFromBotRemoveDocumentsFromBotPost**](DefaultApi.md#removedocumentsfrombotremovedocumentsfrombotpost) | **POST** /remove_documents_from_bot | Remove Documents From Bot
[**searchDocumentByEmbeddingSearchDocumentByEmbeddingGet**](DefaultApi.md#searchdocumentbyembeddingsearchdocumentbyembeddingget) | **GET** /search_document_by_embedding | Search Document By Embedding
[**searchDocumentSearchDocumentGet**](DefaultApi.md#searchdocumentsearchdocumentget) | **GET** /search_document | Search Document
[**startCrewaiTaskStartCrewPost**](DefaultApi.md#startcrewaitaskstartcrewpost) | **POST** /start_crew | Start Crewai Task
[**stripeWebhookWebhooksPost**](DefaultApi.md#stripewebhookwebhookspost) | **POST** /webhooks | Stripe Webhook
[**updateDocumentUpdateDocumentPut**](DefaultApi.md#updatedocumentupdatedocumentput) | **PUT** /update_document | Update Document
[**writingSyncWritingSyncPost**](DefaultApi.md#writingsyncwritingsyncpost) | **POST** /writing_sync | Writing Sync
[**writingWritingPost**](DefaultApi.md#writingwritingpost) | **POST** /writing | Writing


# **assignDocumentsToBotAssignDocumentsToBotPost**
> JsonObject assignDocumentsToBotAssignDocumentsToBotPost(documentBotRequestData, authorization)

Assign Documents To Bot

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final DocumentBotRequestData documentBotRequestData = ; // DocumentBotRequestData | 
final String authorization = authorization_example; // String | 

try {
    final response = api.assignDocumentsToBotAssignDocumentsToBotPost(documentBotRequestData, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->assignDocumentsToBotAssignDocumentsToBotPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **documentBotRequestData** | [**DocumentBotRequestData**](DocumentBotRequestData.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **createDocumentCreateDocumentPost**
> JsonObject createDocumentCreateDocumentPost(bodyCreateDocumentCreateDocumentPost, authorization)

Create Document

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final BodyCreateDocumentCreateDocumentPost bodyCreateDocumentCreateDocumentPost = ; // BodyCreateDocumentCreateDocumentPost | 
final String authorization = authorization_example; // String | 

try {
    final response = api.createDocumentCreateDocumentPost(bodyCreateDocumentCreateDocumentPost, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->createDocumentCreateDocumentPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **bodyCreateDocumentCreateDocumentPost** | [**BodyCreateDocumentCreateDocumentPost**](BodyCreateDocumentCreateDocumentPost.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **createItineraryItineraryPost**
> JsonObject createItineraryItineraryPost(bodyCreateItineraryItineraryPost, authorization)

Create Itinerary

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final BodyCreateItineraryItineraryPost bodyCreateItineraryItineraryPost = ; // BodyCreateItineraryItineraryPost | 
final String authorization = authorization_example; // String | 

try {
    final response = api.createItineraryItineraryPost(bodyCreateItineraryItineraryPost, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->createItineraryItineraryPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **bodyCreateItineraryItineraryPost** | [**BodyCreateItineraryItineraryPost**](BodyCreateItineraryItineraryPost.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **createPaymentIntentCreatePaymentIntentPost**
> BalanceResponse createPaymentIntentCreatePaymentIntentPost(balanceRequest, authorization)

Create Payment Intent

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final BalanceRequest balanceRequest = ; // BalanceRequest | 
final String authorization = authorization_example; // String | 

try {
    final response = api.createPaymentIntentCreatePaymentIntentPost(balanceRequest, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->createPaymentIntentCreatePaymentIntentPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **balanceRequest** | [**BalanceRequest**](BalanceRequest.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**BalanceResponse**](BalanceResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **createPaymentSheetPaymentSheetPost**
> BalanceResponse createPaymentSheetPaymentSheetPost(balanceRequest, authorization)

Create Payment Sheet

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final BalanceRequest balanceRequest = ; // BalanceRequest | 
final String authorization = authorization_example; // String | 

try {
    final response = api.createPaymentSheetPaymentSheetPost(balanceRequest, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->createPaymentSheetPaymentSheetPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **balanceRequest** | [**BalanceRequest**](BalanceRequest.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**BalanceResponse**](BalanceResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteDocumentDeleteDocumentDelete**
> JsonObject deleteDocumentDeleteDocumentDelete(bodyDeleteDocumentDeleteDocumentDelete, authorization)

Delete Document

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final BodyDeleteDocumentDeleteDocumentDelete bodyDeleteDocumentDeleteDocumentDelete = ; // BodyDeleteDocumentDeleteDocumentDelete | 
final String authorization = authorization_example; // String | 

try {
    final response = api.deleteDocumentDeleteDocumentDelete(bodyDeleteDocumentDeleteDocumentDelete, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->deleteDocumentDeleteDocumentDelete: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **bodyDeleteDocumentDeleteDocumentDelete** | [**BodyDeleteDocumentDeleteDocumentDelete**](BodyDeleteDocumentDeleteDocumentDelete.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost**
> JsonObject deleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost(bodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost, authorization)

Delete Documents By Firebase Urls

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost bodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost = ; // BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost | 
final String authorization = authorization_example; // String | 

try {
    final response = api.deleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost(bodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->deleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **bodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost** | [**BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost**](BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **documentExistsDocumentExistsGet**
> JsonObject documentExistsDocumentExistsGet(documentId, originalUserId, authorization)

Document Exists

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final String documentId = documentId_example; // String | 
final String originalUserId = originalUserId_example; // String | 
final String authorization = authorization_example; // String | 

try {
    final response = api.documentExistsDocumentExistsGet(documentId, originalUserId, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->documentExistsDocumentExistsGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **documentId** | **String**|  | 
 **originalUserId** | **String**|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **embedDocumentEmbedDocumentPost**
> JsonObject embedDocumentEmbedDocumentPost(bodyEmbedDocumentEmbedDocumentPost, authorization)

Embed Document

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final BodyEmbedDocumentEmbedDocumentPost bodyEmbedDocumentEmbedDocumentPost = ; // BodyEmbedDocumentEmbedDocumentPost | 
final String authorization = authorization_example; // String | 

try {
    final response = api.embedDocumentEmbedDocumentPost(bodyEmbedDocumentEmbedDocumentPost, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->embedDocumentEmbedDocumentPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **bodyEmbedDocumentEmbedDocumentPost** | [**BodyEmbedDocumentEmbedDocumentPost**](BodyEmbedDocumentEmbedDocumentPost.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **generateAudioApiGenerateAudioPost**
> JsonObject generateAudioApiGenerateAudioPost(files, authorization, textModel, audioModel, speaker1Voice, speaker2Voice, introInstructions, textInstructions, scratchPadInstructions, preludeDialog, podcastDialogInstructions, editedTranscript, userFeedback)

Generate Audio Api

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final BuiltList<MultipartFile> files = /path/to/file.txt; // BuiltList<MultipartFile> | 
final String authorization = authorization_example; // String | 
final String textModel = textModel_example; // String | 
final String audioModel = audioModel_example; // String | 
final String speaker1Voice = speaker1Voice_example; // String | 
final String speaker2Voice = speaker2Voice_example; // String | 
final String introInstructions = introInstructions_example; // String | 
final String textInstructions = textInstructions_example; // String | 
final String scratchPadInstructions = scratchPadInstructions_example; // String | 
final String preludeDialog = preludeDialog_example; // String | 
final String podcastDialogInstructions = podcastDialogInstructions_example; // String | 
final String editedTranscript = editedTranscript_example; // String | 
final String userFeedback = userFeedback_example; // String | 

try {
    final response = api.generateAudioApiGenerateAudioPost(files, authorization, textModel, audioModel, speaker1Voice, speaker2Voice, introInstructions, textInstructions, scratchPadInstructions, preludeDialog, podcastDialogInstructions, editedTranscript, userFeedback);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->generateAudioApiGenerateAudioPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **files** | [**BuiltList&lt;MultipartFile&gt;**](MultipartFile.md)|  | 
 **authorization** | **String**|  | [optional] 
 **textModel** | **String**|  | [optional] [default to 'gpt-4o-mini']
 **audioModel** | **String**|  | [optional] [default to 'tts-1']
 **speaker1Voice** | **String**|  | [optional] [default to 'alloy']
 **speaker2Voice** | **String**|  | [optional] [default to 'echo']
 **introInstructions** | **String**|  | [optional] 
 **textInstructions** | **String**|  | [optional] 
 **scratchPadInstructions** | **String**|  | [optional] 
 **preludeDialog** | **String**|  | [optional] 
 **podcastDialogInstructions** | **String**|  | [optional] 
 **editedTranscript** | **String**|  | [optional] 
 **userFeedback** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **generateStoryGenerateStoryPost**
> JsonObject generateStoryGenerateStoryPost(storyRequest, authorization)

Generate Story

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final StoryRequest storyRequest = ; // StoryRequest | 
final String authorization = authorization_example; // String | 

try {
    final response = api.generateStoryGenerateStoryPost(storyRequest, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->generateStoryGenerateStoryPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **storyRequest** | [**StoryRequest**](StoryRequest.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getBotDocumentsGetBotDocumentsGet**
> JsonObject getBotDocumentsGetBotDocumentsGet(bodyGetBotDocumentsGetBotDocumentsGet, authorization)

Get Bot Documents

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final BodyGetBotDocumentsGetBotDocumentsGet bodyGetBotDocumentsGetBotDocumentsGet = ; // BodyGetBotDocumentsGetBotDocumentsGet | 
final String authorization = authorization_example; // String | 

try {
    final response = api.getBotDocumentsGetBotDocumentsGet(bodyGetBotDocumentsGetBotDocumentsGet, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->getBotDocumentsGetBotDocumentsGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **bodyGetBotDocumentsGetBotDocumentsGet** | [**BodyGetBotDocumentsGetBotDocumentsGet**](BodyGetBotDocumentsGetBotDocumentsGet.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getDocumentContentGetDocumentContentPost**
> JsonObject getDocumentContentGetDocumentContentPost(documentContentRequestData, authorization)

Get Document Content

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final DocumentContentRequestData documentContentRequestData = ; // DocumentContentRequestData | 
final String authorization = authorization_example; // String | 

try {
    final response = api.getDocumentContentGetDocumentContentPost(documentContentRequestData, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->getDocumentContentGetDocumentContentPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **documentContentRequestData** | [**DocumentContentRequestData**](DocumentContentRequestData.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getDocumentIdsByFirebaseUrlsGetDocumentIdsByFirebaseUrlsPost**
> JsonObject getDocumentIdsByFirebaseUrlsGetDocumentIdsByFirebaseUrlsPost(documentRequestData, authorization)

Get Document Ids By Firebase Urls

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final DocumentRequestData documentRequestData = ; // DocumentRequestData | 
final String authorization = authorization_example; // String | 

try {
    final response = api.getDocumentIdsByFirebaseUrlsGetDocumentIdsByFirebaseUrlsPost(documentRequestData, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->getDocumentIdsByFirebaseUrlsGetDocumentIdsByFirebaseUrlsPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **documentRequestData** | [**DocumentRequestData**](DocumentRequestData.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getTokenGetTokenGet**
> JsonObject getTokenGetTokenGet(name, roomId, authorization)

Get Token

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final String name = name_example; // String | 
final String roomId = roomId_example; // String | 
final String authorization = authorization_example; // String | 

try {
    final response = api.getTokenGetTokenGet(name, roomId, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->getTokenGetTokenGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **name** | **String**|  | 
 **roomId** | **String**|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **handlePaymentRequestPaymentPost**
> BalanceResponse handlePaymentRequestPaymentPost(balanceRequest, authorization)

Handle Payment Request

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final BalanceRequest balanceRequest = ; // BalanceRequest | 
final String authorization = authorization_example; // String | 

try {
    final response = api.handlePaymentRequestPaymentPost(balanceRequest, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->handlePaymentRequestPaymentPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **balanceRequest** | [**BalanceRequest**](BalanceRequest.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**BalanceResponse**](BalanceResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **indexGet**
> String indexGet()

Index

Root endpoint for the API.  Returns:     str: A greeting message.

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();

try {
    final response = api.indexGet();
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->indexGet: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

**String**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **indexPost**
> String indexPost()

Index

Root endpoint for the API.  Returns:     str: A greeting message.

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();

try {
    final response = api.indexPost();
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->indexPost: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

**String**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **removeDocumentsFromBotRemoveDocumentsFromBotPost**
> JsonObject removeDocumentsFromBotRemoveDocumentsFromBotPost(documentBotRequestData, authorization)

Remove Documents From Bot

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final DocumentBotRequestData documentBotRequestData = ; // DocumentBotRequestData | 
final String authorization = authorization_example; // String | 

try {
    final response = api.removeDocumentsFromBotRemoveDocumentsFromBotPost(documentBotRequestData, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->removeDocumentsFromBotRemoveDocumentsFromBotPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **documentBotRequestData** | [**DocumentBotRequestData**](DocumentBotRequestData.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchDocumentByEmbeddingSearchDocumentByEmbeddingGet**
> JsonObject searchDocumentByEmbeddingSearchDocumentByEmbeddingGet(embeddingVector, originalUserId, originalBotId, authorization)

Search Document By Embedding

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final BuiltList<num> embeddingVector = ; // BuiltList<num> | 
final String originalUserId = originalUserId_example; // String | 
final String originalBotId = originalBotId_example; // String | 
final String authorization = authorization_example; // String | 

try {
    final response = api.searchDocumentByEmbeddingSearchDocumentByEmbeddingGet(embeddingVector, originalUserId, originalBotId, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->searchDocumentByEmbeddingSearchDocumentByEmbeddingGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **embeddingVector** | [**BuiltList&lt;num&gt;**](num.md)|  | 
 **originalUserId** | **String**|  | 
 **originalBotId** | **String**|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchDocumentSearchDocumentGet**
> JsonObject searchDocumentSearchDocumentGet(query, originalUserId, authorization)

Search Document

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final String query = query_example; // String | 
final String originalUserId = originalUserId_example; // String | 
final String authorization = authorization_example; // String | 

try {
    final response = api.searchDocumentSearchDocumentGet(query, originalUserId, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->searchDocumentSearchDocumentGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **query** | **String**|  | 
 **originalUserId** | **String**|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **startCrewaiTaskStartCrewPost**
> JsonObject startCrewaiTaskStartCrewPost(authorization)

Start Crewai Task

Starts a Celery task for running the CrewAI simulation.  Args:     request (Request): The incoming request containing the agent data.  Returns:     dict: A dictionary containing the task ID and session ID.

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final String authorization = authorization_example; // String | 

try {
    final response = api.startCrewaiTaskStartCrewPost(authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->startCrewaiTaskStartCrewPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stripeWebhookWebhooksPost**
> JsonObject stripeWebhookWebhooksPost()

Stripe Webhook

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();

try {
    final response = api.stripeWebhookWebhooksPost();
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->stripeWebhookWebhooksPost: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateDocumentUpdateDocumentPut**
> JsonObject updateDocumentUpdateDocumentPut(bodyUpdateDocumentUpdateDocumentPut, authorization)

Update Document

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final BodyUpdateDocumentUpdateDocumentPut bodyUpdateDocumentUpdateDocumentPut = ; // BodyUpdateDocumentUpdateDocumentPut | 
final String authorization = authorization_example; // String | 

try {
    final response = api.updateDocumentUpdateDocumentPut(bodyUpdateDocumentUpdateDocumentPut, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->updateDocumentUpdateDocumentPut: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **bodyUpdateDocumentUpdateDocumentPut** | [**BodyUpdateDocumentUpdateDocumentPut**](BodyUpdateDocumentUpdateDocumentPut.md)|  | 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **writingSyncWritingSyncPost**
> JsonObject writingSyncWritingSyncPost(authorization)

Writing Sync

This function handles the writing process by either using LangChain or directly with STORMWikiRunner. It generates an article based on the user's topic and performs research, outline generation, article creation, and polishing. The function also handles the storage of the generated article and updates progress status in Firestore.  Parameters: - request: The Starlette Request object containing the user's input. - db: The asynchronous Firestore client for database operations. - storage: The Google Cloud Firebase Storage client for file storage operations.  Returns: - A dictionary containing the generated article.

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final String authorization = authorization_example; // String | 

try {
    final response = api.writingSyncWritingSyncPost(authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->writingSyncWritingSyncPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **writingWritingPost**
> JsonObject writingWritingPost(verifyFirebaseToken, authorization)

Writing

This function handles the writing process by either using LangChain or directly with STORMWikiRunner. It generates an article based on the user's topic and performs research, outline generation, article creation, and polishing. The function also handles the storage of the generated article and updates progress status in Firestore.  Parameters: - request: The Starlette Request object containing the user's input. - db: The asynchronous Firestore client for database operations. - storage: The Google Cloud Firebase Storage client for file storage operations.  Returns: - A dictionary containing the generated article.

### Example
```dart
import 'package:diogenesaichatbotserver/api.dart';

final api = Diogenesaichatbotserver().getDefaultApi();
final JsonObject verifyFirebaseToken = ; // JsonObject | 
final String authorization = authorization_example; // String | 

try {
    final response = api.writingWritingPost(verifyFirebaseToken, authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling DefaultApi->writingWritingPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **verifyFirebaseToken** | [**JsonObject**](.md)|  | [optional] 
 **authorization** | **String**|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

