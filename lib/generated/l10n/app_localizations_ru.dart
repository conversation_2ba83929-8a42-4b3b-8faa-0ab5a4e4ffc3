// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get questionAndAnswer => 'Задайте вопрос и получите ответ';

  @override
  String get chatWithAiBot => 'Чат с ИИ-ботом';

  @override
  String get generateImage => 'Создать изображения';

  @override
  String get translation => 'Служба перевода';

  @override
  String get summarizeDocument => 'Суммировать документы';

  @override
  String get fixGrammar => 'Исправление грамматики';

  @override
  String get writeDocument => 'Написать документы';

  @override
  String get programming => 'Помощь в программировании';

  @override
  String get posts => 'Посты и обновления';

  @override
  String get transcriptions => 'Услуги транскрипции';

  @override
  String get readBook => 'Читать книги';

  @override
  String get readNews => 'Умные новости';

  @override
  String get chatWithFriends => 'Чат с друзьями и семьей';

  @override
  String get aiKit => 'Набор и инструменты ИИ';

  @override
  String get uploadFiles => 'Загрузить файлы и медиа';

  @override
  String get myBots => 'Мои ИИ-боты и чаты';

  @override
  String get tutor => 'Персонализированный репетитор';

  @override
  String get storyTelling => 'Рассказывание и обмен историями';

  @override
  String get personalizedSearch => 'Персонализированный поиск';

  @override
  String get smartSearch => 'Умный поиск';

  @override
  String get gptResearcher => 'ИИ-исследователь';

  @override
  String get generateComic => 'Создать комиксы';

  @override
  String get createRecipe => 'Создать рецепты';

  @override
  String get aiTripPlanner => 'Планировщик поездок ИИ';

  @override
  String get generateAudio => 'ИИ-аудио';

  @override
  String get localLLM => 'Локальный LLM';

  @override
  String get yolo => 'Загрузка локальных изображений';

  @override
  String get currentLocale => 'Русский';
}
