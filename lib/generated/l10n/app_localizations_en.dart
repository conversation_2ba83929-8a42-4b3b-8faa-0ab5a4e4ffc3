// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get questionAndAnswer => 'Ask and Answer';

  @override
  String get chatWithAiBot => 'Chat with AI Bot';

  @override
  String get generateImage => 'Generate Images';

  @override
  String get translation => 'Translation Service';

  @override
  String get summarizeDocument => 'Summarize Documents';

  @override
  String get fixGrammar => 'Grammar Correction';

  @override
  String get writeDocument => 'Write Documents';

  @override
  String get programming => 'Programming Assistance';

  @override
  String get posts => 'Posts and Updates';

  @override
  String get transcriptions => 'Transcription Services';

  @override
  String get readBook => 'Read Books';

  @override
  String get readNews => 'Smart News';

  @override
  String get chatWithFriends => 'Chat with Friends and Family';

  @override
  String get aiKit => 'AI Kit and Tools';

  @override
  String get uploadFiles => 'Upload Files and Media';

  @override
  String get myBots => 'My AI Bots and Chats';

  @override
  String get tutor => 'Personalized Tutor';

  @override
  String get storyTelling => 'Story Telling and Sharing';

  @override
  String get personalizedSearch => 'Personalized Search';

  @override
  String get smartSearch => 'Smart Search';

  @override
  String get gptResearcher => 'AI Researcher';

  @override
  String get generateComic => 'Generate Comics';

  @override
  String get createRecipe => 'Create Recipes';

  @override
  String get aiTripPlanner => 'AI Trip Planner';

  @override
  String get generateAudio => 'AI Audio';

  @override
  String get localLLM => 'Local LLM';

  @override
  String get yolo => 'Local Images Upload';

  @override
  String get currentLocale => 'English';
}
