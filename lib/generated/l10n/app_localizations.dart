import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_de.dart';
import 'app_localizations_el.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_hi.dart';
import 'app_localizations_it.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_pt.dart';
import 'app_localizations_ru.dart';
import 'app_localizations_tr.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('de'),
    Locale('el'),
    Locale('en'),
    Locale('es'),
    Locale('fr'),
    Locale('hi'),
    Locale('it'),
    Locale('ja'),
    Locale('pt'),
    Locale('ru'),
    Locale('tr'),
    Locale('zh'),
  ];

  /// No description provided for @questionAndAnswer.
  ///
  /// In en, this message translates to:
  /// **'Ask and Answer'**
  String get questionAndAnswer;

  /// No description provided for @chatWithAiBot.
  ///
  /// In en, this message translates to:
  /// **'Chat with AI Bot'**
  String get chatWithAiBot;

  /// No description provided for @generateImage.
  ///
  /// In en, this message translates to:
  /// **'Generate Images'**
  String get generateImage;

  /// No description provided for @translation.
  ///
  /// In en, this message translates to:
  /// **'Translation Service'**
  String get translation;

  /// No description provided for @summarizeDocument.
  ///
  /// In en, this message translates to:
  /// **'Summarize Documents'**
  String get summarizeDocument;

  /// No description provided for @fixGrammar.
  ///
  /// In en, this message translates to:
  /// **'Grammar Correction'**
  String get fixGrammar;

  /// No description provided for @writeDocument.
  ///
  /// In en, this message translates to:
  /// **'Write Documents'**
  String get writeDocument;

  /// No description provided for @programming.
  ///
  /// In en, this message translates to:
  /// **'Programming Assistance'**
  String get programming;

  /// No description provided for @posts.
  ///
  /// In en, this message translates to:
  /// **'Posts and Updates'**
  String get posts;

  /// No description provided for @transcriptions.
  ///
  /// In en, this message translates to:
  /// **'Transcription Services'**
  String get transcriptions;

  /// No description provided for @readBook.
  ///
  /// In en, this message translates to:
  /// **'Read Books'**
  String get readBook;

  /// No description provided for @readNews.
  ///
  /// In en, this message translates to:
  /// **'Smart News'**
  String get readNews;

  /// No description provided for @chatWithFriends.
  ///
  /// In en, this message translates to:
  /// **'Chat with Friends and Family'**
  String get chatWithFriends;

  /// No description provided for @aiKit.
  ///
  /// In en, this message translates to:
  /// **'AI Kit and Tools'**
  String get aiKit;

  /// No description provided for @uploadFiles.
  ///
  /// In en, this message translates to:
  /// **'Upload Files and Media'**
  String get uploadFiles;

  /// No description provided for @myBots.
  ///
  /// In en, this message translates to:
  /// **'My AI Bots and Chats'**
  String get myBots;

  /// No description provided for @tutor.
  ///
  /// In en, this message translates to:
  /// **'Personalized Tutor'**
  String get tutor;

  /// No description provided for @storyTelling.
  ///
  /// In en, this message translates to:
  /// **'Story Telling and Sharing'**
  String get storyTelling;

  /// No description provided for @personalizedSearch.
  ///
  /// In en, this message translates to:
  /// **'Personalized Search'**
  String get personalizedSearch;

  /// No description provided for @smartSearch.
  ///
  /// In en, this message translates to:
  /// **'Smart Search'**
  String get smartSearch;

  /// No description provided for @gptResearcher.
  ///
  /// In en, this message translates to:
  /// **'AI Researcher'**
  String get gptResearcher;

  /// No description provided for @generateComic.
  ///
  /// In en, this message translates to:
  /// **'Generate Comics'**
  String get generateComic;

  /// No description provided for @createRecipe.
  ///
  /// In en, this message translates to:
  /// **'Create Recipes'**
  String get createRecipe;

  /// No description provided for @aiTripPlanner.
  ///
  /// In en, this message translates to:
  /// **'AI Trip Planner'**
  String get aiTripPlanner;

  /// No description provided for @generateAudio.
  ///
  /// In en, this message translates to:
  /// **'AI Audio'**
  String get generateAudio;

  /// No description provided for @localLLM.
  ///
  /// In en, this message translates to:
  /// **'Local LLM'**
  String get localLLM;

  /// No description provided for @yolo.
  ///
  /// In en, this message translates to:
  /// **'Local Images Upload'**
  String get yolo;

  /// No description provided for @currentLocale.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get currentLocale;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>[
    'ar',
    'de',
    'el',
    'en',
    'es',
    'fr',
    'hi',
    'it',
    'ja',
    'pt',
    'ru',
    'tr',
    'zh',
  ].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'de':
      return AppLocalizationsDe();
    case 'el':
      return AppLocalizationsEl();
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
    case 'fr':
      return AppLocalizationsFr();
    case 'hi':
      return AppLocalizationsHi();
    case 'it':
      return AppLocalizationsIt();
    case 'ja':
      return AppLocalizationsJa();
    case 'pt':
      return AppLocalizationsPt();
    case 'ru':
      return AppLocalizationsRu();
    case 'tr':
      return AppLocalizationsTr();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
