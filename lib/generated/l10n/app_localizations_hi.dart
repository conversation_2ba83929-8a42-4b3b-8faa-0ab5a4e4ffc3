// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get questionAndAnswer => 'पूछें और उत्तर दें';

  @override
  String get chatWithAiBot => 'AI बॉट के साथ चैट करें';

  @override
  String get generateImage => 'छवियाँ उत्पन्न करें';

  @override
  String get translation => 'अनुवाद सेवा';

  @override
  String get summarizeDocument => 'दस्तावेज़ों का सारांश';

  @override
  String get fixGrammar => 'व्याकरण सुधारें';

  @override
  String get writeDocument => 'दस्तावेज़ लिखें';

  @override
  String get programming => 'प्रोग्रामिंग सहायता';

  @override
  String get posts => 'पोस्ट और अपडेट';

  @override
  String get transcriptions => 'प्रतिलिपि सेवाएँ';

  @override
  String get readBook => 'किताबें पढ़ें';

  @override
  String get readNews => 'स्मार्ट समाचार';

  @override
  String get chatWithFriends => 'दोस्तों और परिवार के साथ चैट करें';

  @override
  String get aiKit => 'AI किट और उपकरण';

  @override
  String get uploadFiles => 'फाइलें और मीडिया अपलोड करें';

  @override
  String get myBots => 'मेरे AI बॉट और चैट';

  @override
  String get tutor => 'व्यक्तिगत ट्यूटर';

  @override
  String get storyTelling => 'कहानियाँ सुनाना और साझा करना';

  @override
  String get personalizedSearch => 'व्यक्तिगत खोज';

  @override
  String get smartSearch => 'स्मार्ट खोज';

  @override
  String get gptResearcher => 'AI शोधकर्ता';

  @override
  String get generateComic => 'कॉमिक्स उत्पन्न करें';

  @override
  String get createRecipe => 'रेसिपी बनाएं';

  @override
  String get aiTripPlanner => 'AI यात्रा योजनाकार';

  @override
  String get generateAudio => 'AI ऑडियो';

  @override
  String get localLLM => 'स्थानीय LLM';

  @override
  String get yolo => 'स्थानीय छवियों को अपलोड करें';

  @override
  String get currentLocale => 'हिन्दी';
}
