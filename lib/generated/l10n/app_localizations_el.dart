// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Modern Greek (`el`).
class AppLocalizationsEl extends AppLocalizations {
  AppLocalizationsEl([String locale = 'el']) : super(locale);

  @override
  String get questionAndAnswer => 'Ερώτηση και Απάντηση';

  @override
  String get chatWithAiBot => 'Συνομιλία με τον AI Bot';

  @override
  String get generateImage => 'Δημιουργία Εικόνων';

  @override
  String get translation => 'Υπηρεσία Μετάφρασης';

  @override
  String get summarizeDocument => 'Περίληψη Εγγράφων';

  @override
  String get fixGrammar => 'Διόρθωση Γραμματικής';

  @override
  String get writeDocument => 'Συγγραφή Εγγράφων';

  @override
  String get programming => 'Βοήθεια Προγραμματισμού';

  @override
  String get posts => 'Δημοσιεύσεις και Ενημερώσεις';

  @override
  String get transcriptions => 'Υπηρεσίες Μεταγραφής';

  @override
  String get readBook => 'Διαβάστε Βιβλία';

  @override
  String get readNews => 'Έξυπνες Ειδήσεις';

  @override
  String get chatWithFriends => 'Συνομιλία με Φίλους και Οικογένεια';

  @override
  String get aiKit => 'Εργαλεία και Κιτ AI';

  @override
  String get uploadFiles => 'Μεταφόρτωση Αρχείων και Μέσων';

  @override
  String get myBots => 'Οι AI Bots και Συνομιλίες μου';

  @override
  String get tutor => 'Προσωπικός Δάσκαλος';

  @override
  String get storyTelling => 'Αφήγηση Ιστοριών και Μοιρασιά';

  @override
  String get personalizedSearch => 'Εξατομικευμένη Αναζήτηση';

  @override
  String get smartSearch => 'Έξυπνη Αναζήτηση';

  @override
  String get gptResearcher => 'Ερευνητής AI';

  @override
  String get generateComic => 'Δημιουργία Κόμικ';

  @override
  String get createRecipe => 'Δημιουργία Συνταγών';

  @override
  String get aiTripPlanner => 'AI Σχεδιαστής Ταξιδιών';

  @override
  String get generateAudio => 'AI Ήχος';

  @override
  String get localLLM => 'Τοπικό LLM';

  @override
  String get yolo => 'Μεταφόρτωση Τοπικών Εικόνων';

  @override
  String get currentLocale => 'Ελληνικά';
}
