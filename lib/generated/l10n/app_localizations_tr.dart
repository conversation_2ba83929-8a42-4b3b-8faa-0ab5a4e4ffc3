// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Turkish (`tr`).
class AppLocalizationsTr extends AppLocalizations {
  AppLocalizationsTr([String locale = 'tr']) : super(locale);

  @override
  String get questionAndAnswer => 'Sor ve Cevapla';

  @override
  String get chatWithAiBot => 'Yapay Zeka Botu ile Sohbet Et';

  @override
  String get generateImage => 'Görüntü Oluştur';

  @override
  String get translation => 'Çeviri Servisi';

  @override
  String get summarizeDocument => 'Belgeleri Özetle';

  @override
  String get fixGrammar => 'Dilbilgisi Düzeltmesi';

  @override
  String get writeDocument => 'Belgeler Yaz';

  @override
  String get programming => 'Programlama Yardımı';

  @override
  String get posts => 'Gönderiler ve Güncellemeler';

  @override
  String get transcriptions => 'Transkripsiyon Hizmetleri';

  @override
  String get readBook => 'Kitap Oku';

  @override
  String get readNews => 'Akıllı Haberler';

  @override
  String get chatWithFriends => 'Arkadaşlar ve Aile ile Sohbet Et';

  @override
  String get aiKit => 'Yapay Zeka Kiti ve Araçları';

  @override
  String get uploadFiles => 'Dosyaları ve Medyayı Yükle';

  @override
  String get myBots => 'Benim Yapay Zeka Botlarım ve Sohbetlerim';

  @override
  String get tutor => 'Kişiselleştirilmiş Eğitmen';

  @override
  String get storyTelling => 'Hikaye Anlatımı ve Paylaşımı';

  @override
  String get personalizedSearch => 'Kişiselleştirilmiş Arama';

  @override
  String get smartSearch => 'Akıllı Arama';

  @override
  String get gptResearcher => 'Yapay Zeka Araştırmacısı';

  @override
  String get generateComic => 'Karakalem Oluştur';

  @override
  String get createRecipe => 'Tarif Oluştur';

  @override
  String get aiTripPlanner => 'Yapay Zeka Seyahat Planlayıcısı';

  @override
  String get generateAudio => 'Yapay Zeka Ses';

  @override
  String get localLLM => 'Yerel LLM';

  @override
  String get yolo => 'Yerel Görüntü Yükle';

  @override
  String get currentLocale => 'Türkçe';
}
