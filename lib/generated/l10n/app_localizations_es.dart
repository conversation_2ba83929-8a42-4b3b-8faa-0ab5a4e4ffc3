// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get questionAndAnswer => 'Preguntar y Responder';

  @override
  String get chatWithAiBot => 'Chatear con el Bot de IA';

  @override
  String get generateImage => 'Generar Imágenes';

  @override
  String get translation => 'Servicio de Traducción';

  @override
  String get summarizeDocument => 'Resumir Documentos';

  @override
  String get fixGrammar => 'Corrección Gramatical';

  @override
  String get writeDocument => 'Escribir Documentos';

  @override
  String get programming => 'Asistencia en Programación';

  @override
  String get posts => 'Publicaciones y Actualizaciones';

  @override
  String get transcriptions => 'Servicios de Transcripción';

  @override
  String get readBook => 'Leer Libros';

  @override
  String get readNews => 'Noticias Inteligentes';

  @override
  String get chatWithFriends => 'Chatear con Amigos y Familia';

  @override
  String get aiKit => 'Kit y Herramientas de IA';

  @override
  String get uploadFiles => 'Subir Archivos y Medios';

  @override
  String get myBots => 'Mis Bots de IA y Chats';

  @override
  String get tutor => 'Tutor Personalizado';

  @override
  String get storyTelling => 'Narración y Compartición de Historias';

  @override
  String get personalizedSearch => 'Búsqueda Personalizada';

  @override
  String get smartSearch => 'Búsqueda Inteligente';

  @override
  String get gptResearcher => 'Investigador IA';

  @override
  String get generateComic => 'Generar Cómics';

  @override
  String get createRecipe => 'Crear Recetas';

  @override
  String get aiTripPlanner => 'Planificador de Viajes IA';

  @override
  String get generateAudio => 'Audio IA';

  @override
  String get localLLM => 'LLM Local';

  @override
  String get yolo => 'Subida de Imágenes Locales';

  @override
  String get currentLocale => 'Español';
}
