// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Italian (`it`).
class AppLocalizationsIt extends AppLocalizations {
  AppLocalizationsIt([String locale = 'it']) : super(locale);

  @override
  String get questionAndAnswer => 'Fai una domanda e ricevi una risposta';

  @override
  String get chatWithAiBot => 'Chatta con il Bot IA';

  @override
  String get generateImage => 'Genera Immagini';

  @override
  String get translation => 'Servizio di Traduzione';

  @override
  String get summarizeDocument => 'Riassumi Documenti';

  @override
  String get fixGrammar => 'Correzione Grammaticale';

  @override
  String get writeDocument => 'Scrivi Documenti';

  @override
  String get programming => 'Assistenza alla Programmazione';

  @override
  String get posts => 'Post e Aggiornamenti';

  @override
  String get transcriptions => 'Servizi di Trascrizione';

  @override
  String get readBook => 'Leggi Libri';

  @override
  String get readNews => 'Notizie Intelligenti';

  @override
  String get chatWithFriends => 'Chatta con Amici e Famiglia';

  @override
  String get aiKit => 'Kit e Strumenti IA';

  @override
  String get uploadFiles => 'Carica File e Media';

  @override
  String get myBots => 'I Miei Bot e Chat IA';

  @override
  String get tutor => 'Tutor Personalizzato';

  @override
  String get storyTelling => 'Raccontare e Condividere Storie';

  @override
  String get personalizedSearch => 'Ricerca Personalizzata';

  @override
  String get smartSearch => 'Ricerca Intelligente';

  @override
  String get gptResearcher => 'Ricercatore IA';

  @override
  String get generateComic => 'Genera Fumetti';

  @override
  String get createRecipe => 'Crea Ricette';

  @override
  String get aiTripPlanner => 'Pianificatore di Viaggi IA';

  @override
  String get generateAudio => 'Audio IA';

  @override
  String get localLLM => 'LLM Locale';

  @override
  String get yolo => 'Caricamento Immagini Locali';

  @override
  String get currentLocale => 'Italiano';
}
