// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get questionAndAnswer => '質問と回答';

  @override
  String get chatWithAiBot => 'AIボットとチャット';

  @override
  String get generateImage => '画像を生成する';

  @override
  String get translation => '翻訳サービス';

  @override
  String get summarizeDocument => '文書を要約する';

  @override
  String get fixGrammar => '文法修正';

  @override
  String get writeDocument => '文書を書く';

  @override
  String get programming => 'プログラミング支援';

  @override
  String get posts => '投稿と更新';

  @override
  String get transcriptions => '転記サービス';

  @override
  String get readBook => '本を読む';

  @override
  String get readNews => 'スマートニュース';

  @override
  String get chatWithFriends => '友達や家族とチャット';

  @override
  String get aiKit => 'AIキットとツール';

  @override
  String get uploadFiles => 'ファイルとメディアをアップロード';

  @override
  String get myBots => '私のAIボットとチャット';

  @override
  String get tutor => 'パーソナライズされたチューター';

  @override
  String get storyTelling => '物語の語りと共有';

  @override
  String get personalizedSearch => 'パーソナライズされた検索';

  @override
  String get smartSearch => 'スマート検索';

  @override
  String get gptResearcher => 'AIリサーチャー';

  @override
  String get generateComic => '漫画を生成する';

  @override
  String get createRecipe => 'レシピを作成する';

  @override
  String get aiTripPlanner => 'AI旅行プランナー';

  @override
  String get generateAudio => 'AIオーディオ';

  @override
  String get localLLM => 'ローカルLLM';

  @override
  String get yolo => 'ローカル画像アップロード';

  @override
  String get currentLocale => '日本語';
}
