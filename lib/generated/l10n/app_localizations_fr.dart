// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get questionAndAnswer => 'Poser et Répondre';

  @override
  String get chatWithAiBot => 'Discuter avec le Bot IA';

  @override
  String get generateImage => 'Générer des Images';

  @override
  String get translation => 'Service de Traduction';

  @override
  String get summarizeDocument => 'Résumer des Documents';

  @override
  String get fixGrammar => 'Correction Grammaticale';

  @override
  String get writeDocument => 'Rédiger des Documents';

  @override
  String get programming => 'Assistance en Programmation';

  @override
  String get posts => 'Publications et Mises à Jour';

  @override
  String get transcriptions => 'Services de Transcription';

  @override
  String get readBook => 'Lire des Livres';

  @override
  String get readNews => 'Actualités Intelligentes';

  @override
  String get chatWithFriends => 'Discuter avec des Amis et de la Famille';

  @override
  String get aiKit => 'Kit et Outils IA';

  @override
  String get uploadFiles => 'Télécharger des Fichiers et des Médias';

  @override
  String get myBots => 'Mes Bots IA et Discussions';

  @override
  String get tutor => 'Tuteur Personnalisé';

  @override
  String get storyTelling => 'Récit et Partage';

  @override
  String get personalizedSearch => 'Recherche Personnalisée';

  @override
  String get smartSearch => 'Recherche Intelligente';

  @override
  String get gptResearcher => 'Chercheur IA';

  @override
  String get generateComic => 'Générer des Bandes Dessinées';

  @override
  String get createRecipe => 'Créer des Recettes';

  @override
  String get aiTripPlanner => 'Planificateur de Voyage IA';

  @override
  String get generateAudio => 'Audio IA';

  @override
  String get localLLM => 'LLM Local';

  @override
  String get yolo => 'Téléchargement d\'Images Local';

  @override
  String get currentLocale => 'Français';
}
