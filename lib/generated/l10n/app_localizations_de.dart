// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get questionAndAnswer => 'Fragen und Antworten';

  @override
  String get chatWithAiBot => 'Chatten mit dem KI-Bot';

  @override
  String get generateImage => 'Bilder Generieren';

  @override
  String get translation => 'Übersetzungsdienst';

  @override
  String get summarizeDocument => 'Dokumente Zusammenfassen';

  @override
  String get fixGrammar => 'Grammatik Korrektur';

  @override
  String get writeDocument => 'Dokumente Schreiben';

  @override
  String get programming => 'Programmierhilfe';

  @override
  String get posts => 'Beiträge und Updates';

  @override
  String get transcriptions => 'Transkriptionsdienste';

  @override
  String get readBook => 'Bücher Lesen';

  @override
  String get readNews => 'Intelligente Nachrichten';

  @override
  String get chatWithFriends => 'Mit Freunden und Familie Chatten';

  @override
  String get aiKit => 'KI-Kit und Werkzeuge';

  @override
  String get uploadFiles => 'Dateien und Medien Hochladen';

  @override
  String get myBots => 'Meine KI-Bots und Chats';

  @override
  String get tutor => 'Personalisierter Tutor';

  @override
  String get storyTelling => 'Geschichten Erzählen und Teilen';

  @override
  String get personalizedSearch => 'Personalisierte Suche';

  @override
  String get smartSearch => 'Intelligente Suche';

  @override
  String get gptResearcher => 'KI-Forscher';

  @override
  String get generateComic => 'Comics Generieren';

  @override
  String get createRecipe => 'Rezepte Erstellen';

  @override
  String get aiTripPlanner => 'KI-Reiseplaner';

  @override
  String get generateAudio => 'KI-Audio';

  @override
  String get localLLM => 'Lokales LLM';

  @override
  String get yolo => 'Lokale Bildübertragung';

  @override
  String get currentLocale => 'Deutsch';
}
