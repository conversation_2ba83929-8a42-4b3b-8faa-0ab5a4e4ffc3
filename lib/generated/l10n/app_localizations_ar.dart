// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get questionAndAnswer => 'اسأل وأجب';

  @override
  String get chatWithAiBot => 'الدردشة مع بوت الذكاء الاصطناعي';

  @override
  String get generateImage => 'إنشاء صور';

  @override
  String get translation => 'خدمة الترجمة';

  @override
  String get summarizeDocument => 'تلخيص المستندات';

  @override
  String get fixGrammar => 'تصحيح القواعد';

  @override
  String get writeDocument => 'كتابة مستندات';

  @override
  String get programming => 'مساعدة في البرمجة';

  @override
  String get posts => 'المشاركات والتحديثات';

  @override
  String get transcriptions => 'خدمات النسخ';

  @override
  String get readBook => 'قراءة الكتب';

  @override
  String get readNews => 'أخبار ذكية';

  @override
  String get chatWithFriends => 'الدردشة مع الأصدقاء والعائلة';

  @override
  String get aiKit => 'مجموعة وأدوات الذكاء الاصطناعي';

  @override
  String get uploadFiles => 'تحميل الملفات والوسائط';

  @override
  String get myBots => 'بوتاتي والدردشات';

  @override
  String get tutor => 'مدرس شخصي';

  @override
  String get storyTelling => 'سرد ومشاركة القصص';

  @override
  String get personalizedSearch => 'بحث مخصص';

  @override
  String get smartSearch => 'بحث ذكي';

  @override
  String get gptResearcher => 'باحث الذكاء الاصطناعي';

  @override
  String get generateComic => 'إنشاء كوميديا';

  @override
  String get createRecipe => 'إنشاء وصفات';

  @override
  String get aiTripPlanner => 'مخطط رحلات الذكاء الاصطناعي';

  @override
  String get generateAudio => 'صوت الذكاء الاصطناعي';

  @override
  String get localLLM => 'LLM محلي';

  @override
  String get yolo => 'تحميل الصور المحلية';

  @override
  String get currentLocale => 'العربية';
}
