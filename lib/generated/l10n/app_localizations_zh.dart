// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get questionAndAnswer => '问与答';

  @override
  String get chatWithAiBot => '与AI机器人聊天';

  @override
  String get generateImage => '生成图像';

  @override
  String get translation => '翻译';

  @override
  String get summarizeDocument => '文档摘要';

  @override
  String get fixGrammar => '修复语法';

  @override
  String get writeDocument => '撰写文档';

  @override
  String get programming => '编程';

  @override
  String get posts => '帖子';

  @override
  String get transcriptions => '转录';

  @override
  String get readBook => '阅读书籍';

  @override
  String get readNews => '阅读新闻';

  @override
  String get chatWithFriends => '与朋友聊天';

  @override
  String get aiKit => 'AI工具包';

  @override
  String get uploadFiles => '上传文件';

  @override
  String get myBots => '我的机器人';

  @override
  String get tutor => '助教';

  @override
  String get storyTelling => '讲故事';

  @override
  String get personalizedSearch => '个性化搜索';

  @override
  String get smartSearch => '智能搜索';

  @override
  String get gptResearcher => '研究报告';

  @override
  String get generateComic => '生成漫画';

  @override
  String get createRecipe => '智能菜谱';

  @override
  String get aiTripPlanner => '智能行程';

  @override
  String get generateAudio => '智能播客';

  @override
  String get localLLM => '本地模型';

  @override
  String get yolo => '本地图像处理';

  @override
  String get currentLocale => '中文';
}
