// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get questionAndAnswer => 'Perguntar e Responder';

  @override
  String get chatWithAiBot => 'Conversar com o Bot de IA';

  @override
  String get generateImage => 'Gerar Imagens';

  @override
  String get translation => 'Serviço de Tradução';

  @override
  String get summarizeDocument => 'Resumir Documentos';

  @override
  String get fixGrammar => 'Correção Gramatical';

  @override
  String get writeDocument => 'Escrever Documentos';

  @override
  String get programming => 'Assistência em Programação';

  @override
  String get posts => 'Postagens e Atualizações';

  @override
  String get transcriptions => 'Serviços de Transcrição';

  @override
  String get readBook => 'Ler Livros';

  @override
  String get readNews => 'Notícias Inteligentes';

  @override
  String get chatWithFriends => 'Conversar com Amigos e Família';

  @override
  String get aiKit => 'Kit e Ferramentas de IA';

  @override
  String get uploadFiles => 'Enviar Arquivos e Mídia';

  @override
  String get myBots => 'Meus Bots e Chats de IA';

  @override
  String get tutor => 'Tutor Personalizado';

  @override
  String get storyTelling => 'Contar e Compartilhar Histórias';

  @override
  String get personalizedSearch => 'Busca Personalizada';

  @override
  String get smartSearch => 'Busca Inteligente';

  @override
  String get gptResearcher => 'Pesquisador de IA';

  @override
  String get generateComic => 'Gerar Quadrinhos';

  @override
  String get createRecipe => 'Criar Receitas';

  @override
  String get aiTripPlanner => 'Planejador de Viagens de IA';

  @override
  String get generateAudio => 'Áudio de IA';

  @override
  String get localLLM => 'LLM Local';

  @override
  String get yolo => 'Upload de Imagens Locais';

  @override
  String get currentLocale => 'Português';
}
