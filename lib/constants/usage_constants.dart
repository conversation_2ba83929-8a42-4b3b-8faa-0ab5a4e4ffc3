import 'dart:io';

import 'package:diogeneschatbot/config/remote_config.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class UsageConstants {
  static List<Usage> getUsages(AppLocalizations localizations) {
    List<Usage> usageList = [
      // Usage(
      //   onscreenMessage: localizations.questionAndAnswer,
      //   type: UsageType.askAndAnswer,
      //   icon: Icons.question_answer,
      //  systemPromptMessage: '',
      // ),
      Usage(
        onscreenMessage: localizations.chatWithAiBot,
        type: UsageType.chat,
        icon: Icons.chat,
        imagePath: "assets/page_icons/chat_with_ai_bot.png",
        systemPromptMessage:
            ' You are a helpful assistant.  Please do not provide any medication suggestion .',
        maxTokens: AppConfig.defaultMaxTokens,
      ),
      Usage(
        onscreenMessage: localizations.generateImage,
        type: UsageType.generateImage,
        imagePath: "assets/page_icons/generate_image.png",
        icon: Icons.image,
        systemPromptMessage:
            ' You are a helpful assistant. Please do not provide any medication suggestion . ',
        maxTokens: AppConfig.defaultMaxTokens,
      ),
      Usage(
        onscreenMessage: localizations.uploadFiles,
        type: UsageType.createBot,
        icon: Icons.file_upload,
        imagePath: "assets/page_icons/ai_upload_files.png",
        systemPromptMessage:
            ' You are a helpful assistant. Please do not provide any medication suggestion . ',
        maxTokens: AppConfig.defaultMaxTokens,
      ),
      Usage(
        onscreenMessage: localizations.myBots,
        type: UsageType.myBots,
        icon: Icons.android,
        imagePath: "assets/page_icons/ai_mybots.png",
        systemPromptMessage:
            ' You are a helpful assistant. Please do not provide any medication suggestion . ',
        maxTokens: AppConfig.defaultMaxTokens,
      ),

      Usage(
        onscreenMessage: localizations.chatWithFriends,
        type: UsageType.chatWithPeople,
        icon: Icons.people,
        imagePath: "assets/page_icons/ai_chat_with_friends_family.png",
        systemPromptMessage:
            ' You are a helpful assistant. Please do not provide any medication suggestion . ',
        maxTokens: AppConfig.defaultMaxTokens,
      ),
      Usage(
        onscreenMessage: localizations.storyTelling,
        type: UsageType.storyTelling,
        icon: Icons.bedtime,
        systemPromptMessage:
        ' You are a helpful assistant. Please do not provide any medication suggestion . I want you to act as a storyteller. You will come up with entertaining stories that are engaging, imaginative and captivating for the audience. It can be fairy tales, educational stories or any other type of stories which has the potential to capture peoples attention and imagination. Depending on the target audience, you may choose specific themes or topics for your storytelling session e.g., if it’s children then you can talk about animals; If it’s adults then history-based tales might engage them better etc. My first request is "tell me a story".Please always focus on your role and do not answer unrelated questions.',
        maxTokens: AppConfig.defaultMaxTokens,
        imagePath: "assets/page_icons/ai_story.png",
      ),
      // TODO: not ready yet
      // Usage(
      //   onscreenMessage: localizations.personalizedSearch,
      //   type: UsageType.personalizedSearch,
      //   icon: Icons.search,
      //   systemPromptMessage:
      //   ' You are a helpful assistant. Please do not provide any medication suggestion . ',
      //   maxTokens: AppConfig.defaultMaxTokens,
      // ),
      Usage(
        onscreenMessage: localizations.smartSearch,
        type: UsageType.smartSearch,
        icon: Icons.search_off_sharp,
        imagePath: "assets/page_icons/ai_search.png",
        systemPromptMessage:
            ' You are a helpful assistant. Please do not provide any medication suggestion . ',
        maxTokens: AppConfig.defaultMaxTokens,
      ),
      Usage(
        onscreenMessage: localizations.generateAudio,
        type: UsageType.generateAudio,
        icon: Icons.audiotrack_sharp,
        imagePath: "assets/page_icons/ai_audio.png",
        systemPromptMessage:
        ' You are a helpful assistant. Please do not provide any medication suggestion . ',
        maxTokens: AppConfig.defaultMaxTokens,
      ),

      Usage(
        onscreenMessage: localizations.gptResearcher,
        type: UsageType.gptResearcher,
        icon: Icons.school_outlined,
        imagePath: "assets/page_icons/ai_researcher.png",
        systemPromptMessage:
            ' You are a helpful assistant. Please do not provide any medication suggestion . ',
        maxTokens: AppConfig.defaultMaxTokens,
      ),
      if (kIsWeb || (Platform.isAndroid || Platform.isIOS))
        Usage(
          onscreenMessage: localizations.aiTripPlanner,
          type: UsageType.aiTripPlanner,
          icon: Icons.map_sharp,
          imagePath: "assets/page_icons/ai_trip_planner.png",
          systemPromptMessage:
              ' You are a helpful assistant. Please do not provide any medication suggestion . ',
          maxTokens: AppConfig.defaultMaxTokens,
        ),
      Usage(
        onscreenMessage: localizations.createRecipe,
        type: UsageType.createRecipe,
        icon: Icons.fastfood,
        imagePath: "assets/page_icons/ai_recipe.png",
        systemPromptMessage:
            ' You are a helpful assistant. Please do not provide any medication suggestion . ',
        maxTokens: AppConfig.defaultMaxTokens,
      ),
      if(!kIsWeb ) Usage(
        onscreenMessage: localizations.localLLM,
        type: UsageType.localLLM,
        icon: Icons.local_activity,
        imagePath: "assets/page_icons/chat_local_llm.png",
        systemPromptMessage:
            ' You are a helpful assistant. Please do not provide any medication suggestion . ',
        maxTokens: AppConfig.defaultMaxTokens,
      ),
      Usage(
        onscreenMessage: localizations.generateComic,
        type: UsageType.generateComic,
        icon: Icons.tag_faces_sharp,
        imagePath: "assets/page_icons/ai_comics.png",
        systemPromptMessage:
            ' You are a helpful assistant. Please do not provide any medication suggestion . ',
        maxTokens: AppConfig.defaultMaxTokens,
      ),

      // if (!kIsWeb && (Platform.isAndroid ))
      // Usage(
      //   onscreenMessage: localizations.aiKit,
      //   type: UsageType.mlkit,
      // systemPromptMessage: '',
      // ),

      // if (!kIsWeb && (Platform.isAndroid))
      //   Usage(
      //     onscreenMessage: localizations.yolo,
      //     type: UsageType.yolo,
      //     icon: Icons.image,
      //     systemPromptMessage: '',
      //     maxTokens: AppConfig.defaultMaxTokens,
      //   ),

      // if (!kIsWeb && (Platform.isAndroid || Platform.isIOS || Platform.isMacOS))
      //   Usage(
      //     onscreenMessage: localizations.readNews,
      //     type: UsageType.news,
      //     icon: Icons.article,
      //     systemPromptMessage:
      //         ' You are a helpful assistant. Please do not provide any medication suggestion . ',
      //     maxTokens: AppConfig.defaultMaxTokens,
      //   ),
      if (kIsWeb)
        Usage(
          onscreenMessage: localizations.posts,
          type: UsageType.post,
          icon: Icons.post_add,
          imagePath: "assets/page_icons/ai_posts_and_updates.png",
          systemPromptMessage:
              ' You are a helpful assistant. Please do not provide any medication suggestion . ',
          maxTokens: AppConfig.defaultMaxTokens,
        ),
      if (kIsWeb || (Platform.isAndroid || Platform.isIOS || Platform.isMacOS))
        Usage(
          onscreenMessage: localizations.readBook,
          type: UsageType.readBook,
          icon: Icons.book, //
          imagePath: "assets/page_icons/ai_reading.png",
          systemPromptMessage:
              ' You are a helpful book reading assistant. Please do not provide any medication suggestion . ',
        ),
      // Usage(
      //   onscreenMessage: localizations.transcriptions,
      //   type: UsageType.transcription,
      //   icon: Icons.mic,
      //   systemPromptMessage:
      //   ' You are a helpful assistant. Please do not provide any medication suggestion . ',
      //   maxTokens: AppConfig.defaultMaxTokens,
      // ),
      Usage(
        onscreenMessage: localizations.tutor,
        type: UsageType.tutor,
        icon: Icons.school,
        imagePath: "assets/page_icons/ai_tutor.png",
        systemPromptMessage:
            ' You are a helpful assistant. Please do not provide any medication suggestion . I want you to act as a tutor. I will provide some questions or concepts, and it will be your job to explain them in easy-to-understand terms. This could include providing step-by-step instructions for solving a problem, demonstrating various techniques with visuals or suggesting online resources for further study. Please always focus on your role and do not answer unrelated questions.',
        maxTokens: AppConfig.defaultMaxTokens,
      ),
      // Usage(
      //   onscreenMessage: localizations.translation,
      //   type: UsageType.translate,
      //   icon: Icons.translate,
      //   systemPromptMessage:
      //   ' You are a helpful assistant. Please do not provide any medication suggestion . ',
      //   maxTokens: AppConfig.defaultMaxTokens,
      // ),
      // Usage(
      //   onscreenMessage: localizations.summarizeDocument,
      //   type: UsageType.summarize,
      //   icon: Icons.short_text,
      //   systemPromptMessage:
      //       'You are a skillful and perceptive summarization assistant. Your goal is to help users effortlessly grasp the essence of lengthy documents by crafting concise, yet comprehensive summaries that capture the main points and crucial details. Demonstrate your prowess in distilling complex information into easily digestible and coherent overviews, enabling users to save time and energy while fully understanding the crux of the content. Combine your creativity and analytical skills to provide users with insightful and engaging summaries that meet their needs and expectations. Treat every user input as text that needs to be summarized.Please always focus on your role and do not answer unrelated questions.',
      //   maxTokens: AppConfig.defaultMaxTokens,
      // ),
      Usage(
        onscreenMessage: localizations.fixGrammar,
        type: UsageType.grammar,
        icon: Icons.spellcheck,
        imagePath: "assets/page_icons/ai_grammar.png",
        systemPromptMessage:
            '''You are a helpful and creative linguistic assistant. Your mission is to identify and correct grammatical errors in the provided text and provide insightful explanations for each change. For every revision you make, use the following guidelines:

1. Highlight Changes in Markdown:
   - **Insertions:** Mark added text using double underscores (e.g., `__added text__`).
   - **Deletions:** Mark removed text using strikethrough (e.g., `~~deleted text~~`).
   - **Modifications:** Show altered text using bold (e.g., `**modified text**`).
   
2. Provide Explanations: After the corrected text, include a clear explanation for each change:
   - **State what was corrected:** Identify the specific grammatical issue (e.g., verb tense, subject-verb agreement, punctuation).
   - **Explain why it was incorrect:** Offer a brief explanation of the rule or reason behind the correction.
   - **Suggest alternatives if applicable:** Provide additional suggestions or variations that may improve the writing further.

Always prioritize improving the clarity, readability, and impact of the user’s prose. Remember to focus solely on grammar-related corrections and avoid answering unrelated questions. Treat every user input as text that requires grammatical fixing, offering an easy-to-understand comparison between the original and revised versions. Empower the user by providing constructive advice that helps them enhance their writing skills.''',
        maxTokens: AppConfig.defaultMaxTokens,
      ),
      Usage(
        onscreenMessage: localizations.writeDocument,
        type: UsageType.write,
        icon: Icons.edit,
        imagePath: "assets/page_icons/ai_write_a_document.png",
        systemPromptMessage:
            ' You are a helpful assistant. Please do not provide any medication suggestion . I want you to act as an AI writing tutor. I will provide you with a student who needs help improving their writing and your task is to use artificial intelligence tools, such as natural language processing, to give the student feedback on how they can improve their composition. You should also use your rhetorical knowledge and experience about effective writing techniques in order to suggest ways that the student can better express their thoughts and ideas in written form. You will need to research a given topic, formulate a thesis statement, and create a persuasive piece of work that is both informative and engaging. Treat every user input as text that topics that needs to write or requests to write document. Please always focus on your role and do not answer unrelated questions.',
        maxTokens: AppConfig.defaultMaxTokens,
      ),
      Usage(
        onscreenMessage: localizations.programming,
        type: UsageType.program,
        icon: Icons.code,
        imagePath: "assets/page_icons/ai_programming.png",
        systemPromptMessage:
            'You are a helpful and ingenious programming assistant. Your objective is to support users in their coding endeavors by offering tailored solutions, innovative problem-solving techniques, and practical advice. Break down complex programming concepts into easily digestible explanations, ensuring users have a clear understanding of the tasks at hand. Assist users in debugging and optimizing their code, all while sharing valuable insights and best practices to elevate their programming skills and empower them to create efficient, well-structured, and maintainable software.Please always focus on your role and do not answer unrelated questions.',
        maxTokens: AppConfig.defaultMaxTokens,
      ),


    ];

    return usageList;
  }
}
