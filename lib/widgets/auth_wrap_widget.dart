import 'package:diogeneschatbot/constants/app_constants.dart';
import 'package:diogeneschatbot/pages/home_page.dart';
import 'package:diogeneschatbot/pages/login_signup_page.dart';
import 'package:diogeneschatbot/pages/username_selection_page.dart';
import 'package:diogeneschatbot/services/auth_service.dart';
import 'package:diogeneschatbot/util/enum/username_status.dart';
import 'package:diogeneschatbot/util/functions.dart';
import 'package:flutter/material.dart';

class AuthWrapper extends StatelessWidget {
  final AuthStatus authStatus;
  final AuthService authService;

  const AuthWrapper({
    super.key,
    required this.authStatus,
    required this.authService,
  });

  @override
  Widget build(BuildContext context) {
    print("🏗️ AuthWrapper build called with authStatus: $authStatus");
    
    switch (authStatus) {
      case AuthStatus.unauthenticated:
        print("🔓 AuthStatus.unauthenticated - returning LoginSignupPage");
        return LoginSignupPage();
      case AuthStatus.emailUnverified:
        print("📧 AuthStatus.emailUnverified - returning email verification screen");
        return Scaffold(
          appBar: AppBar(title: const Text('Email Verification')),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Please verify your email to continue.'),
                ElevatedButton(
                  onPressed: () => authService.sendEmailVerification(),
                  child: const Text('Resend Verification Email'),
                ),
                ElevatedButton(
                  onPressed: () => authService.signOut(),
                  child: const Text('Sign Out'),
                ),
              ],
            ),
          ),
        );
      case AuthStatus.authenticated:
        print("✅ AuthStatus.authenticated - checking username");
        final userId = AuthService.currentUserId();
        if (userId == null) {
          print("❌ Authenticated but userId is null - returning LoginSignupPage");
          return LoginSignupPage();
        }
        print("🔍 Checking username for userId: $userId");
        return FutureBuilder<UsernameCheckStatus>(
          future: Functions.checkUsernameExists(userId),
          builder: (BuildContext context,
              AsyncSnapshot<UsernameCheckStatus> snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              if (snapshot.hasError) {
                // If there was an error checking the username, show a toast and navigate to login/signup
                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    content:
                        Text('Error checking username. Please try again.')));
                return LoginSignupPage();
              }

              switch (snapshot.data) {
                case UsernameCheckStatus.exists:
                  return HomePage(title: AppConstants.appTitle);
                case UsernameCheckStatus.notExists:
                  return UsernameSelectionPage(
                      user: AuthService.currentUser()!);
                case UsernameCheckStatus.error:
                default:
                  // Handle error state (if it reaches here)
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                      content: Text('Error occurred. Please try again.')));
                  return LoginSignupPage(); // Redirect to login/signup if an error occurs
              }
            } else {
              return const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }
          },
        );
    }
    ;
  }
}
