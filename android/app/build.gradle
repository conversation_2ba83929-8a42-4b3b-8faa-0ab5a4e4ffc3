plugins {
    id "com.android.application"
    id "kotlin-android"
    id "kotlin-parcelize"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}


def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}


// START: FlutterFire Configuration
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.firebase.firebase-perf'
// END: FlutterFire Configuration
apply plugin: 'com.google.firebase.appdistribution'

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    compileSdkVersion flutter.compileSdkVersion
//    compileSdkVersion 35
   ndkVersion flutter.ndkVersion
//    ndkVersion = "27.2.12479018"
    //   ndkVersion = "28.0.12433566"
//    ndkVersion = "26.1.10909125"

    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    aaptOptions {
        noCompress 'tflite'
        noCompress 'lite'
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    packagingOptions {
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
    }

    defaultConfig {
        applicationId "com.diogenes.ai.chatbot"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 28
        // targetSdkVersion flutter.targetSdkVersion
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled = true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        // manifestPlaceholders = [google_maps_api_key: localProperties.getProperty('google_maps_api_key')]
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    splits {
        abi {
            enable true
            reset()
            include  'armeabi-v7a', 'x86', 'x86_64', 'arm64-v8a'
            universalApk false
        }
    }
    namespace 'com.diogenes.ai.chatbot'
    testNamespace 'com.diogenes.ai.chatbot.test'

    buildFeatures {
        viewBinding true
    }
}

flutter {
    source '../..'
}

////// Java
//java {
//    toolchain {
//        languageVersion.set(JavaLanguageVersion.of(17))
//    }
//}
//
// Kotlin
//kotlin {
//    jvmToolchain {
//        languageVersion.set(JavaLanguageVersion.of(21))
//    }
//}
dependencies {
    implementation 'com.google.android.play:integrity:1.4.0'
    implementation("androidx.multidex:multidex:2.0.1")
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'junit:junit:4.13.2'
    implementation 'com.google.firebase:firebase-analytics'
    // Import the BoM for the Firebase platform
    implementation platform('com.google.firebase:firebase-bom:33.4.0')

    // Add the dependency for the Firebase Authentication library
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation 'com.google.firebase:firebase-auth'
//    implementation 'com.google.firebase:firebase-core'
    implementation 'com.google.firebase:firebase-messaging'

    androidTestImplementation "androidx.test:rules:1.6.1"
    // Optional -- UI testing with Espresso
    androidTestImplementation "androidx.test.espresso:espresso-core:3.6.1"
    // Optional -- UI testing with UI Automator
    androidTestImplementation "androidx.test.uiautomator:uiautomator:2.3.0"
    // Optional -- UI testing with Compose
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4:1.7.3'

    // Android Test dependencies
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test:runner:1.6.2'
    androidTestImplementation 'tools.fastlane:screengrab:2.1.1'

    // For AGP 7.4+
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.2'
    // For AGP 7.3
    // coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.3'
    // For AGP 4.0 to 7.2
    // coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.9'

    implementation 'com.stripe:stripe-android:17.1.1'
    implementation 'com.google.android.gms:play-services-wallet:19.4.0'
    // Add the dependencies for the App Check libraries
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation("com.google.firebase:firebase-appcheck-debug")

    def billing_version = "7.1.1"

    implementation "com.android.billingclient:billing:$billing_version"
    implementation "com.android.billingclient:billing-ktx:$billing_version"

}
