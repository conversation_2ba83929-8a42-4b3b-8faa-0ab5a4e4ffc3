# Keep TensorFlow Lite GPU classes
-keep class org.tensorflow.lite.gpu.** { *; }

# Don't warn about specific TensorFlow Lite GPU related classes
-dontwarn org.tensorflow.lite.gpu.GpuDelegateFactory$Options$GpuBackend

# Stripe Push Provisioning related classes (no warnings)
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivity$g
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Args
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Error
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningEphemeralKeyProvider

# Don't warn about specific missing MediaPipe proto classes
-dontwarn com.google.mediapipe.proto.CalculatorProfileProto$CalculatorProfile
-dontwarn com.google.mediapipe.proto.GraphTemplateProto$CalculatorGraphTemplate

# Don't warn about missing javax.lang.model classes
-dontwarn javax.lang.model.SourceVersion
-dontwarn javax.lang.model.element.Element
-dontwarn javax.lang.model.element.ElementKind
-dontwarn javax.lang.model.type.TypeMirror
-dontwarn javax.lang.model.type.TypeVisitor
-dontwarn javax.lang.model.util.SimpleTypeVisitor8

# Keep all MediaPipe proto and framework classes
-keep class com.google.mediapipe.proto.** { *; }
-keep class com.google.mediapipe.framework.** { *; }

# Keep all javax.lang.model and AutoValue-related classes
-keep class javax.lang.model.** { *; }
-keep class autovalue.shaded.com.squareup.javapoet.** { *; }

# Don't warn about MediaPipe proto and framework classes
-dontwarn com.google.mediapipe.proto.**

# Don't warn about javax.lang.model and AutoValue-related classes
-dontwarn javax.lang.model.**
-dontwarn autovalue.shaded.com.squareup.javapoet.**