<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <instrumentation
        android:name="androidx.test.runner.AndroidJUnitRunner"
        android:targetPackage="com.diogenes.ai.chatbot" />

    <application
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="Tests for ai.chatbot"
        android:supportsRtl="false"
        android:theme="@style/Theme.AppCompat.Light.NoActionBar">
        <uses-library android:name="android.test.runner" />

        <activity
            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
            android:exported="true"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            tools:replace="android:exported, android:theme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
 />


    </application>

</manifest>
