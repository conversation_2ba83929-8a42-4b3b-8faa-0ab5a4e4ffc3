package com.diogenes.ai.chatbot.test;

import androidx.test.espresso.Espresso;
import androidx.test.espresso.ViewInteraction;
import androidx.test.espresso.action.ViewActions;
import androidx.test.espresso.matcher.ViewMatchers;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.filters.LargeTest;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.ActivityTestRule;
import androidx.test.uiautomator.UiDevice;
import androidx.test.uiautomator.UiObject;
import androidx.test.uiautomator.UiSelector;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import tools.fastlane.screengrab.Screengrab;
import tools.fastlane.screengrab.UiAutomatorScreenshotStrategy;
import tools.fastlane.screengrab.locale.LocaleTestRule;

import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.assertion.ViewAssertions.matches;
import static androidx.test.espresso.matcher.ViewMatchers.isDisplayed;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static androidx.test.espresso.matcher.ViewMatchers.withTagKey;

import android.os.Environment;

import com.diogenes.ai.chatbot.MainActivity;

import java.io.File;

@RunWith(AndroidJUnit4.class)
@LargeTest
public class DiogenesInstrumentedTest {

    @Rule
    public ActivityTestRule<MainActivity> mActivityRule = new ActivityTestRule<>(MainActivity.class);

    @Rule
    public LocaleTestRule localeTestRule = new LocaleTestRule();

    private UiDevice device;

    @Before
    public void setUp() {
        device = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());
    }

    @Test
    public void findButtonAndTakeScreenshot() throws Exception {
        // Replace 'Button Text' with the text of the button you're looking for
        UiObject button = device.findObject(new UiSelector().text("Chat with AI bot"));

        // Click the button
        button.click();

        // Wait for a while to make sure the click action is performed
        Thread.sleep(1000);

        // Take a screenshot
        File screenshotFile = new File(Environment.getExternalStorageDirectory().getPath(), "screenshot.png");
        device.takeScreenshot(screenshotFile);

        // You can also save the screenshot in a specific directory, like this:
        // File screenshotDir = new File(Environment.getExternalStorageDirectory().getPath(), "screenshots");
        // screenshotDir.mkdirs();
        // File screenshotFile = new File(screenshotDir, "screenshot.png");
        // device.takeScreenshot(screenshotFile);
    }

    @Test
    public void testHomeScreenScreenshot() {
        Screengrab.setDefaultScreenshotStrategy(new UiAutomatorScreenshotStrategy());
//        Espresso.onView(ViewMatchers.withId(R.id.))
//                .perform(ViewActions.click());
//        ViewInteraction chatWithAiBotButton = onView(ViewMatchers.withText("Chat with AI bot"));
//        chatWithAiBotButton.check(matches(isDisplayed()));

        // Your custom onView...
//        onView(withTagKey(Key.)).perform(click());

        Screengrab.screenshot("01HomeScreen");
    }

    // Add other test methods for different activities or features.

}
