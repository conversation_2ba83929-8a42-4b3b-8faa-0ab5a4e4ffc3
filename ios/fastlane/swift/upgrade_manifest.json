{"Actions.swift": "Autogenerated API", "Fastlane.swift": "Autogenerated API", "DeliverfileProtocol.swift": "Autogenerated API", "GymfileProtocol.swift": "Autogenerated API", "MatchfileProtocol.swift": "Autogenerated API", "Plugins.swift": "Autogenerated API", "PrecheckfileProtocol.swift": "Autogenerated API", "ScanfileProtocol.swift": "Autogenerated API", "ScreengrabfileProtocol.swift": "Autogenerated API", "SnapshotfileProtocol.swift": "Autogenerated API", "LaneFileProtocol.swift": "Fastfile Components", "OptionalConfigValue.swift": "Fastfile Components", "Atomic.swift": "Networking", "ControlCommand.swift": "Networking", "RubyCommand.swift": "Networking", "RubyCommandable.swift": "Networking", "Runner.swift": "Networking", "SocketClient.swift": "Networking", "SocketClientDelegateProtocol.swift": "Networking", "SocketResponse.swift": "Networking", "main.swift": "Runner Code", "ArgumentProcessor.swift": "Runner Code", "RunnerArgument.swift": "Runner Code"}