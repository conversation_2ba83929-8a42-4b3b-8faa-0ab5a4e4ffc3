# Uncomment the lines below you want to change by removing the # in the beginning

# A list of devices you want to take the screenshots from
devices([
#   "iPhone 8",
  "iPhone 8 Plus", # 5.5 inch
  # "iPhone 11 Pro Max", # 6.5 inch
  # "iPhone 14",
#   "iPhone 14 Plus",
#   "iPhone 14 Pro",
    # "iPhone 16 Pro",
  # "iPhone 14 Pro Max", # 6.7 inch
#   "iPad Pro (11-inch) (4th generation)",
  # "iPad (10th generation)",
##  "iPad Air (5th generation)",
#   "iPhone SE",
#   "iPhone X",
  # "iPad Pro (12.9-inch) (6th generation)", # 12.9 inch
  # "iPad Pro (9.7-inch)",
#   "Apple TV 1080p",
#   "Apple Watch Series 6 - 44mm"
])


# [06:34:51]: ▸ User defaults from command line:
# [06:34:51]: ▸     IDEPackageSupportUseBuiltinSCM = YES
# [06:34:52]: ▸ --- xcodebuild: WARNING: Using the first of multiple matching destinations:
# [06:34:52]: ▸ { platform:macOS, arch:x86_64, variant:Mac Catalyst, id:F0D71A02-BE17-5C52-989D-B60486507D09 }
# [06:34:52]: ▸ { platform:iOS, id:dvtdevice-DVTiPhonePlaceholder-iphoneos:placeholder, name:Any iOS Device }
# [06:34:52]: ▸ { platform:iOS Simulator, id:dvtdevice-DVTiOSDeviceSimulatorPlaceholder-iphonesimulator:placeholder, name:Any iOS Simulator Device }
# [06:34:52]: ▸ { platform:macOS, variant:Mac Catalyst, name:Any Mac }
# [06:34:52]: ▸ { platform:iOS Simulator, id:E52D28ED-40D4-4B7B-8109-B219FE447246, OS:16.2, name:iPad (10th generation) }
# [06:34:52]: ▸ { platform:iOS Simulator, id:6C3C40BB-7DD9-40A5-BCEB-F47BECB0C27B, OS:16.2, name:iPad Air (5th generation) }
# [06:34:52]: ▸ { platform:iOS Simulator, id:6AB7F346-8606-4F3D-B50D-0BC873B9768C, OS:16.2, name:iPad Pro (11-inch) (4th generation) }
# [06:34:52]: ▸ { platform:iOS Simulator, id:49BE1032-4949-45AB-B0E1-55034E8CADFB, OS:16.2, name:iPad Pro (12.9-inch) (6th generation) }
# [06:34:52]: ▸ { platform:iOS Simulator, id:FEB8602E-395F-47D0-A998-8584DCDE9267, OS:16.2, name:iPad mini (6th generation) }
# [06:34:52]: ▸ { platform:iOS Simulator, id:23A1F79F-C325-46AC-8FB6-129481C67EF0, OS:16.2, name:iPhone 8 Plus }
# [06:34:52]: ▸ { platform:iOS Simulator, id:5CE9E62C-9C4F-43E0-84C9-AB96B2F059C0, OS:16.2, name:iPhone 11 Pro Max }
# [06:34:52]: ▸ { platform:iOS Simulator, id:3837F43F-594E-4F09-B9DB-3EE676A0F34E, OS:16.2, name:iPhone 14 }
# [06:34:52]: ▸ { platform:iOS Simulator, id:EFEA42E5-CEE9-4023-BE3E-15F286618F7D, OS:16.2, name:iPhone 14 Plus }
# [06:34:52]: ▸ { platform:iOS Simulator, id:4C37D838-48EB-4E1F-AACA-6AAFDE2580E3, OS:16.2, name:iPhone 14 Pro }
# [06:34:52]: ▸ { platform:iOS Simulator, id:E942ABB9-66AE-4330-B66F-4B4388CBCA10, OS:16.2, name:iPhone 14 Pro Max }
# [06:34:52]: ▸ { platform:iOS Simulator, id:AA460909-DD31-473C-A073-48840364ED74, OS:16.2, name:iPhone SE (3rd generation) }

languages([
  "en-US",
  "zh-CN",
  "de-DE",
#   "it-IT",
#   ["pt", "pt_BR"] # Portuguese with Brazilian locale
])

# The name of the scheme which contains the UI Tests
scheme("diogenes.ai.chatbotUITests")

# Where should the resulting screenshots be stored?
output_directory("./fastlane/screenshots")

# remove the '#' to clear all previously generated screenshots before creating new ones
# clear_previous_screenshots(true)

# Remove the '#' to set the status bar to 9:41 AM, and show full battery and reception. See also override_status_bar_arguments for custom options.
override_status_bar(true)

# Arguments to pass to the app on launch. See https://docs.fastlane.tools/actions/snapshot/#launch-arguments
# launch_arguments(["-favColor red"])


# For more information about all available options run
# fastlane action snapshot
