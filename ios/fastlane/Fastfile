# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# fastlane run app_store_connect_api_key   key_id: "RYV8GYBPW9"   issuer_id: "69a6de7e-86b9-47e3-e053-5b8c7c11a4d1"  key_filepath: "/Users/<USER>/Documents/devsec/AuthKey_RYV8GYBPW9.p8"

# Uncomment the line if you want fastlane to automatically update itself
update_fastlane
require 'yaml' # Require YAML to read pubspec.yaml

default_platform(:ios)

platform :ios do

  desc "Authenticate App Store Connect using API Key"
  lane :authenticate_app_store do
    app_store_connect_api_key(
      key_id: "RYV8GYBPW9",
      issuer_id: "69a6de7e-86b9-47e3-e053-5b8c7c11a4d1",
      key_filepath: "/Users/<USER>/Documents/devsec/AuthKey_RYV8GYBPW9.p8", # Update with your path
      in_house: false
    )
  end

  desc "Sync certificates from local machine"
  lane :sync_certificates do
    match({ readonly: true, type: "appstore" }) # Read-only to prevent overriding existing certificates.
  end

  desc "Get the current app version from pubspec.yaml"
  lane :get_app_version do
    pubspec = YAML.load_file('../../pubspec.yaml')
    version = pubspec['version']
    version # Return the version string
  end

  desc "Set build number"
  lane :set_build_number do
    authenticate_app_store
    increment_version_number(version_number: get_app_version) # Read the version from pubspec.yaml
    latest_build = latest_testflight_build_number(version: get_app_version, app_identifier: 'com.diogenes.ai.chatbot')
    increment_build_number(build_number: latest_build + 1) # Increment the build number by 1
  end

  desc "Generate new localized screenshots"
  lane :screenshots do
    capture_screenshots(workspace: "Runner.xcworkspace", scheme: "diogenes.ai.chatbotUITests")
    frame_screenshots(white: true)
  end

  desc "Deliver app metadata without uploading the binary"
  lane :deliver_metadata do
    authenticate_app_store
    deliver(
      skip_binary_upload: true,
      username: "<EMAIL>",
      precheck_include_in_app_purchases: false,
      automatic_release: false,
      skip_app_version_update: false,
      skip_screenshots: true,
      force: true
    )
  end

  desc "Upload build to TestFlight"
  lane :beta_release do
    build
    upload_to_testflight()
  end

  desc "Build the app and set the build number"
  lane :build do
    set_build_number
    build_app
  end

  desc "Release the app, creating an IPA and delivering metadata"
  lane :release do
    build
    deliver_metadata
  end

  desc "Submit the build for review"
  lane :submit_review do
    deliver(
      build_number: latest_testflight_build_number(version: get_app_version, app_identifier: 'com.diogenes.ai.chatbot'),
      submit_for_review: true,
      automatic_release: true,
      force: true, # Skip HTML report verification
      skip_metadata: true,
      skip_screenshots: true,
      skip_binary_upload: true
    )
  end

end

