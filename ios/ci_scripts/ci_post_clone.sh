#!/bin/sh

# The default execution directory of this script is the ci_scripts directory.
cd $CI_WORKSPACE # change working directory to the root of your cloned repo.

# Install Flutter using git.
git clone https://github.com/flutter/flutter.git --depth 1 -b stable $HOME/flutter
export PATH="$PATH:$HOME/flutter/bin"

# Install Flutter artifacts for iOS (--ios), or macOS (--macos) platforms.
flutter precache --ios
cd $CI_PRIMARY_REPOSITORY_PATH/


curl -sL https://firebase.tools | bash

dart pub global activate flutterfire_cli

# Define the path where the .env.production file will be located
ENV_FILE=".env.production"

# Check if the .env.production file exists
if [ -f "$ENV_FILE" ]; then
    echo ".env.production file already exists. Appending/updating environment variables..."
else
    echo ".env.production file does not exist. Creating a new file..."
    touch $ENV_FILE
fi

# Add or update the environment variables in the .env.production file
# Define an array of environment variable keys
declare -a ENV_VARS=(
    "AWS_API_GATEWAY_API_KEY"
    "API_URL_PROD"
    "OPENAI_API_KEY"
    "GOOGLE_MAPS_API_KEY"
    "EMBEDDING_SERVER_HOST"
    "WEBSOCKET_HOST"
    "DIOGENES_WEB_URL"
    "REPLICATE_API_TOKEN"
    "GOOGLE_AI_API_KEY"
    "DEEPGRAM_API_KEY"
    "LIVEKIT_SERVER_URL"
    "GOOGLE_CLOUD_IOS_API_KEY"
    "STRIPE_PUBLISHABLE"
)

# Loop through the environment variables and update or append to the .env.production file
for VAR in "${ENV_VARS[@]}"; do
    if [ -z "${!VAR}" ]; then
        echo "Error: $VAR is not set. Please provide the environment variable before running the build."
        exit 1
    fi

    # Remove existing variable if it exists
    sed -i '' "/^$VAR=/d" $ENV_FILE
    
    # Append the new variable with its value
    echo "$VAR=${!VAR}" >> $ENV_FILE
done

# Sanity check: Expose and log the last 5 letters of each line in the .env.production file
echo "Performing sanity check on the environment variables file..."
while IFS= read -r line; do
    # Get the last 5 characters of each line (ignoring the variable name part)
    LAST_FIVE=${line: -5}
    echo "Last 5 characters of the line: $LAST_FIVE"
done < $ENV_FILE

# Log the completion of the script
echo ".env.production file has been updated with environment variables."

echo "Incrementing build number..."

# Check the OS

# MacOS commands
BUILD_NUMBER=$(cat build_number.txt)
NEW_BUILD_NUMBER=$((BUILD_NUMBER + 1))

echo $NEW_BUILD_NUMBER > build_number.txt
sed -i '' "s/^version: .*/version: 1.0.$NEW_BUILD_NUMBER+${NEW_BUILD_NUMBER}/" pubspec.yaml

# Set up Git configuration
git config --global user.email "<EMAIL>"
git config --global user.name "Simon Dai"
git add build_number.txt pubspec.yaml
git commit -m "Increment build number to $NEW_BUILD_NUMBER"

# Push changes back to the repository
git push origin HEAD:build_ios  # Adjust branch name as necessary

# Install CMake
echo "Installing CMake..."
brew install cmake

flutter clean
# Install Flutter dependencies.
flutter pub get

# for swagger, since swagger code generated code has issue, we add generated code and fix it then add to git
# flutter pub run build_runner build

# Install CocoaPods using Homebrew.
HOMEBREW_NO_AUTO_UPDATE=1 # disable homebrew's automatic updates.
brew install cocoapods

# Install CocoaPods dependencies.
cd ios && pod install # run `pod install` in the `ios` directory.



exit 0