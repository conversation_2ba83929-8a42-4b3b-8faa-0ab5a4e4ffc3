<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Diogenes AI ChatBot</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>diogenes.ai.chatbot</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>GIDClientID</key>
		<string>575471299837-citkpaf3ieqk7q8ucr44lg77006udskv.apps.googleusercontent.com</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string></string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.575471299837-citkpaf3ieqk7q8ucr44lg77006udskv</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>com.diogenes.ai.chatbot</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.diogenes.ai.chatbot</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>FirebaseAppDelegateProxyEnabled</key>
		<false/>
		<key>FirebaseDynamicLinksCustomDomains</key>
		<array>
			<string>https://diogenesaichatbot.web.app/link</string>
			<string>https://diogenesaichatbot.web.app</string>
		</array>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
		</dict>
		<key>NSAppleMusicUsageDescription</key>
		<string>Access music resources</string>
		<key>NSAudioUsageDescription</key>
		<string>Audio recording for voice recognition, audio/video chat with friends.</string>
		<key>NSCameraUsageDescription</key>
		<string>Use camera to take a photo/video for display/ video chat with friends</string>
		<key>NSDocumentsFolderUsageDescription</key>
		<string>Read and write files to for chatbot knowledge base</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Use location always(maps, location functionality to create itinerary)</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Use location when use the app(maps, location functionality to create itinerary)</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>Allow access to microphone for recording audio(speech recognition, audio/vido chat with friends)</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Read your photos for display/send to friends for chat</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>Read your photos for display, chat with freinds, upload to knowledge base, etc</string>
		<key>NSSpeechRecognitionUsageDescription</key>
		<string>Use speech recognition to recognize your speech and input</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>remote-notification</string>
			<string>audio</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen.storyboard</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIRequiresFullScreen</key>
		<true/>
		<key>UIFileSharingEnabled</key>
		<true/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>io.flutter.embedded_views_preview</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>FirebaseMessagingAutoInitEnabled</key>
		<false/>
		<key>LSMinimumSystemVersion</key>
		<string>13.0</string>
	</dict>
</plist>
