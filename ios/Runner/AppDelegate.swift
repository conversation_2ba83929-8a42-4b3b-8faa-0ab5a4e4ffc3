import UIKit
import Flutter
// import FlutterSecureStorage
import GoogleMaps

@main
@objc class AppDelegate: FlutterAppDelegate {

  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // let flutterStorage = FlutterSecureStorage()
    // let google_maps_api_key = flutterStorage.read("google_maps_api_key")
    // Get the Google Maps API key from environment variables
    GMSServices.provideAPIKey("AIzaSyCxdFiMpOn4cWwe2h7-PnLokYv5UNAybS8")

    GeneratedPluginRegistrant.register(with: self)
 

        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

}
