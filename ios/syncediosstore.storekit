{"appPolicies": {"eula": "", "policies": [{"locale": "en_US", "policyText": "", "policyURL": ""}]}, "identifier": "26EFFEBE", "nonRenewingSubscriptions": [], "products": [{"displayPrice": "20.0", "familyShareable": false, "internalID": "6738058541", "localizations": [{"description": "cost $20 for customer, worth 100 credits", "displayName": "100 credits", "locale": "en_US"}], "productID": "credits_100", "referenceName": "credits_100", "type": "Consumable"}], "settings": {"_applicationInternalID": "6446092554", "_developerTeamID": "Q6R596WK4T", "_failTransactionsEnabled": false, "_lastSynchronizedDate": 755403488.13006, "_locale": "en_US", "_storefront": "USA", "_storeKitErrors": [{"current": null, "enabled": false, "name": "Load Products"}, {"current": null, "enabled": false, "name": "Purchase"}, {"current": null, "enabled": false, "name": "Verification"}, {"current": null, "enabled": false, "name": "App Store Sync"}, {"current": null, "enabled": false, "name": "Subscription Status"}, {"current": null, "enabled": false, "name": "App Transaction"}, {"current": null, "enabled": false, "name": "Manage Subscriptions Sheet"}, {"current": null, "enabled": false, "name": "Refund Request Sheet"}, {"current": null, "enabled": false, "name": "Offer Code Redeem Sheet"}]}, "subscriptionGroups": [], "version": {"major": 4, "minor": 0}}