//
//  diogenes_ai_chatbotUITestsLaunchTests.swift
//  diogenes.ai.chatbotUITests
//
//  Created by <PERSON> on 3/11/23.
//

import XCTest

final class diogenes_ai_chatbotUITestsLaunchTests: XCTestCase {

    override class var runsForEachTargetApplicationUIConfiguration: Bool {
        true
    }

    override func setUpWithError() throws {
        
        continueAfterFailure = false

    }

    func testLaunch() throws {
        let app = XCUIApplication()
         setupSnapshot(app)
         app.launch()

        // Insert steps here to perform after app launch but before taking a screenshot,
        // such as logging into a test account or navigating somewhere in the app

        let attachment = XCTAttachment(screenshot: app.screenshot())
        attachment.name = "Launch Screen"
        attachment.lifetime = .keepAlways
        add(attachment)
    }
}
