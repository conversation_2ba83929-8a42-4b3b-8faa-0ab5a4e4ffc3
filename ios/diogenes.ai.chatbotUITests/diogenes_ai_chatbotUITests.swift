//
//  diogenes_ai_chatbotUITests.swift
//  diogenes.ai.chatbotUITests
//
//  Created by <PERSON> on 3/11/23.
//

import XCTest

final class diogenes_ai_chatbotUITests: XCTestCase {

    override func setUpWithError() throws {
        // Put setup code here. This method is called before the invocation of each test method in the class.

        // In UI tests it is usually best to stop immediately when a failure occurs.
        continueAfterFailure = false

        // In UI tests it’s important to set the initial state - such as interface orientation - required for your tests before they run. The setUp method is a good place to do this.

    }

    override func tearDownWithError() throws {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
    }

    func testAddScreenshot() throws {
        // UI tests must launch the application that they test.
        let app = XCUIApplication()
         setupSnapshot(app)
         app.launch()
        sleep(20)
        snapshot("01HomeScreen")
//    saveScreenshot(name: "01HomeScreen")
        // Use XCTAssert and related functions to verify your tests produce the correct results.
    }
    
    func testChatScreenshot() throws {
        // UI tests must launch the application that they test.
        let app = XCUIApplication()
         setupSnapshot(app)
         app.launch()
        
        let chatButton = app.buttons["Chat with AI bot"]
        XCTAssertTrue(chatButton.waitForExistence(timeout: 10))
        
        chatButton.tap()
        let enterYourMessageTextField = app.textFields["Enter your message"]
        XCTAssertTrue(enterYourMessageTextField.waitForExistence(timeout: 10))
        enterYourMessageTextField.tap()

        enterYourMessageTextField.typeText("Who is Einstein?\n")
//        Need to find the button to send message
//        sleep(20)
       //      app.buttons["Send"].tap()
        
//        wait for network to finish
        sleep(20)
        enterYourMessageTextField.tap()
        
        enterYourMessageTextField.typeText("Tell me more about his major academic achievement\n")
//        Need to find the button to send message
//        sleep(20)
        //      app.buttons["Send"].tap()
        
        //        wait for network to finish
        sleep(20)
        
        snapshot("02chat_page")

        // Check ChatHistory page
//        app.buttons["History"].tap()

//        sleep(5)
//        snapshot("03chat_history_page")
//        screenshot = XCUIScreen.main.screenshot()
//          attachment = XCTAttachment(screenshot: screenshot)
//          attachment.lifetime = .keepAlways
//          add(attachment)
//        let backButton = app.buttons["Back"]
//        backButton.tap()
//        backButton.tap()
                
                
//        // Find and click the specific button by id
//        let button = app.buttons["HomeScreenUsageChoiceButton_UsageType.chat"]
//        XCTAssertTrue(button.waitForExistence(timeout: 10))
//        button.tap()
//
//
//        // Find the text field by id and input text
//        let textField = app.textFields["QuestionInputTextField"]
//        XCTAssertTrue(textField.waitForExistence(timeout: 15))
//        textField.typeText("Who is Einstein?")
//
//        // Find and click the send button by id
//        let sendButton = app.buttons["SendInputQuestionButton"]
//        XCTAssertTrue(sendButton.waitForExistence(timeout: 25))
//        sendButton.tap()
//
//        // Wait for 20 seconds
//        sleep(20)
//
        // Take a screenshot
        

        
        // Use XCTAssert and related functions to verify your tests produce the correct results.
    }
    
    func testProgramScreenshot() throws {
        // UI tests must launch the application that they test.
        let app = XCUIApplication()
        setupSnapshot(app)
        app.launch()
        
        let programmingButton = app.buttons["Programming"]
        XCTAssertTrue(programmingButton.waitForExistence(timeout: 10))
        programmingButton.tap()
        let enterYourMessageTextField = app.textFields["Enter your message"]
        XCTAssertTrue(enterYourMessageTextField.waitForExistence(timeout: 10))
        enterYourMessageTextField.tap()
        
        enterYourMessageTextField.typeText("Write a short python code to show DFS:\n")
        //        Need to find the button to send message
//        sleep(20)
//        let sendButton = app.buttons["Send"]
//        XCTAssertTrue(sendButton.waitForExistence(timeout: 10))
//        sendButton.tap()
        
        //        wait for network to finish
        sleep(30)
        snapshot("03program_page")
        
        enterYourMessageTextField.tap()
        
        enterYourMessageTextField.typeText("Convert above code to golang\n")
        //        Need to find the button to send message
//        sleep(20)
        //      app.buttons["Send"].tap()
        
        //        wait for network to finish
        sleep(30)
        
        snapshot("04program_page")


    }

    func testGenerateImageScreenshot() throws {
        // UI tests must launch the application that they test.
        let app = XCUIApplication()
        setupSnapshot(app)
        app.launch()
        
        let generateImageButton = app.buttons["Image Generation"]
        XCTAssertTrue(generateImageButton.waitForExistence(timeout: 10))
        generateImageButton.tap()
        let enterYourMessageTextField = app.textFields["Enter your message"]
        XCTAssertTrue(enterYourMessageTextField.waitForExistence(timeout: 10))
        enterYourMessageTextField.tap()
        enterYourMessageTextField.tap()

        enterYourMessageTextField.typeText("Generate an oil painting of a cat fighting with a dog:\n")
        //        Need to find the button to send message
//        sleep(20)
        //      app.buttons["Send"].tap()

        //        wait for network to finish
        sleep(10)
        snapshot("05generateImage_page")

    }
    
    func testTranslatePage() throws {
        
        let app = XCUIApplication()
        setupSnapshot(app)
        app.launch()
        
        let translationButton = app.buttons["Translation"]
        XCTAssertTrue(translationButton.waitForExistence(timeout: 15))
        translationButton.tap()
        let enterYourMessageTextField = app.textFields["Enter text to translate"]
        XCTAssertTrue(enterYourMessageTextField.waitForExistence(timeout: 10))
        enterYourMessageTextField.tap()
        enterYourMessageTextField.tap()

        enterYourMessageTextField.typeText("Today is a good day!")
        let translateButton = app.buttons["Translate"]
        XCTAssertTrue(translateButton.waitForExistence(timeout: 15))
        translateButton.tap()
        //        Need to find the button to send message
//        sleep(20)
        //      app.buttons["Send"].tap()

        //        wait for network to finish
        sleep(10)
        snapshot("06Translate")
                
    }
    
    func saveScreenshot(name: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .medium, timeStyle: .short)
        let filename = "\(name)-\(timestamp).png"
        let screenshot = XCUIScreen.main.screenshot()
        let attachment = XCTAttachment(screenshot: screenshot)
        attachment.name = filename
        attachment.lifetime = .keepAlways
        add(attachment)
    }
    
    func testLaunchPerformance() throws {
        if #available(macOS 10.15, iOS 13.0, tvOS 13.0, watchOS 7.0, *) {
            // This measures how long it takes to launch your application.
            measure(metrics: [XCTApplicationLaunchMetric()]) {
                XCUIApplication().launch()
            }
        }
    }
}
