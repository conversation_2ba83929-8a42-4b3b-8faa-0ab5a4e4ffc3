
@router.post("/deduct-tokens")
async def deduct_tokens(
    req: TokenDeductionRequest,
    db: firestore.AsyncClient = Depends(get_db),
    decoded_token=Depends(verify_firebase_token),
):
    """
    Deduct tokens after actual API usage.
    Called by backend services after completing AI operations.
    """
    try:
        user_id: str = decoded_token["user_id"]
        
        # Validate request
        if req.actual_tokens <= 0:
            raise HTTPException(status_code=400, detail="Invalid token amount")
        
        # Get current user state
        user_doc_ref = db.collection("users").document(user_id)
        user_doc = await user_doc_ref.get()
        
        if not user_doc.exists:
            raise HTTPException(status_code=404, detail="User not found")
        
        data = user_doc.to_dict() or {}
        current_tokens = int(data.get("credits_remaining_tokens") or 0)
        is_subscribed = bool(data.get("is_subscribed") or False)
        
        # Apply policy B deduction rules
        ep = (req.endpoint or "").lower().strip()
        chat_eps = {"chat", "achat", "chatai", "chat_completions"}
        embed_eps = {"embeddings", "embedding", "embed", "document_embed"}
        
        tokens_to_deduct = 0
        
        if ep in chat_eps:
            # Chat endpoints: only deduct if not subscribed (pay-per-use fallback)
            if not is_subscribed:
                tokens_to_deduct = req.actual_tokens
        elif ep in embed_eps:
            # Embedding endpoints: always deduct tokens
            tokens_to_deduct = req.actual_tokens
        else:
            # Other endpoints: configurable (default: deduct)
            tokens_to_deduct = req.actual_tokens
        
        # Perform deduction
        if tokens_to_deduct > 0:
            new_balance = max(0, current_tokens - tokens_to_deduct)
            await user_doc_ref.set(
                {"credits_remaining_tokens": new_balance}, merge=True
            )
            
            # Log the deduction
            await db.collection("token_usage_log").add({
                "user_id": user_id,
                "endpoint": req.endpoint,
                "tokens_deducted": tokens_to_deduct,
                "tokens_before": current_tokens,
                "tokens_after": new_balance,
                "request_id": req.request_id,
                "timestamp": firestore.SERVER_TIMESTAMP,
            })
        
        return {
            "success": True,
            "tokens_deducted": tokens_to_deduct,
            "remaining_tokens": current_tokens - tokens_to_deduct,
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Token deduction error: {e}")


class TokenDeductionRequest(BaseModel):
    endpoint: str
    actual_tokens: int
    request_id: str | None = None
