rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /apiUsages/{document=**} {
      allow read, write: if request.auth != null;
    }
    
    match /reported_content/{document=**} {
      allow read,write: if request.auth != null;
    }
    
      // Rule for apiUsageRecord collection
    match /apiUsageRecord/{document=**} {
      allow read, write: if request.auth != null;
    }
    
      function isCloudFunction() {
      return request.auth.token.isCloudFunction == true;
    }
    // Helper functions
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }

    function isAdmin() {
      return request.auth != null && request.auth.token.admin == true;
    }
    match /progress/{userId}/sessions/{sessionId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
    }

    // Users
    match /users/{userId} {
      allow read: if true;
      allow write: if request.auth != null || isCloudFunction();
      
      match /followers/{followerId} {
        allow read: if true; 
        allow write: if request.auth != null;
      }
      
      match /followings/{followingId} {
        allow read: if true; 
        allow write: if request.auth != null;
      }
  // Match the contacts subcollection under each user
      match /contacts/{contactID} {
      
        allow read: if true; 
        // Allow users to add contact requests with a status of 'pending'
        allow create: if request.auth != null ;
        // Allow users to update contacts if they are authenticated
  			allow update: if request.auth != null;
   			allow delete: if request.auth != null;
      }
    }
    
		function isMember(chatRoomId) {
      return request.auth.uid in get(/databases/$(database)/documents/chatRooms/$(chatRoomId)).data.memberIds;
    }

    match /chatRooms/{chatRoomId} {
      // allow read: if request.auth.uid != null && isMember(chatRoomId);
      allow read: if request.auth.uid != null ;
      allow create: if request.auth.uid != null && request.resource.data.memberIds.hasAny([request.auth.uid]);
      allow update, delete: if request.auth.uid != null && isMember(chatRoomId);
      
        // Match the tokens collection
      match /tokens/{tokenId} {
        // Allow read and write access to the tokens collection
        allow read, write: if request.auth.uid != null ;
      }
    }
    match /messages/{messageId} {
      allow create: if request.auth.uid != null;
      allow read, update,delete: if request.auth.uid != null;
      // allow read, update: if request.auth.uid != null && isMember(request.resource.data.chatRoomId);
    }
    
    // Posts
    match /posts/{postId} {
      allow read: if true;
      allow write: if request.auth != null;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.authorId;
      allow delete: if request.auth != null && request.auth.uid == resource.data.authorId;
    
    }
    
        // Bots
    match /bots/{botId} {
      allow read: if true;
      allow write: if request.auth != null;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.ownerId;
      allow delete: if request.auth != null && request.auth.uid == resource.data.ownerId;
      
      // Knowledge subcollection
      match /knowledge/{knowledgeId} {
        allow read: if true;
        allow write: if request.auth != null && request.auth.uid == resource.data.ownerId;
      }
    }
    
    function isAuthenticated() {
      return request.auth.uid != null;
    }

    function isBotOwner(botId) {
      return isAuthenticated() && get(/databases/$(database)/documents/bots/$(botId)).data.ownerId == request.auth.uid;
    }

    // match /bots/{botId} {
    //   allow read, write: if isBotOwner(botId);
    // }

match /conversations/{conversationId} {
      allow read : if true; // TODO need to only allow specific people read or if public all people can read
      allow write: if isCloudFunction()  || isAuthenticated() && (resource == null || resource.data.userId == request.auth.uid || isBotOwner(resource.data.botId));
    }

    match /conversations/{conversationId}/messages/{messageId} {
      allow read : if true; // TODO need to only allow specific people read or if public all people can read
      allow write: if isCloudFunction() || isAuthenticated() && (isBotOwner(get(/databases/$(database)/documents/conversations/$(conversationId)).data.botId) || get(/databases/$(database)/documents/conversations/$(conversationId)).data.userId == request.auth.uid);
    }
    
    match /userFileSystemItems/{userId}/{document=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Usernames
    match /usernames/{username} {
      allow read: if true;
      allow write: if request.auth != null;
    }


    // Shared Data
    match /sharedData/{docId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }

    match /reviews/{reviewId} {
      allow read: if true;
      allow write: if request.auth.uid == request.resource.data.reviewerId;
    }
    
    match /comics/{userId} {
      
      // Users can only access their own comic sessions
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
      
      match /sessions/{sessionId} {
     
        // Users can only access their own comic sessions
        allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
        allow delete: if true;
      }
      
      match /predictions/{predictionId} {
     
        // Users can only access their own comic sessions
        allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
        allow delete: if true;
      } 
    }
    
     
			match /recipes/{userId}/userRecipes/{recipeId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
    }
    
    match /itinerary/{userId}/user_itineraries/{itineraryId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
    }
    
    match /audio/{userId}/user_audios/{audioId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
    }
    
    match /calls/{callId} {
      allow read: if true;
      allow write: if request.auth != null ||  isAdmin() || isCloudFunction();
      }
    
    
		match /_ext-firestore-semantic-search/{document=**} {
      // Deny read and write operations
      allow read, write: if false;
    }
    
        match /stories/{userId}/user_stories/{storyId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
    }
  
            match /customers/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
    }
  match /configuration/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
    }
  match /products/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
    }
    match /invoices/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
    }
    // New rule for accessing the user_invoices subcollection
    match /invoices/{userId}/user_invoices/{invoiceId} {
      // Allow read and write to invoices for the user or if the user is an admin/cloud function
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
    }

    // AI Tutor Feature Rules
    // These rules secure all AI tutor related collections and ensure proper user access control

    // Learning Plans - Users can only access their own learning plans
    match /learning_plans/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();

      // Subcollection for individual plans
      match /plans/{planId} {
        allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
      }
    }

    // Learning Progress - Users can only access their own progress data
    match /learning_progress/{progressId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // Learning Sessions - Users can only access their own learning sessions
    match /learning_sessions/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();

      // Subcollection for individual sessions
      match /sessions/{sessionId} {
        allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
      }
    }

    // Flashcards - Users can access their own flashcards and public ones
    match /flashcards/{flashcardId} {
      allow read: if request.auth != null &&
        (resource.data.userId == request.auth.uid || resource.data.isPublic == true);
      allow write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // Quizzes - Users can access their own quizzes and public ones
    match /quizzes/{quizId} {
      allow read: if request.auth != null &&
        (resource.data.userId == request.auth.uid || resource.data.isPublic == true);
      allow write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // Quiz Results - Users can only access their own quiz results
    match /quiz_results/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();

      // Subcollection for individual quiz results
      match /results/{resultId} {
        allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
      }
    }

    // Study Recommendations - Users can only access their own recommendations
    match /study_recommendations/{recommendationId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // Learning Analytics - Users can only access their own analytics
    match /learning_analytics/{analyticsId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // AI Tutor Settings - Users can only access their own settings
    match /ai_tutor_settings/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
    }

    // Learning Milestones - Users can only access their own milestones
    match /learning_milestones/{milestoneId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // Study Schedules - Users can only access their own schedules
    match /study_schedules/{scheduleId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // Learning Goals - Users can only access their own goals
    match /learning_goals/{goalId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // Concept Mastery - Users can only access their own concept mastery data
    match /concept_mastery/{masteryId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // Spaced Repetition Data - Users can only access their own spaced repetition data
    match /spaced_repetition/{repetitionId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // AI Tutor Conversations - Users can only access their own AI tutor conversations
    match /ai_tutor_conversations/{conversationId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // Learning Path Suggestions - Users can only access their own suggestions
    match /learning_path_suggestions/{suggestionId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // Performance Insights - Users can only access their own insights
    match /performance_insights/{insightId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // Adaptive Learning Data - Users can only access their own adaptive learning data
    match /adaptive_learning/{adaptiveId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // Curriculum Data - Read-only access for all authenticated users, write access for admins only
    match /curriculum/{subjectId} {
      allow read: if request.auth != null;
      allow write: if isAdmin() || isCloudFunction();
    }

    // AI Generated Content Cache - Users can read cached content, only system can write
    match /ai_content_cache/{cacheId} {
      allow read: if request.auth != null;
      allow write: if isAdmin() || isCloudFunction();
    }

    // Learning Analytics Cache - Users can only access their own analytics cache
    match /analytics_cache/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();

      // Subcollection for different analytics types
      match /insights/{insightId} {
        allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
      }

      match /recommendations/{recommendationId} {
        allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
      }
    }

    // User Preferences for AI Tutor - Users can only access their own preferences
    match /ai_tutor_preferences/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
    }

    // Learning Session Analytics - Users can only access their own session analytics
    match /session_analytics/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();

      // Subcollection for detailed analytics
      match /daily_stats/{dateId} {
        allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
      }

      match /weekly_stats/{weekId} {
        allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
      }

      match /monthly_stats/{monthId} {
        allow read, write: if isOwner(userId) || isAdmin() || isCloudFunction();
      }
    }
  }
}
