import Cocoa
import FlutterMacOS
import FirebaseCore
import FirebaseAppCheck

@main
class AppDelegate: FlutterAppDelegate {



  // Custom log function
  func log(_ message: String) {
    print("[MainWindow Debug] \(message)")
  }
    
    override func applicationDidFinishLaunching(_ notification: Notification) {
        // FirebaseApp.configure()  // Initialize Firebase here
        // Load API key from environment variable or dotenv
//                let apiKey: String
//                if let envApiKey = ProcessInfo.processInfo.environment["GOOGLE_CLOUD_IOS_API_KEY"] {
//                    apiKey = envApiKey
//                } else {
//                    log("API key not found!")
//                    return
//                }
//                
//                // Check if running in simulator
//                #if targetEnvironment(simulator)
//                GIDSignIn.sharedInstance.configureDebugProvider(withAPIKey: apiKey) { error in
//                    if let error = error {
//                        log("Error configuring `GIDSignIn` for App Check: \(error)")
//                    } else {
//                        log("App Check debug provider configured successfully.")
//                    }
//                }
//                log("Firebase App Check using debug provider.")
//                #else
//                // Configure App Check for production.
////                AppCheck.setAppCheckProviderFactory(AppCheckDeviceCheckProviderFactory())
//                log("Firebase App Check enabled in production mode.")
//                #endif
        
        }


    // This method should terminate the app after the last window is closed
    override func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        log("Application should terminate after last window closed.")
        return true
    }

    override func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }

    // This ensures that the app does not handle reopen events
    override func applicationShouldHandleReopen(_ sender: NSApplication, hasVisibleWindows flag: Bool) -> Bool {
        if !flag {
            // If there are no visible windows, terminate the app
            log("Application reopened with no visible windows, terminating.")
            NSApplication.shared.terminate(self)
        }
        return false
    }
}
