#!/usr/bin/env bash
# Integration-style test against the local emulators using GraphQL over HTTP.
# Verifies <PERSON>ser<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, GetMovieById, ListMovies.

set -euo pipefail

PROJECT_ID="diogenesaichatbot"
REGION="us-central1"
SERVICE_ID="diogenesaichatbot"
CONNECTOR_ID="default"
PORT="9399"
AUTH_PORT="9099"

ENDPOINT="http://localhost:${PORT}/v1/projects/${PROJECT_ID}/locations/${REGION}/services/${SERVICE_ID}:executeGraphql?connector=${CONNECTOR_ID}"
AUTH_SIGNUP_URL="http://localhost:${AUTH_PORT}/identitytoolkit.googleapis.com/v1/accounts:signUp?key=dummy"

jq_test() { command -v jq >/dev/null 2>&1; }

# GQL snippets
read -r -d '' UPSERT_USER <<'EOF'
mutation UpsertChatUser($displayName: String, $photoUrl: String) { chatUser_upsert(data: { id_expr: "auth.uid", displayName: $displayName, photoUrl: $photoUrl }) { id } }
EOF

read -r -d '' CREATE_SESSION <<'EOF'
mutation CreateSession($title: String) {
  chatSession_insert(data: { userId_expr: "auth.uid", title: $title }) { id }
}
EOF

read -r -d '' APPEND_MESSAGE <<'EOF'
mutation AppendMessage($sessionId: UUID!, $role: String!, $content: String!, $index: Int) {
  chatMessage_insert(data: { sessionId: $sessionId, userId_expr: "auth.uid", role: $role, content: $content, index: $index }) { id }
}
EOF

read -r -d '' LIST_SESSIONS <<'EOF'
query ListMySessions($limit: Int = 10, $offset: Int = 0) {
  chatUser(key: { id_expr: "auth.uid" }) {
    sessions: chatSessions_on_user(orderBy: [{createdAt: DESC}], limit: $limit, offset: $offset) { id title }
  }
}
EOF

# Build JSON bodies
mk_body() {
  local q="$1"; local vars="$2"; local op="$3"
  if jq_test; then jq -n --arg q "$q" --argjson v "$vars" --arg op "$op" '{query: $q, variables: $v, operationName: $op}';
  else python3 - <<PY
import json,sys
q = json.dumps(sys.argv[1])[1:-1]
print(json.dumps({"query": q, "variables": json.loads(sys.argv[2]), "operationName": sys.argv[3]}))
PY
  "$q" "$vars" "$op"; fi
}

# Emulators scoped run
firebase emulators:exec --only dataconnect,auth -- bash -lc '
  set -euo pipefail
  echo "[TEST] Getting anonymous idToken from Auth emulator..."
  TOKEN=$(curl -sS -X POST "'$AUTH_SIGNUP_URL'" -H "Content-Type: application/json" -d "{\"returnSecureToken\": true}" | (jq -r .idToken 2>/dev/null || python3 -c "import sys,json; print(json.load(sys.stdin).get('idToken',''))"))
  if [ -z "$TOKEN" ]; then echo "[TEST][ERROR] Failed to obtain idToken"; exit 1; fi
  echo "[TEST] token ok"

  echo "[TEST] UpsertChatUser..."
  VARS_USER="{\"displayName\": \"Test User\", \"photoUrl\": \"https://example.com/p.png\"}"
  BODY_USER=$(python3 - <<PY
import json
print(json.dumps({"query": "$(printf %s "$UPSERT_USER" | python3 -c 'import json,sys; print(json.dumps(sys.stdin.read())[1:-1])')", "variables": json.loads("$VARS_USER"), "operationName": "UpsertChatUser"}))
PY
)
  RES_USER=$(curl -sS -X POST "'$ENDPOINT'" -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN" -d "$BODY_USER")
  echo "$RES_USER"
  echo "$RES_USER" | grep -q "chatUser_upsert" || { echo "[TEST][ERROR] UpsertChatUser failed"; exit 1; }

  echo "[TEST] CreateSession..."
  VARS_SESSION="{\"title\": \"Test Session\"}"
  BODY_SESSION=$(python3 - <<PY
import json
print(json.dumps({"query": "$(printf %s "$CREATE_SESSION" | python3 -c 'import json,sys; print(json.dumps(sys.stdin.read())[1:-1])')", "variables": json.loads("$VARS_SESSION"), "operationName": "CreateSession"}))
PY
)
  RES_SESSION=$(curl -sS -X POST "'$ENDPOINT'" -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN" -d "$BODY_SESSION")
  echo "$RES_SESSION"
  SESSION_ID=$(echo "$RES_SESSION" | (jq -r .data.chatSession_insert.id 2>/dev/null || python3 -c "import sys,json; d=json.load(sys.stdin); print(((d.get('data') or {}).get('chatSession_insert') or {}).get('id',''))"))
  if [ -z "$SESSION_ID" ]; then echo "[TEST][ERROR] CreateSession failed"; exit 1; fi
  echo "[TEST] session id: $SESSION_ID"

  echo "[TEST] AppendMessage..."
  VARS_APPEND="{\"sessionId\": \"$SESSION_ID\", \"role\": \"user\", \"content\": \"Hello\", \"index\": 0}"
  BODY_APPEND=$(python3 - <<PY
import json
print(json.dumps({"query": "$(printf %s "$APPEND_MESSAGE" | python3 -c 'import json,sys; print(json.dumps(sys.stdin.read())[1:-1])')", "variables": json.loads("$VARS_APPEND"), "operationName": "AppendMessage"}))
PY
)
  RES_APPEND=$(curl -sS -X POST "'$ENDPOINT'" -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN" -d "$BODY_APPEND")
  echo "$RES_APPEND"
  echo "$RES_APPEND" | grep -q "chatMessage_insert" || { echo "[TEST][ERROR] AppendMessage failed"; exit 1; }

  echo "[TEST] ListMySessions..."
  VARS_LIST="{\"limit\": 5, \"offset\": 0}"
  BODY_LIST=$(python3 - <<PY
import json
print(json.dumps({"query": "$(printf %s "$LIST_SESSIONS" | python3 -c 'import json,sys; print(json.dumps(sys.stdin.read())[1:-1])')", "variables": json.loads("$VARS_LIST"), "operationName": "ListMySessions"}))
PY
)
  RES_LIST=$(curl -sS -X POST "'$ENDPOINT'" -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN" -d "$BODY_LIST")
  echo "$RES_LIST"
  echo "[TEST] Success"
'
