import 'package:diogeneschatbot/features/ai_tutor/domain/entities/study_recommendation.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('StudyRecommendation', () {
    test('toJson/fromJson roundtrip', () {
      final rec = StudyRecommendation(
        id: 'r1',
        title: 'Review Algebra',
        description: 'Practice linear equations',
        type: RecommendationType.reviewFlashcards,
        subject: 'Math',
        topic: 'Algebra',
        priority: 4,
        estimatedTime: const Duration(minutes: 45),
        metadata: {'level': 'intermediate'},
      );

      final json = rec.toJson();
      final parsed = StudyRecommendation.fromJson(json);

      expect(parsed, equals(rec));
    });

    test('fromJson handles missing/unknown type, non-int estimatedTime, and null metadata', () {
      final json = {
        'id': 'r2',
        'title': 'Watch video',
        'description': 'Khan Academy lesson',
        'type': 'unknown_type',
        'subject': 'Physics',
        'priority': 3,
        'estimatedTime': 30.9, // non-int
        // metadata is missing -> defaults to {}
      };

      final rec = StudyRecommendation.fromJson(json);
      expect(rec.type, RecommendationType.studyConcept); // fallback
      expect(rec.estimatedTime.inMinutes, 31); // 30.9 -> 31
      expect(rec.metadata, isEmpty);
    });

    test('equatable works for identical values', () {
      final a = StudyRecommendation(
        id: 'x',
        title: 'Take Quiz',
        description: 'Chapter 2',
        type: RecommendationType.takeQuiz,
        subject: 'Chemistry',
        priority: 5,
        estimatedTime: const Duration(minutes: 20),
        metadata: const {},
      );
      final b = a.copyWith();
      expect(a, equals(b));
    });
  });
}

