import 'dart:convert';

import 'package:diogeneschatbot/services/billing_service.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';

class _DioMock extends Dio {
  Response<dynamic> Function(RequestOptions options)? onSend;

  @override
  Future<Response<T>> request<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    Options? options,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final handler = onSend ?? (opts) => Response(requestOptions: opts, statusCode: 500, data: '');
    final resp = handler(RequestOptions(path: path, method: options?.method ?? 'GET', data: data));
    return Response<T>(requestOptions: resp.requestOptions, statusCode: resp.statusCode, data: resp.data as T);
  }
}

void main() {
  group('BillingService', () {
    test('getBillingPortalLink returns URL on success', () async {
      final dio = _DioMock()
        ..onSend = (opts) =>
            Response(requestOptions: opts, statusCode: 200, data: jsonEncode({'url': 'https://portal.example'}));
      final svc = BillingService(dio: dio);
      final url = await svc.getBillingPortalLink();
      expect(url, 'https://portal.example');
    });

    test('createCreditCheckoutSession returns URL on success', () async {
      final dio = _DioMock()
        ..onSend = (opts) =>
            Response(requestOptions: opts, statusCode: 200, data: jsonEncode({'url': 'https://checkout.example'}));
      final svc = BillingService(dio: dio);
      final url = await svc.createCreditCheckoutSession(packageId: 'credits_100k');
      expect(url, 'https://checkout.example');
    });

    test('listCreditPackages parses from {packages: [...]} shape', () async {
      final dio = _DioMock()
        ..onSend = (opts) => Response(
          requestOptions: opts,
          statusCode: 200,
          data: jsonEncode({
            'packages': [
              {'id': 'credits_100k', 'label': '100k tokens', 'price_display': '\$10', 'tokens': 100000},
              {'id': 'credits_1m', 'label': '1M tokens', 'price_display': '\$80', 'tokens': 1000000},
            ],
          }),
        );
      final svc = BillingService(dio: dio);
      final packages = await svc.listCreditPackages();
      expect(packages.length, 2);
      expect(packages.first.id, 'credits_100k');
      expect(packages.first.priceDisplay, '\$10');
      expect(packages[1].id, 'credits_1m');
    });

    test('listCreditPackages throws on unexpected shape', () async {
      final dio = _DioMock()
        ..onSend = (opts) => Response(requestOptions: opts, statusCode: 200, data: jsonEncode({'unexpected': true}));
      final svc = BillingService(dio: dio);
      await expectLater(svc.listCreditPackages(), throwsA(isA<Exception>()));
    });

    test('preflightCheck parses allowed and remainingTokens', () async {
      final dio = _DioMock()
        ..onSend = (opts) => Response(
          requestOptions: opts,
          statusCode: 200,
          data: jsonEncode({'allowed': true, 'remaining_tokens': 12345}),
        );
      final svc = BillingService(dio: dio);
      final result = await svc.preflightCheck(endpoint: 'chat', estimatedTokens: 1000);
      expect(result.allowed, isTrue);
      expect(result.remainingTokens, 12345);
    });

    test('preflightCheck fails closed and returns allowed=false on error', () async {
      final dio = _DioMock()..onSend = (opts) => Response(requestOptions: opts, statusCode: 500, data: 'server error');
      final svc = BillingService(dio: dio);
      final result = await svc.preflightCheck(endpoint: 'chat', estimatedTokens: 1000);
      expect(result.allowed, isFalse);
      expect(result.reason, isNotEmpty);
    });
  });
}
